# Stripe支付集成说明文档

## 概述

本文档详细说明了在ShareWharf电商平台中集成Stripe支付功能的实现方法。我们在商品详情页面(ProductDetail.vue)中添加了Stripe支付功能，使用户可以直接在商品详情页完成支付流程，包括支持3D Secure验证。

## 技术栈

- 前端: Vue 3 + Stripe.js
- 后端: Java + Stripe Java SDK
- UI组件: Element Plus + TailwindCSS

## Stripe密钥配置

项目使用以下Stripe密钥：

- **公钥(Publishable Key)**: `pk_test_51RSdhqE3OG0pdvgKig337kFcphVpzL3zk0dOjvrmMnjVAypWn8hQPbfX3BOVQKz5bViDVvmYTPGVqKpl9ZQ9HkV500Ue0KCbMY`

  - 用于前端初始化Stripe.js
  - 安全可以在前端代码中使用

- **私钥(Secret Key)**: `sk_test_51RSdhqE3OG0pdvgK14adGw3cOcaUGPurGS8I1uvFPHn91QMHqSV1JAnvuKcoR2WE3rTXCam7hvghRkHyIJXQCTLk00jfvPdd8X`
  - 仅用于后端API
  - **注意**: 私钥应存储在环境变量或配置文件中，不应硬编码或暴露在前端代码中

## 支付流程详解

### 前端支付流程

1. **初始化Stripe**

   - 使用公钥初始化Stripe实例
   - 创建卡片元素并挂载到DOM

2. **收集支付信息**

   - 收集用户邮箱地址
   - 通过Stripe卡片元素收集支付卡信息

3. **创建支付方法**

   - 调用`stripe.createPaymentMethod()`创建支付方法
   - 传递卡片信息和账单详情

4. **发送支付请求**

   - 向后端发送支付方法ID和订单信息
   - 等待后端创建PaymentIntent并返回结果

5. **处理支付结果**
   - 直接成功：显示成功信息，跳转到订单确认页
   - 需要验证：调用`confirmCardPayment`完成3D Secure验证
   - 支付失败：显示错误信息，提供重试选项

### 后端支付流程

1. **接收支付请求**

   - 验证请求数据完整性
   - 验证商品价格和金额

2. **创建PaymentIntent**

   - 使用Stripe API创建PaymentIntent
   - 设置金额、货币、支付方法等信息

3. **处理PaymentIntent结果**

   - 成功：创建订单记录，返回成功状态
   - 需要验证：返回clientSecret供前端完成验证
   - 失败：返回错误信息

4. **处理Webhook事件**
   - 监听支付成功、失败等事件
   - 更新订单状态和库存信息

## 支付状态与错误处理

### 支付状态说明

Stripe支付可能会返回以下状态：

1. **succeeded**：支付已成功完成
2. **requires_action**：需要额外操作（如3D Secure验证）
3. **requires_payment_method**：需要提供新的支付方法
4. **requires_confirmation**：需要确认支付
5. **canceled**：支付已取消
6. **processing**：支付正在处理中

### 常见错误类型及处理方法

#### 1. 卡片错误 (card_error)

| 错误代码             | 错误描述           | 处理方法                       |
| -------------------- | ------------------ | ------------------------------ |
| `card_declined`      | 卡片被拒绝         | 提示用户使用其他卡片或支付方式 |
| `insufficient_funds` | 卡片余额不足       | 提示用户使用其他卡片或支付方式 |
| `expired_card`       | 卡片已过期         | 提示用户更新卡片信息           |
| `incorrect_cvc`      | 安全码错误         | 提示用户重新输入正确的安全码   |
| `processing_error`   | 处理卡片时出现错误 | 提示用户稍后重试或使用其他卡片 |

#### 2. 验证错误 (validation_error)

- 卡号格式错误
- 过期日期无效
- 安全码格式错误

#### 3. API错误 (api_error)

- Stripe服务暂时不可用
- 请求超时

#### 4. 其他错误

- 重复支付
- 幂等性键冲突

### 在前端处理支付结果

为提供更好的用户体验，我们在前端实现了一个支付结果弹窗组件，用于显示各种支付结果：

1. **支付成功**：显示成功信息，提供查看订单按钮
2. **支付需要验证**：引导用户完成3D Secure验证
3. **支付失败**：显示具体错误原因，提供重试选项
4. **支付状态未知**：提示用户联系客服确认

所有错误消息都同时提供中英文说明，便于国际用户理解。

### 示例支付结果处理代码

```javascript
// 处理支付结果
if (response.data.status === 'succeeded') {
  // 支付成功
  showPaymentResult({
    status: 'success',
    title: '支付成功 (Payment Successful)',
    messageCn: '您的订单已成功支付',
    messageEn: 'Your payment has been successfully processed',
  })
} else if (response.data.status === 'requires_action') {
  // 需要3D Secure验证
  const { error } = await stripe.confirmCardPayment(response.data.client_secret)

  if (error) {
    // 验证失败
    showPaymentResult({
      status: 'error',
      title: '验证失败 (Verification Failed)',
      messageCn: '支付验证失败: ' + error.message,
      messageEn: 'Payment verification failed: ' + error.message,
    })
  } else {
    // 验证成功
    showPaymentResult({
      status: 'success',
      title: '验证成功 (Verification Successful)',
      messageCn: '验证成功，您的支付已完成',
      messageEn: 'Verification successful, your payment has been completed',
    })
  }
}
```

## 调试与故障排除

### 常见问题

1. **支付状态不一致**

   - **症状**：后端Stripe显示支付成功，但前端显示失败
   - **原因**：前端无法正确识别Stripe返回的成功状态
   - **解决方法**：增强前端对各种成功响应格式的识别，确保检查Stripe返回的原始PaymentIntent对象

2. **3D Secure验证失败**

   - **症状**：用户完成3D Secure验证后返回失败
   - **原因**：验证过程中断或超时
   - **解决方法**：确保正确处理验证回调，检查验证URL配置

3. **重复支付问题**
   - **症状**：用户被多次扣款
   - **原因**：缺少幂等性控制
   - **解决方法**：使用幂等性键(idempotencyKey)防止重复支付

### 调试方法

1. **检查浏览器控制台**

   - 查看网络请求和响应
   - 分析JavaScript错误

2. **查看Stripe Dashboard**

   - 检查支付记录和状态
   - 查看详细的错误日志

3. **使用Stripe CLI测试Webhook**

   ```bash
   stripe listen --forward-to http://localhost:8080/api/payments/webhook
   ```

4. **使用Stripe测试卡号**
   - 成功支付: 4242 4242 4242 4242
   - 需要验证: 4000 0025 0000 3155
   - 支付失败: 4000 0000 0000 9995
   - 余额不足: 4000 0000 0000 9995
   - 需要3D Secure验证: 4000 0027 6000 3184

## 安全最佳实践

1. **PCI DSS合规**

   - 使用Stripe.js避免直接处理信用卡信息
   - 确保所有支付页面使用HTTPS

2. **防止敏感信息泄露**

   - 私钥只在后端使用，永不暴露给前端
   - 使用环境变量管理密钥

3. **防止欺诈交易**

   - 在后端验证所有价格和订单信息
   - 实施交易监控和异常检测

4. **数据安全**
   - 最小化存储支付相关信息
   - 加密存储必要的支付数据

## Java后端实现

### 1. 添加Stripe依赖

在Maven项目的`pom.xml`中添加依赖:

```xml
<dependency>
  <groupId>com.stripe</groupId>
  <artifactId>stripe-java</artifactId>
  <version>22.0.0</version>
</dependency>
```

### 2. 基于Spark实现支付处理API

根据提供的示例代码，我们可以修改为处理PaymentIntent的API:

```java
package com.sharewharf.payment;

import java.nio.file.Paths;
import java.util.Map;
import java.util.HashMap;

import static spark.Spark.post;
import static spark.Spark.get;
import static spark.Spark.port;
import static spark.Spark.staticFiles;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import com.stripe.Stripe;
import com.stripe.model.PaymentIntent;
import com.stripe.param.PaymentIntentCreateParams;

public class PaymentServer {

  public static void main(String[] args) {
    port(8080);

    // 设置Stripe API密钥
    Stripe.apiKey = "sk_test_51RSdhqE3OG0pdvgK14adGw3cOcaUGPurGS8I1uvFPHn91QMHqSV1JAnvuKcoR2WE3rTXCam7hvghRkHyIJXQCTLk00jfvPdd8X";

    staticFiles.externalLocation(
        Paths.get("public").toAbsolutePath().toString());

    Gson gson = new Gson();

    // 处理创建PaymentIntent的请求
    post("/api/payments/create-payment-intent", (request, response) -> {
      response.type("application/json");

      try {
        // 解析请求数据
        JsonObject requestData = JsonParser.parseString(request.body()).getAsJsonObject();

        // 获取请求参数
        String paymentMethodId = requestData.get("paymentMethodId").getAsString();
        Long amount = requestData.get("amount").getAsLong();
        String currency = requestData.get("currency").getAsString();
        String description = requestData.get("description").getAsString();
        String email = requestData.get("email").getAsString();
        String idempotencyKey = requestData.get("idempotencyKey").getAsString();

        // 获取元数据
        JsonObject metadataJson = requestData.get("metadata").getAsJsonObject();
        Long productId = metadataJson.get("productId").getAsLong();
        Integer quantity = metadataJson.get("quantity").getAsInt();
        String attributes = metadataJson.get("attributes").getAsString();

        // 验证商品和金额
        // 注意：这里应该从数据库查询商品价格并验证金额
        // 简化版本中我们跳过这一步

        // 构建PaymentIntent参数
        PaymentIntentCreateParams.Builder paramsBuilder = PaymentIntentCreateParams.builder()
          .setAmount(amount)
          .setCurrency(currency)
          .setPaymentMethod(paymentMethodId)
          .setDescription(description)
          .setReceiptEmail(email)
          .setConfirm(true)
          .setReturnUrl("http://localhost:8080/order/confirmation");

        // 添加元数据
        Map<String, String> metadata = new HashMap<>();
        metadata.put("productId", productId.toString());
        metadata.put("quantity", quantity.toString());
        metadata.put("attributes", attributes);
        paramsBuilder.putAllMetadata(metadata);

        // 创建PaymentIntent
        Map<String, Object> options = new HashMap<>();
        options.put("idempotencyKey", idempotencyKey);

        PaymentIntent paymentIntent = PaymentIntent.create(paramsBuilder.build(), options);

        // 重要：直接返回整个PaymentIntent对象
        // 这样前端可以根据paymentIntent.status判断支付状态
        return paymentIntent;

      } catch (Exception e) {
        e.printStackTrace();

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("error", e.getMessage());

        response.status(400);
        return errorResponse;
      }
    }, gson::toJson);

    // 查询支付状态的API
    get("/api/payments/payment-status", (request, response) -> {
      String paymentIntentId = request.queryParams("payment_intent_id");

      PaymentIntent paymentIntent = PaymentIntent.retrieve(paymentIntentId);

      Map<String, Object> responseMap = new HashMap<>();
      responseMap.put("status", paymentIntent.getStatus());

      if (paymentIntent.getReceiptEmail() != null) {
        responseMap.put("customer_email", paymentIntent.getReceiptEmail());
      }

      return responseMap;
    }, gson::toJson);

    // 处理Webhook的API
    post("/api/payments/webhook", (request, response) -> {
      String payload = request.body();
      String sigHeader = request.headers("Stripe-Signature");
      String endpointSecret = "whsec_your_webhook_signing_secret"; // 从配置中获取

      try {
        // 验证Webhook签名
        com.stripe.model.Event event = com.stripe.net.Webhook.constructEvent(
          payload, sigHeader, endpointSecret
        );

        // 处理事件
        String eventType = event.getType();

        switch (eventType) {
          case "payment_intent.succeeded":
            // 处理支付成功事件
            System.out.println("支付成功: " + event.getData().getObject().toJson());
            // 这里应该更新订单状态、发送确认邮件等
            break;

          case "payment_intent.payment_failed":
            // 处理支付失败事件
            System.out.println("支付失败: " + event.getData().getObject().toJson());
            // 这里应该更新订单状态、通知用户等
            break;

          default:
            // 处理其他事件
            System.out.println("收到事件: " + eventType);
            break;
        }

        return Map.of("received", true);

      } catch (Exception e) {
        e.printStackTrace();
        response.status(400);
        return Map.of("error", e.getMessage());
      }
    }, gson::toJson);
  }
}
```

### 3. 使用Spring Boot实现支付处理API

如果你的项目使用Spring Boot，可以按以下方式实现:

```java
package com.sharewharf.controller;

import com.sharewharf.dto.PaymentRequest;
import com.sharewharf.dto.PaymentResponse;
import com.sharewharf.service.OrderService;

import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.PaymentIntent;
import com.stripe.param.PaymentIntentCreateParams;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/payments")
public class PaymentController {

    @Value("${stripe.api.key}")
    private String stripeApiKey;

    @PostConstruct
    public void init() {
        // 初始化Stripe API密钥
        Stripe.apiKey = stripeApiKey;
    }

    @PostMapping("/create-payment-intent")
    public ResponseEntity<?> createPaymentIntent(@RequestBody PaymentRequest request) {
        try {
            // 验证请求数据
            if (request.getPaymentMethodId() == null ||
                request.getAmount() == null ||
                request.getCurrency() == null ||
                request.getEmail() == null ||
                request.getMetadata() == null) {

                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "error", "请求数据不完整"
                ));
            }

            // 这里应该验证商品和金额
            // 简化版本中我们跳过这一步

            // 构建PaymentIntent参数
            PaymentIntentCreateParams.Builder paramsBuilder = PaymentIntentCreateParams.builder()
                .setAmount(request.getAmount())
                .setCurrency(request.getCurrency())
                .setPaymentMethod(request.getPaymentMethodId())
                .setDescription(request.getDescription())
                .setReceiptEmail(request.getEmail())
                .setConfirm(true)
                .setReturnUrl("http://localhost:8080/order/confirmation");

            // 添加元数据
            Map<String, String> metadata = new HashMap<>();
            metadata.put("productId", request.getMetadata().getProductId().toString());
            metadata.put("quantity", request.getMetadata().getQuantity().toString());
            metadata.put("attributes", request.getMetadata().getAttributes());
            paramsBuilder.putAllMetadata(metadata);

            // 创建PaymentIntent
            Map<String, Object> options = new HashMap<>();
            options.put("idempotencyKey", request.getIdempotencyKey());

            PaymentIntent paymentIntent = PaymentIntent.create(paramsBuilder.build(), options);

            // 根据PaymentIntent状态返回结果
            if ("succeeded".equals(paymentIntent.getStatus())) {
                // 支付成功
                // 这里应该创建订单记录

                return ResponseEntity.ok(PaymentResponse.builder()
                    .success(true)
                    .paymentIntentId(paymentIntent.getId())
                    .orderId("order_" + System.currentTimeMillis())
                    .build());

            } else if ("requires_action".equals(paymentIntent.getStatus())) {
                // 需要3D Secure验证
                return ResponseEntity.ok(PaymentResponse.builder()
                    .requiresAction(true)
                    .clientSecret(paymentIntent.getClientSecret())
                    .paymentIntentId(paymentIntent.getId())
                    .build());

            } else {
                throw new RuntimeException("支付状态异常: " + paymentIntent.getStatus());
            }

        } catch (StripeException e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "error", "服务器内部错误"
            ));
        }
    }

    @GetMapping("/payment-status")
    public ResponseEntity<?> getPaymentStatus(@RequestParam("payment_intent_id") String paymentIntentId) {
        try {
            PaymentIntent paymentIntent = PaymentIntent.retrieve(paymentIntentId);

            Map<String, Object> response = new HashMap<>();
            response.put("status", paymentIntent.getStatus());

            if (paymentIntent.getReceiptEmail() != null) {
                response.put("customer_email", paymentIntent.getReceiptEmail());
            }

            return ResponseEntity.ok(response);

        } catch (StripeException e) {
            return ResponseEntity.badRequest().body(Map.of(
                "error", e.getMessage()
            ));
        }
    }
}
```

## 前端响应处理

前端需要正确处理Stripe API返回的响应。下面是一个健壮的处理逻辑示例：

```javascript
const response = await request.post('/api/payments/create-payment-intent', paymentData)

// 检查是否直接返回了PaymentIntent对象
if (response.data && response.data.object === 'payment_intent') {
  const paymentIntent = response.data

  if (paymentIntent.status === 'succeeded') {
    // 支付成功
    showSuccessMessage()
    navigateToOrderConfirmation(paymentIntent.id)
  } else if (paymentIntent.status === 'requires_action') {
    // 需要3D Secure验证
    const { error, paymentIntent: confirmedIntent } = await stripe.confirmCardPayment(
      paymentIntent.client_secret,
    )

    if (error) {
      // 验证失败
      showErrorMessage(error.message)
    } else if (confirmedIntent.status === 'succeeded') {
      // 验证成功
      showSuccessMessage()
      navigateToOrderConfirmation(confirmedIntent.id)
    }
  } else {
    // 其他状态
    showErrorMessage(`支付未完成: ${paymentIntent.status}`)
  }
} else if (response.data.success) {
  // 自定义成功响应
  showSuccessMessage()
  navigateToOrderConfirmation(response.data.orderId)
} else {
  // 错误响应
  showErrorMessage(response.data.error || '支付处理失败')
}
```

## 结语

通过本文档的指导，你应该能够在Vue3应用中成功集成Stripe支付功能，并正确处理各种支付状态和错误情况。记住，支付集成需要认真处理各种边缘情况，确保用户体验流畅，同时保证交易安全和数据准确。

如果遇到问题，请参考Stripe官方文档或联系Stripe支持团队获取帮助。
