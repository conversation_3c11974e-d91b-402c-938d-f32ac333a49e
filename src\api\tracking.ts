import axios from 'axios'
import type { AxiosResponse } from 'axios'
import type { ApiResponse } from './types'

// 51Tracking API 基础URL
const TRACKING_API_BASE_URL = 'https://api.51Tracking.com/v4'

// 创建一个专用的axios实例用于51Tracking API
const trackingRequest = axios.create({
  baseURL: TRACKING_API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器，添加API Key
trackingRequest.interceptors.request.use(
  (config) => {
    // 从环境变量或配置中获取API Key
    const trackingApiKey = import.meta.env.VITE_TRACKING_API_KEY || ''

    if (trackingApiKey) {
      config.headers['Tracking-Api-Key'] = trackingApiKey
    } else {
      console.error('51Tracking API Key is not configured')
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 定义接口类型
export interface TrackingCreateDTO {
  tracking_number: string
  courier_code: string
  order_number?: string
  customer_name?: string
  title?: string
  logistics_channel?: string
  destination_code?: string
  tracking_ship_date?: string
  tracking_postal_code?: string
  tracking_destination_country?: string
  tracking_origin_country?: string
  lang?: string
}

export interface TrackingInfo {
  id: string
  tracking_number: string
  courier_code: string
  order_number?: string
  delivery_status: string
  archived: boolean
  updating: boolean
  destination_country?: string
  origin_country?: string
  signed_by?: string
  latest_event?: string
  latest_checkpoint_time?: string
  transit_time?: string
  origin_info?: {
    courier_code?: string
    trackinfo?: Array<{
      checkpoint_date: string
      checkpoint_delivery_status: string
      tracking_detail: string
      location: string
    }>
  }
  destination_info?: {
    courier_code?: string
    trackinfo?: Array<{
      checkpoint_date: string
      checkpoint_delivery_status: string
      tracking_detail: string
      location: string
    }>
  }
}

export interface TrackingCreateResponse {
  meta: {
    code: number
    message: string
  }
  data: {
    id: string
    tracking_number: string
    courier_code: string
  }
}

export interface TrackingGetResponse {
  meta: {
    code: number
    message: string
  }
  data: {
    success: TrackingInfo[]
    rejected: Array<{
      tracking_number: string
      rejectedCode: number
      rejectedMessage: string
    }>
  }
}

export interface TrackingUpdateResponse {
  meta: {
    code: number
    message: string
  }
  data: {
    id: string
    tracking_number: string
    courier_code: string
  }
}

// 51Tracking API 服务
export const trackingApi = {
  // 创建物流单号进行查询
  createTracking(data: TrackingCreateDTO): Promise<AxiosResponse<TrackingCreateResponse>> {
    return trackingRequest.post('/trackings/create', data)
  },

  // 获取物流轨迹信息
  getTracking(trackingNumbers: string): Promise<AxiosResponse<TrackingGetResponse>> {
    return trackingRequest.get(`/trackings/get?tracking_numbers=${trackingNumbers}`)
  },

  // 批量获取物流轨迹信息
  getBatchTracking(trackingNumbers: string[]): Promise<AxiosResponse<TrackingGetResponse>> {
    return trackingRequest.get(`/trackings/get?tracking_numbers=${trackingNumbers.join(',')}`)
  },

  // 更新物流单号信息
  updateTracking(
    id: string,
    data: Partial<TrackingCreateDTO>,
  ): Promise<AxiosResponse<TrackingUpdateResponse>> {
    return trackingRequest.put(`/trackings/update/${id}`, data)
  },

  // 删除物流单号
  deleteTracking(id: string): Promise<AxiosResponse<any>> {
    return trackingRequest.delete(`/trackings/delete/${id}`)
  },
}

// 将51Tracking API与本地系统集成的服务
export const trackingService = {
  // 创建物流追踪并关联到订单
  async createOrderTracking(
    orderId: number,
    trackingData: TrackingCreateDTO,
  ): Promise<ApiResponse<any>> {
    try {
      // 1. 调用51Tracking API创建追踪
      const trackingResponse = await trackingApi.createTracking(trackingData)

      if (trackingResponse.data.meta.code === 200) {
        const trackingInfo = trackingResponse.data.data

        // 2. 将追踪信息保存到本地系统
        // 这里应该调用本地API将追踪信息与订单关联
        // 例如: return request.post('/api/order-tracking', { orderId, trackingId: trackingInfo.id, ... })

        // 临时返回成功响应
        return {
          code: 1,
          msg: '物流追踪创建成功',
          data: trackingInfo,
        }
      } else {
        return {
          code: 0,
          msg: trackingResponse.data.meta.message || '物流追踪创建失败',
          data: null,
        }
      }
    } catch (error) {
      console.error('创建物流追踪失败:', error)
      return {
        code: 0,
        msg: '创建物流追踪失败，请稍后重试',
        data: null,
      }
    }
  },

  // 获取订单的物流追踪信息
  async getOrderTracking(
    orderId: number,
    trackingNumber: string,
  ): Promise<ApiResponse<TrackingInfo | null>> {
    try {
      const response = await trackingApi.getTracking(trackingNumber)

      if (response.data.meta.code === 200 && response.data.data.success.length > 0) {
        return {
          code: 1,
          msg: '获取物流信息成功',
          data: response.data.data.success[0],
        }
      } else {
        return {
          code: 0,
          msg: '未找到物流信息',
          data: null,
        }
      }
    } catch (error) {
      console.error('获取物流信息失败:', error)
      return {
        code: 0,
        msg: '获取物流信息失败，请稍后重试',
        data: null,
      }
    }
  },
}
