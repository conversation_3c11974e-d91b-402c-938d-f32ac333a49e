import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import Icons from '@/components/icons'
import 'element-plus/dist/index.css'
import 'tailwindcss/tailwind.css'
import './assets/main.css'
import { ElMessage } from 'element-plus'
import zhLocale from '@/locales/zh'
import enLocale from '@/locales/en'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { i18n } from './i18n' // 导入i18n实例

// 添加全局token检查
// setInterval(() => {
//   const tokenTimestamp = localStorage.getItem('tokenTimestamp')
//   if (tokenTimestamp) {
//     const elapsed = Date.now() - parseInt(tokenTimestamp)
//     if (elapsed >= 3 * 60 * 1000) {
//       // 3分钟
//       localStorage.removeItem('token')
//       localStorage.removeItem('user')
//       localStorage.removeItem('tokenTimestamp')

//       // 如果用户当前在需要登录的页面，则跳转到登录页
//       const currentRoute = router.currentRoute.value
//       if (currentRoute.meta.requiresAuth) {
//         ElMessage.warning('登录已过期，请重新登录')
//         router.push('/login')
//       }
//     }
//   }
// }, 1000)

const app = createApp(App)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err)
  console.error('Error info:', info)
}

app.use(router)
app.use(ElementPlus)
app.use(Icons)
app.use(i18n)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.mount('#app')
