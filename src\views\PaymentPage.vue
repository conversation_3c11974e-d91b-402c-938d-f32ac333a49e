<template>
  <div
    class="min-h-screen bg-gradient-to-br from-slate-50/80 via-blue-50/80 to-indigo-50/80 transition-all duration-700"
    :class="{ 'dark:from-slate-900 dark:via-slate-800/90 dark:to-indigo-950': isDarkMode }"
  >
    <!-- 新增地址表单弹窗 - 放在页面最上方 -->
    <Teleport to="body">
      <div
        v-if="showAddressForm"
        class="fixed inset-0 z-[100] flex items-center justify-center bg-black/50 backdrop-blur-sm"
        @click.self="showAddressForm = false"
      >
        <div
          class="bg-white dark:bg-slate-800 rounded-2xl shadow-2xl w-full max-w-lg mx-4 transform transition-all duration-300 animate-fadeIn overflow-y-auto max-h-[90vh]"
        >
          <div
            class="p-6 border-b border-slate-200 dark:border-slate-700 flex justify-between items-center"
          >
            <h3
              class="text-xl font-bold bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent dark:from-blue-400 dark:to-indigo-400"
            >
              {{ isEditingAddress ? t('address.edit') : t('address.addNew') }}
            </h3>
            <button
              @click="showAddressForm = false"
              class="p-2 rounded-full hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors duration-200"
            >
              <svg
                class="w-5 h-5 text-slate-500 dark:text-slate-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <div class="p-6">
            <div class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div class="col-span-2 sm:col-span-1">
                  <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    {{ t('address.name') }} *
                  </label>
                  <input
                    v-model="addressForm.name"
                    type="text"
                    class="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
                    :class="{ 'border-red-500 dark:border-red-400': addressErrors.name }"
                    :placeholder="t('address.name')"
                  />
                  <p v-if="addressErrors.name" class="mt-1 text-sm text-red-500 dark:text-red-400">
                    {{ addressErrors.name }}
                  </p>
                  <p v-else class="mt-1 text-xs text-slate-500 dark:text-slate-400">
                    请输入收件人全名
                  </p>
                </div>

                <div class="col-span-2 sm:col-span-1">
                  <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    {{ t('address.phone') }} *
                  </label>
                  <input
                    v-model="addressForm.phoneNumber"
                    type="text"
                    class="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
                    :class="{ 'border-red-500 dark:border-red-400': addressErrors.phoneNumber }"
                    :placeholder="'(*************'"
                  />
                  <p
                    v-if="addressErrors.phoneNumber"
                    class="mt-1 text-sm text-red-500 dark:text-red-400"
                  >
                    {{ addressErrors.phoneNumber }}
                  </p>
                  <p v-else class="mt-1 text-xs text-slate-500 dark:text-slate-400">
                    格式：(************* 或 ************
                  </p>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div class="col-span-2 sm:col-span-1">
                  <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    {{ t('address.state') }} *
                  </label>
                  <select
                    v-model="addressForm.state"
                    class="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
                    :class="{ 'border-red-500 dark:border-red-400': addressErrors.state }"
                  >
                    <option value="" disabled>请选择州</option>
                    <option v-for="state in usStates" :key="state.code" :value="state.name">
                      {{ state.code }} - {{ state.name }}
                    </option>
                  </select>
                  <p v-if="addressErrors.state" class="mt-1 text-sm text-red-500 dark:text-red-400">
                    {{ addressErrors.state }}
                  </p>
                  <p v-else class="mt-1 text-xs text-slate-500 dark:text-slate-400">请选择美国州</p>
                </div>

                <div class="col-span-2 sm:col-span-1">
                  <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    {{ t('address.city') }} *
                  </label>
                  <input
                    v-model="addressForm.city"
                    type="text"
                    class="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
                    :class="{ 'border-red-500 dark:border-red-400': addressErrors.city }"
                    :placeholder="t('address.city')"
                  />
                  <p v-if="addressErrors.city" class="mt-1 text-sm text-red-500 dark:text-red-400">
                    {{ addressErrors.city }}
                  </p>
                  <p v-else class="mt-1 text-xs text-slate-500 dark:text-slate-400">
                    请输入城市名称
                  </p>
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                  {{ t('address.street') }} *
                </label>
                <input
                  v-model="addressForm.street"
                  type="text"
                  class="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
                  :class="{ 'border-red-500 dark:border-red-400': addressErrors.street }"
                  placeholder="123 Main St"
                />
                <p v-if="addressErrors.street" class="mt-1 text-sm text-red-500 dark:text-red-400">
                  {{ addressErrors.street }}
                </p>
                <p v-else class="mt-1 text-xs text-slate-500 dark:text-slate-400">
                  请输入街道地址，如：123 Main St
                </p>
              </div>

              <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                  {{ t('address.detail') }} *
                </label>
                <input
                  v-model="addressForm.addressDetail"
                  type="text"
                  class="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
                  :class="{ 'border-red-500 dark:border-red-400': addressErrors.addressDetail }"
                  placeholder="Apt 4B, Floor 2, etc."
                />
                <p
                  v-if="addressErrors.addressDetail"
                  class="mt-1 text-sm text-red-500 dark:text-red-400"
                >
                  {{ addressErrors.addressDetail }}
                </p>
                <p v-else class="mt-1 text-xs text-slate-500 dark:text-slate-400">
                  请输入详细地址，如：公寓号、楼层等
                </p>
              </div>

              <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                  {{ t('address.zipCode') }} *
                </label>
                <input
                  v-model="addressForm.zipCode"
                  type="text"
                  class="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
                  :class="{ 'border-red-500 dark:border-red-400': addressErrors.zipCode }"
                  placeholder="12345 或 12345-6789"
                />
                <p v-if="addressErrors.zipCode" class="mt-1 text-sm text-red-500 dark:text-red-400">
                  {{ addressErrors.zipCode }}
                </p>
                <p v-else class="mt-1 text-xs text-slate-500 dark:text-slate-400">
                  请输入5位数字邮编，或5位数字-4位数字格式
                </p>
              </div>

              <div class="flex items-center">
                <input
                  id="isDefaultAddress"
                  v-model="isDefaultAddress"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label
                  for="isDefaultAddress"
                  class="ml-2 text-sm text-slate-700 dark:text-slate-300"
                >
                  {{ t('address.setDefault') }}
                </label>
              </div>
            </div>

            <div class="mt-6 flex justify-end space-x-4">
              <button
                @click="showAddressForm = false"
                class="px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors duration-200"
              >
                {{ t('address.cancel') }}
              </button>
              <button
                @click="saveAddress"
                class="px-4 py-2 rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-md hover:shadow-lg transition-all duration-200"
              >
                {{ t('address.save') }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </Teleport>

    <!-- 大的中央添加地址按钮 - 仅在没有地址且用户未选择地址时显示 -->
    <div
      v-if="showCentralAddressButton"
      class="fixed inset-0 z-40 flex items-center justify-center bg-black/20 backdrop-blur-sm"
    >
      <div
        class="bg-white dark:bg-slate-800 rounded-2xl shadow-2xl p-8 max-w-md mx-4 text-center transform transition-all duration-300 animate-fadeIn"
      >
        <svg
          class="w-20 h-20 mx-auto text-blue-500 dark:text-blue-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
          />
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
          />
        </svg>

        <h2 class="mt-6 text-2xl font-bold text-slate-900 dark:text-white">
          请在支付之前先填写收货地址
        </h2>
        <p class="mt-2 text-slate-600 dark:text-slate-400">{{ t('address.needAddress') }}</p>

        <button
          @click="openAddAddressForm"
          class="mt-8 px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 w-full flex items-center justify-center"
        >
          <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
          {{ t('address.addNew') }}
        </button>

        <!-- <button
          @click="showCentralAddressButton = false"
          class="mt-4 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-300 transition-colors duration-200"
        >
          {{ t('address.cancel') }}
        </button> -->
      </div>
    </div>

    <!-- Enhanced Navigation -->
    <nav
      class="sticky top-0 z-50 bg-white/90 backdrop-blur-xl border-b border-slate-200/60 shadow-md transition-all duration-300"
      :class="{ 'dark:bg-slate-900/90 dark:border-slate-700/60 shadow-lg': isDarkMode }"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-20">
          <div class="flex items-center space-x-6">
            <div class="flex items-center space-x-3">
              <div
                class="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg transform transition-transform hover:scale-105"
              >
                <svg
                  class="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                  />
                </svg>
              </div>
              <div>
                <h1
                  class="text-xl font-bold bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent"
                  :class="{ 'dark:from-blue-400 dark:to-indigo-400': isDarkMode }"
                >
                  SecurePay Pro
                </h1>
                <p class="text-xs text-slate-600 dark:text-slate-400">{{ t('nav.subtitle') }}</p>
              </div>
            </div>
          </div>

          <div class="flex items-center space-x-4">
            <!-- Language Toggle -->
            <div class="flex items-center bg-slate-100 dark:bg-slate-700 rounded-xl p-1 shadow-sm">
              <button
                @click="setLanguage('zh')"
                class="px-3 py-1.5 text-sm font-medium rounded-lg transition-all duration-200"
                :class="
                  currentLanguage === 'zh'
                    ? 'bg-white dark:bg-slate-600 text-slate-900 dark:text-white shadow-sm'
                    : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white'
                "
              >
                中文
              </button>
              <button
                @click="setLanguage('en')"
                class="px-3 py-1.5 text-sm font-medium rounded-lg transition-all duration-200"
                :class="
                  currentLanguage === 'en'
                    ? 'bg-white dark:bg-slate-600 text-slate-900 dark:text-white shadow-sm'
                    : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white'
                "
              >
                EN
              </button>
            </div>

            <!-- Security Badge -->
            <div
              class="hidden md:flex items-center space-x-2 px-3 py-1.5 bg-green-50 dark:bg-green-900/30 rounded-full shadow-sm"
            >
              <svg
                class="w-4 h-4 text-green-600 dark:text-green-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                />
              </svg>
              <span class="text-xs font-medium text-green-700 dark:text-green-300">{{
                t('nav.sslSecured')
              }}</span>
            </div>

            <!-- Theme Toggle -->
            <button
              @click="toggleDarkMode"
              class="p-3 rounded-xl transition-all duration-300 bg-slate-100 hover:bg-slate-200 text-slate-700 hover:scale-105 shadow-sm"
              :class="{
                'dark:bg-slate-700 dark:hover:bg-slate-600 dark:text-slate-300': isDarkMode,
              }"
            >
              <svg
                v-if="isDarkMode"
                class="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                />
              </svg>
              <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
      <!-- Enhanced Progress Steps with Animation -->
      <div class="mb-16">
        <div class="flex items-center justify-center max-w-3xl mx-auto">
          <div class="flex items-center w-full">
            <!-- Step 1 -->
            <div class="flex flex-col items-center flex-1">
              <div
                class="w-14 h-14 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center text-white shadow-lg relative transform transition-all duration-500 hover:scale-110"
              >
                <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                <div
                  class="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white dark:border-slate-800"
                ></div>
              </div>
              <span class="mt-3 text-sm font-semibold text-blue-700 dark:text-blue-400">{{
                t('steps.orderConfirmed')
              }}</span>
            </div>

            <!-- Progress Line -->
            <div
              class="flex-1 h-1.5 mx-4 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full shadow-sm"
            ></div>

            <!-- Step 2 -->
            <div class="flex flex-col items-center flex-1">
              <div
                class="w-14 h-14 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center text-white shadow-lg animate-pulse transform transition-all duration-500 hover:scale-110"
              >
                <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                  />
                </svg>
              </div>
              <span class="mt-3 text-sm font-semibold text-blue-700 dark:text-blue-400">{{
                t('steps.payment')
              }}</span>
            </div>

            <!-- Progress Line -->
            <div class="flex-1 h-1.5 mx-4 bg-slate-200 dark:bg-slate-700 rounded-full"></div>

            <!-- Step 3 -->
            <div class="flex flex-col items-center flex-1">
              <div
                class="w-14 h-14 bg-slate-200 dark:bg-slate-700 rounded-full flex items-center justify-center text-slate-500 dark:text-slate-400 transform transition-all duration-500 hover:scale-110"
              >
                <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <span class="mt-3 text-sm font-medium text-slate-500 dark:text-slate-400">{{
                t('steps.complete')
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content Container with Glass Effect -->
      <div class="grid lg:grid-cols-3 gap-8">
        <!-- 左侧内容区：地址选择和订单摘要 -->
        <div class="lg:col-span-1 h-fit space-y-6">
          <!-- 修改地址选择卡片部分 -->
          <div
            class="bg-white dark:bg-slate-800/90 rounded-2xl shadow-xl border border-slate-200 dark:border-slate-700 overflow-hidden backdrop-blur-sm transition-all duration-300 hover:shadow-2xl flex flex-col"
            style="height: 450px"
          >
            <!-- Header -->
            <div
              class="bg-gradient-to-r from-purple-600 to-violet-600 p-6 relative overflow-hidden flex-shrink-0"
            >
              <div class="flex items-center space-x-3 relative z-10">
                <div
                  class="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                </div>
                <h2 class="text-lg font-bold text-white">
                  {{ t('address.title') }}
                </h2>
              </div>

              <!-- Decorative Elements -->
              <div
                class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mr-16 -mt-16"
              ></div>
              <div
                class="absolute bottom-0 right-10 w-16 h-16 bg-white/10 rounded-full -mb-8"
              ></div>
            </div>

            <!-- 地址选择器 -->
            <div class="flex flex-col flex-grow overflow-hidden">
              <div class="p-6 pb-0 flex-shrink-0">
                <div class="flex justify-between items-center mb-4">
                  <h3 class="text-lg font-medium text-slate-800 dark:text-white">
                    {{ t('address.select') }}
                  </h3>
                </div>
              </div>

              <!-- 地址列表容器 - 设置固定高度和滚动 -->
              <div class="flex-grow overflow-hidden flex flex-col">
                <!-- 地址为空时显示大的添加地址按钮 -->
                <div
                  v-if="!hasAddresses"
                  class="flex flex-col items-center justify-center py-10 flex-grow"
                >
                  <div class="text-center mb-6">
                    <svg
                      class="w-16 h-16 mx-auto text-slate-400 dark:text-slate-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                    <p class="mt-4 text-lg text-slate-600 dark:text-slate-400">
                      {{ t('address.empty') }}
                    </p>
                    <p class="mt-2 text-sm text-slate-500 dark:text-slate-500">
                      {{ t('address.needAddress') }}
                    </p>
                  </div>

                  <button
                    @click="openAddAddressForm"
                    class="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center"
                  >
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                    {{ t('address.addNew') }}
                  </button>
                </div>

                <!-- 有地址时显示地址选择器和小的添加地址按钮 -->
                <div v-else class="flex flex-col flex-grow overflow-hidden">
                  <div class="px-6 flex-shrink-0">
                    <div class="flex justify-end mb-4">
                      <button
                        @click="openAddAddressForm"
                        class="inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-lg bg-blue-50 text-blue-700 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-800/50 transition-colors duration-200"
                      >
                        <svg
                          class="w-4 h-4 mr-1.5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                          />
                        </svg>
                        {{ t('address.addNew') }}
                      </button>
                    </div>
                  </div>

                  <!-- 可滚动的地址列表 -->
                  <div class="flex-grow overflow-y-auto px-6 pb-6">
                    <div v-if="addresses && addresses.length > 0" class="space-y-3">
                      <div
                        v-for="address in [...addresses].reverse()"
                        :key="address.id"
                        class="p-4 rounded-xl border transition-all duration-300"
                        :class="[
                          selectedAddressId === address.id
                            ? 'border-blue-500 bg-blue-50/50 dark:bg-blue-900/20 shadow-md'
                            : 'border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-700',
                        ]"
                        @click="selectAddress(address)"
                      >
                        <div class="flex justify-between">
                          <div class="flex items-center space-x-2">
                            <span class="font-medium text-slate-800 dark:text-white">{{
                              address.name
                            }}</span>
                            <span class="text-slate-600 dark:text-slate-400">{{
                              address.phoneNumber
                            }}</span>
                            <span
                              v-if="address.Default === 1"
                              class="px-2 py-0.5 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 rounded-full"
                            >
                              {{ t('address.default') }}
                            </span>
                          </div>
                          <div class="flex flex-col items-end space-y-1">
                            <!-- 修改为垂直布局 -->
                            <button
                              @click.stop="openEditAddressForm(address)"
                              class="p-1 text-slate-500 hover:text-blue-600 dark:text-slate-400 dark:hover:text-blue-400"
                            >
                              <svg
                                class="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="2"
                                  d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                                />
                              </svg>
                            </button>
                            <button
                              @click.stop="deleteAddress(address.id)"
                              class="p-1 text-slate-500 hover:text-red-600 dark:text-slate-400 dark:hover:text-red-400"
                            >
                              <svg
                                class="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="2"
                                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div class="mt-2 text-sm text-slate-600 dark:text-slate-300">
                          {{ formatAddress(address) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Order Summary Card -->
          <div
            class="bg-white dark:bg-slate-800/90 rounded-2xl shadow-xl border border-slate-200 dark:border-slate-700 overflow-hidden backdrop-blur-sm transition-all duration-300 hover:shadow-2xl"
          >
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-indigo-600 p-6 relative overflow-hidden">
              <div class="flex items-center space-x-3 relative z-10">
                <div
                  class="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                    />
                  </svg>
                </div>
                <h2 class="text-lg font-bold text-white">
                  {{ t('order.summary') }}
                </h2>
              </div>

              <!-- Decorative Elements -->
              <div
                class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mr-16 -mt-16"
              ></div>
              <div
                class="absolute bottom-0 right-10 w-16 h-16 bg-white/10 rounded-full -mb-8"
              ></div>
            </div>

            <!-- Product Info -->
            <div class="p-6">
              <div
                class="flex items-center space-x-4 mb-6 transform transition-transform hover:scale-102"
              >
                <div
                  class="relative w-24 h-24 rounded-xl overflow-hidden bg-slate-100 dark:bg-slate-700 shadow-md"
                >
                  <img
                    :src="productData.image || '/placeholder.svg'"
                    :alt="product.name"
                    class="w-full h-full object-contain transition-transform duration-700 hover:scale-110"
                    @error="handleImageError"
                  />
                  <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                </div>
                <div class="flex-1 min-w-0">
                  <h3 class="font-semibold text-slate-900 dark:text-white truncate text-lg">
                    {{ product.name }}
                  </h3>
                  <div class="mt-2 flex items-center">
                    <span
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                    >
                      {{ t('order.quantity') }}: {{ quantity }}
                    </span>

                    <span
                      v-if="selectAttrList && selectAttrList.length > 0"
                      class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-violet-100 text-violet-800 dark:bg-violet-900 dark:text-violet-300"
                    >
                      {{ selectAttrList.length }} {{ t('order.attributes') }}
                    </span>
                  </div>

                  <div
                    v-if="selectAttrList && selectAttrList.length > 0"
                    class="mt-2 text-sm text-slate-700 dark:text-slate-300"
                  >
                    <div class="flex flex-wrap gap-1">
                      <span
                        v-for="(attr, index) in selectAttrList"
                        :key="index"
                        class="inline-flex items-center px-2 py-1 rounded-md text-xs bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300"
                      >
                        {{ attr.key }}: {{ attr.value }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Price Breakdown -->
              <div class="space-y-3 pt-4 border-t border-slate-200 dark:border-slate-600">
                <div
                  class="flex justify-between text-sm items-center py-2 hover:bg-slate-50 dark:hover:bg-slate-700/50 px-2 rounded-lg transition-colors duration-200"
                >
                  <span class="text-slate-900 dark:text-slate-300 font-medium flex items-center">
                    <svg
                      class="w-4 h-4 mr-1.5 text-slate-500 dark:text-slate-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
                      />
                    </svg>
                    {{ t('order.subtotal') }}
                  </span>
                  <span class="font-medium text-slate-900 dark:text-white"
                    >${{ formatPrice(baseAmount) }}</span
                  >
                </div>
                <div
                  class="flex justify-between text-sm items-center py-2 hover:bg-slate-50 dark:hover:bg-slate-700/50 px-2 rounded-lg transition-colors duration-200"
                >
                  <span class="text-slate-900 dark:text-slate-300 font-medium flex items-center">
                    <svg
                      class="w-4 h-4 mr-1.5 text-slate-500 dark:text-slate-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                      />
                    </svg>
                    {{ t('order.serviceFee') }}
                    {{
                      paymentMethod === 'stripe' ? '(4% Service charge)' : '(2% WeChat service fee)'
                    }}
                  </span>
                  <span class="font-medium text-slate-900 dark:text-white"
                    >${{ formatPrice(serviceFee) }}</span
                  >
                </div>
                <div
                  class="flex justify-between text-sm items-center py-2 hover:bg-slate-50 dark:hover:bg-slate-700/50 px-2 rounded-lg transition-colors duration-200"
                >
                  <span class="text-slate-900 dark:text-slate-300 font-medium flex items-center">
                    <svg
                      class="w-4 h-4 mr-1.5 text-slate-500 dark:text-slate-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                    {{ t('order.processingFee') }}
                    {{
                      paymentMethod === 'stripe'
                        ? `(${stripeServiceFee}$ Stripe service charge)`
                        : null
                    }}
                  </span>
                  <span class="font-medium text-green-700 dark:text-green-400">
                    {{ paymentMethod === 'stripe' ? '$' + stripeServiceFee : t('order.free') }}
                  </span>
                </div>
              </div>

              <!-- Total Amount -->
              <div
                class="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-100 dark:border-blue-800/30 flex justify-between items-center"
              >
                <span class="font-medium text-slate-900 dark:text-slate-300">{{
                  t('order.total')
                }}</span>
                <div class="text-right">
                  <span class="text-xl font-bold text-blue-700 dark:text-blue-400"
                    >${{ formatPrice(totalAmount) }}</span
                  >
                  <div class="text-xs text-slate-600 dark:text-slate-400 mt-0.5">
                    ≈ ¥{{ formatCnyPrice(totalAmount) }}
                  </div>
                </div>
              </div>

              <!-- Calculation Formula -->
              <div class="mt-2 text-xs text-slate-500 dark:text-slate-400 text-right">
                <span v-if="paymentMethod === 'stripe'">
                  {{
                    `( $${formatPrice(totalAmount)} = $${formatPrice(baseAmount)} + 4% * $${formatPrice(baseAmount)} + $0.3 )`
                  }}
                </span>
                <span v-else-if="paymentMethod === 'wechat'">
                  {{
                    `( $${formatPrice(totalAmount)} = $${formatPrice(baseAmount)} + 2% * $${formatPrice(baseAmount)} )`
                  }}
                </span>
              </div>

              <!-- Security Features -->
              <div
                class="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-xl border border-green-200 dark:border-green-800/30 transform transition-all duration-300 hover:shadow-md"
              >
                <div class="flex items-center space-x-2 mb-2">
                  <svg
                    class="w-5 h-5 text-green-600 dark:text-green-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                    />
                  </svg>
                  <span class="text-sm font-semibold text-green-800 dark:text-green-300">{{
                    t('security.title')
                  }}</span>
                </div>
                <ul class="text-xs text-green-700 dark:text-green-400 space-y-1 pl-5">
                  <li class="flex items-center">
                    <svg
                      class="w-3 h-3 mr-1.5 text-green-600 dark:text-green-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    {{ t('security.ssl') }}
                  </li>
                  <li class="flex items-center">
                    <svg
                      class="w-3 h-3 mr-1.5 text-green-600 dark:text-green-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    {{ t('security.pci') }}
                  </li>
                  <li class="flex items-center">
                    <svg
                      class="w-3 h-3 mr-1.5 text-green-600 dark:text-green-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    {{ t('security.fraud') }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- Payment Methods Card -->
        <div class="lg:col-span-2">
          <div
            class="bg-white/90 dark:bg-slate-800/90 backdrop-blur-xl rounded-2xl shadow-xl border border-slate-200/60 dark:border-slate-700/60 overflow-hidden transition-all duration-300 hover:shadow-2xl"
          >
            <!-- Header -->
            <div
              class="bg-gradient-to-r from-indigo-600 to-blue-600 p-6 border-b border-slate-200/60 dark:border-slate-600/60 relative overflow-hidden"
            >
              <div class="flex items-center justify-between relative z-10">
                <div class="flex items-center space-x-3">
                  <div
                    class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg"
                  >
                    <svg
                      class="w-6 h-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h2 class="text-xl font-bold text-white">
                      {{ t('payment.method') }}
                    </h2>
                    <p class="text-sm text-white/80">
                      {{ t('payment.chooseMethod') }}
                    </p>
                  </div>
                </div>

                <!-- Payment Timer -->
                <div class="text-right">
                  <div class="text-sm font-medium text-white/80">
                    {{ t('payment.sessionExpires') }}
                  </div>
                  <div class="text-lg font-bold text-white flex items-center justify-end">
                    <svg
                      class="w-5 h-5 mr-1.5 animate-pulse"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    {{ sessionTimer }}
                  </div>
                </div>
              </div>

              <!-- Decorative Elements -->
              <div
                class="absolute top-0 left-0 w-40 h-40 bg-white/10 rounded-full -ml-20 -mt-20"
              ></div>
              <div
                class="absolute bottom-0 right-0 w-24 h-24 bg-white/10 rounded-full -mr-10 -mb-10"
              ></div>
            </div>

            <div class="p-6">
              <!-- Payment Method Selection -->
              <div class="grid md:grid-cols-3 gap-4 mb-8">
                <!-- Credit Card -->
                <button
                  @click="changePaymentMethod('stripe')"
                  class="group relative p-6 rounded-xl border-2 transition-all duration-300 hover:scale-105 hover:shadow-lg"
                  :class="{
                    'border-blue-500 bg-blue-50/70 dark:bg-blue-900/30 shadow-lg shadow-blue-500/20':
                      paymentMethod === 'stripe',
                    'border-slate-200 dark:border-slate-600 hover:border-blue-300 dark:hover:border-blue-500 bg-white dark:bg-slate-800/80':
                      paymentMethod !== 'stripe',
                  }"
                >
                  <div class="text-center">
                    <div
                      class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center text-white shadow-lg transform transition-all duration-300 group-hover:scale-110"
                    >
                      <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                        />
                      </svg>
                    </div>
                    <h3 class="font-semibold text-slate-800 dark:text-white text-lg">
                      {{ t('payment.creditCard') }}
                    </h3>
                    <p class="text-sm text-slate-600 dark:text-slate-400 mt-1">
                      {{ t('payment.cardTypes') }}
                    </p>
                  </div>
                  <div
                    v-if="paymentMethod === 'stripe'"
                    class="absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center shadow-md"
                  >
                    <svg
                      class="w-5 h-5 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                </button>

                <!-- WeChat Pay -->
                <button
                  @click="changePaymentMethod('wechat')"
                  class="group relative p-6 rounded-xl border-2 transition-all duration-300 hover:scale-105 hover:shadow-lg"
                  :class="{
                    'border-green-500 bg-green-50/70 dark:bg-green-900/30 shadow-lg shadow-green-500/20':
                      paymentMethod === 'wechat',
                    'border-slate-200 dark:border-slate-600 hover:border-green-300 dark:hover:border-green-500 bg-white dark:bg-slate-800/80':
                      paymentMethod !== 'wechat',
                  }"
                >
                  <div class="text-center">
                    <div
                      class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center text-white shadow-lg transform transition-all duration-300 group-hover:scale-110"
                    >
                      <svg class="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                        <path
                          d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.162 4.203 2.969 5.531L2.5 17.312l2.844-1.406c.781.156 1.594.25 2.344.25.313 0 .625-.032.938-.063-.188-.625-.313-1.281-.313-1.968 0-4.094 3.5-7.406 7.813-7.406.406 0 .813.031 1.219.094C16.5 4.219 13.031 2.188 8.691 2.188zm-2.5 3.281c.688 0 1.25.563 1.25 1.25s-.563 1.25-1.25 1.25-1.25-.563-1.25-1.25.563-1.25 1.25-1.25zm5 0c.688 0 1.25.563 1.25 1.25s-.563 1.25-1.25 1.25-.563-.563-.563-1.25.563-1.25 1.25-1.25z"
                        />
                      </svg>
                    </div>
                    <h3 class="font-semibold text-slate-800 dark:text-white text-lg">
                      {{ t('payment.wechatPay') }}
                    </h3>
                    <p class="text-sm text-slate-600 dark:text-slate-400 mt-1">微信支付</p>
                  </div>
                  <div
                    v-if="paymentMethod === 'wechat'"
                    class="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center shadow-md"
                  >
                    <svg
                      class="w-5 h-5 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                </button>

                <!-- Alipay -->
                <button
                  @click="paymentMethod = 'alipay'"
                  class="group relative p-6 rounded-xl border-2 transition-all duration-300 hover:scale-105 hover:shadow-lg"
                  :class="{
                    'border-blue-400 bg-blue-50/70 dark:bg-blue-900/30 shadow-lg shadow-blue-400/20':
                      paymentMethod === 'alipay',
                    'border-slate-200 dark:border-slate-600 hover:border-blue-300 dark:hover:border-blue-400 bg-white dark:bg-slate-800/80':
                      paymentMethod !== 'alipay',
                  }"
                >
                  <div class="text-center">
                    <div
                      class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-400 to-blue-500 rounded-xl flex items-center justify-center text-white shadow-lg transform transition-all duration-300 group-hover:scale-110"
                    >
                      <svg class="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                        <path
                          d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                        />
                      </svg>
                    </div>
                    <h3 class="font-semibold text-slate-800 dark:text-white text-lg">
                      {{ t('payment.alipay') }}
                    </h3>
                    <p class="text-sm text-slate-600 dark:text-slate-400 mt-1">支付宝</p>
                  </div>
                  <div
                    v-if="paymentMethod === 'alipay'"
                    class="absolute -top-2 -right-2 w-8 h-8 bg-blue-400 rounded-full flex items-center justify-center shadow-md"
                  >
                    <svg
                      class="w-5 h-5 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                </button>
              </div>

              <!-- Payment Forms -->
              <div class="min-h-[400px]">
                <!-- Credit Card Form -->
                <div v-if="paymentMethod === 'stripe'" class="space-y-6 animate-fadeIn">
                  <div class="grid md:grid-cols-2 gap-6">
                    <div class="space-y-2">
                      <label
                        class="block text-sm font-semibold text-slate-700 dark:text-slate-300 flex items-center"
                      >
                        <svg
                          class="w-4 h-4 mr-1.5 text-blue-600 dark:text-blue-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                          />
                        </svg>
                        {{ t('payment.emailAddress') }} *
                      </label>
                      <div class="relative">
                        <input
                          type="email"
                          v-model="paymentEmail"
                          class="w-full px-4 py-3 pl-10 border border-slate-300 dark:border-slate-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white transition-all duration-200"
                          placeholder="<EMAIL>"
                        />
                        <div
                          class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                        >
                          <svg
                            class="w-5 h-5 text-slate-400 dark:text-slate-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
                            />
                          </svg>
                        </div>
                      </div>
                      <p v-if="emailError" class="text-sm text-red-500 flex items-center mt-1">
                        <svg
                          class="w-4 h-4 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                          />
                        </svg>
                        {{ emailError }}
                      </p>
                    </div>

                    <div class="space-y-2">
                      <label
                        class="block text-sm font-semibold text-slate-700 dark:text-slate-300 flex items-center"
                      >
                        <svg
                          class="w-4 h-4 mr-1.5 text-blue-600 dark:text-blue-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                          />
                        </svg>
                        {{ t('payment.cardholderName') }} *
                      </label>
                      <div class="relative">
                        <input
                          type="text"
                          v-model="paymentName"
                          class="w-full px-4 py-3 pl-10 border border-slate-300 dark:border-slate-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white transition-all duration-200"
                          placeholder="John Doe"
                        />
                        <div
                          class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                        >
                          <svg
                            class="w-5 h-5 text-slate-400 dark:text-slate-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="space-y-2">
                    <label
                      class="block text-sm font-semibold text-slate-700 dark:text-slate-300 flex items-center"
                    >
                      <svg
                        class="w-4 h-4 mr-1.5 text-blue-600 dark:text-blue-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                        />
                      </svg>
                      {{ t('payment.cardInformation') }} *
                    </label>
                    <div class="relative">
                      <div
                        id="card-element"
                        class="p-4 pl-10 border border-slate-300 dark:border-slate-600 rounded-xl bg-white dark:bg-slate-700 min-h-[60px] transition-all duration-200 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent"
                      ></div>
                      <div
                        class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                      >
                        <svg
                          class="w-5 h-5 text-slate-400 dark:text-slate-500"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                          />
                        </svg>
                      </div>
                    </div>
                    <p v-if="cardError" class="text-sm text-red-500 flex items-center mt-1">
                      <svg
                        class="w-4 h-4 mr-1"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                        />
                      </svg>
                      {{ cardError }}
                    </p>
                  </div>

                  <div
                    class="flex items-center space-x-3 p-4 bg-slate-50 dark:bg-slate-800/50 rounded-xl border border-slate-200 dark:border-slate-700"
                  >
                    <input
                      type="checkbox"
                      id="save-card"
                      class="w-5 h-5 text-blue-600 rounded focus:ring-blue-500 transition-all duration-200 cursor-pointer"
                    />
                    <label
                      for="save-card"
                      class="text-sm text-slate-700 dark:text-slate-300 cursor-pointer select-none"
                    >
                      {{ t('payment.saveCard') }}
                    </label>
                  </div>

                  <button
                    @click="handlePayment"
                    :disabled="isProcessingPayment"
                    class="w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 transform hover:scale-[1.02] active:scale-[0.98]"
                  >
                    <span
                      v-if="isProcessingPayment"
                      class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"
                    ></span>
                    <svg
                      v-else
                      class="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                      />
                    </svg>
                    <span class="text-white">{{
                      isProcessingPayment
                        ? t('payment.processing')
                        : `${t('payment.pay')} $${formatPrice(totalAmount)}`
                    }}</span>
                  </button>
                </div>

                <!-- WeChat Pay -->
                <div v-if="paymentMethod === 'wechat'" class="text-center space-y-6 animate-fadeIn">
                  <div v-if="qrCodeGenerated && !isExpired" class="space-y-4">
                    <div class="relative">
                      <div
                        class="inline-block p-6 bg-white dark:bg-slate-700 rounded-2xl shadow-lg border border-slate-100 dark:border-slate-600 transform transition-all duration-500 hover:shadow-xl"
                      >
                        <img
                          v-if="wechatPayQrCode"
                          :src="wechatPayQrCode"
                          alt="WeChat Pay QR Code"
                          class="w-64 h-64 mx-auto rounded-xl transform transition-all duration-500"
                          :class="{ 'blur-sm opacity-50': hasScanned }"
                        />

                        <!-- QR Code Scanning Animation -->
                        <div
                          v-if="!hasScanned"
                          class="absolute inset-0 flex items-center justify-center pointer-events-none"
                        >
                          <div class="w-full h-1 bg-green-500/50 animate-qrScan absolute"></div>
                        </div>

                        <!-- Scanned Overlay -->
                        <div
                          v-if="hasScanned && !isExpired && orderStatus !== 'EXPIRED'"
                          class="absolute inset-0 flex items-center justify-center bg-black/10 backdrop-blur-sm rounded-2xl"
                        >
                          <div
                            class="bg-white dark:bg-slate-800 p-4 rounded-xl shadow-lg text-center"
                          >
                            <div v-if="isPaying" class="flex flex-col items-center">
                              <svg
                                class="animate-spin h-10 w-10 text-green-500 mb-2"
                                fill="none"
                                viewBox="0 0 24 24"
                              >
                                <circle
                                  class="opacity-25"
                                  cx="12"
                                  cy="12"
                                  r="10"
                                  stroke="currentColor"
                                  stroke-width="4"
                                ></circle>
                                <path
                                  class="opacity-75"
                                  fill="currentColor"
                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                              </svg>
                              <span class="font-medium text-green-700 dark:text-green-400">{{
                                t('payment.processing')
                              }}</span>
                            </div>
                            <div v-else class="flex flex-col items-center">
                              <svg
                                class="h-10 w-10 text-green-500 mb-2"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="2"
                                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                              </svg>
                              <span class="font-medium text-green-700 dark:text-green-400">{{
                                t('payment.qrScanned')
                              }}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Order Number Badge -->
                      <div
                        class="absolute -bottom-3 left-1/2 transform -translate-x-1/2 bg-blue-50 dark:bg-blue-900/30 px-4 py-1.5 rounded-full border border-blue-100 dark:border-blue-800/30 shadow-sm"
                      >
                        <div class="flex items-center space-x-1.5">
                          <svg
                            class="w-4 h-4 text-blue-600 dark:text-blue-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"
                            />
                          </svg>
                          <span class="text-xs font-medium text-blue-700 dark:text-blue-300">{{
                            currentOrderNumber
                          }}</span>
                        </div>
                      </div>
                    </div>

                    <div class="space-y-4">
                      <div>
                        <h3 class="text-xl font-bold text-slate-900 dark:text-white mb-2">
                          {{ t('payment.scanQR') }}
                        </h3>
                        <p class="text-slate-700 dark:text-slate-300 max-w-md mx-auto">
                          {{ t('payment.wechatInstruction') }}
                        </p>
                      </div>

                      <!-- Payment Amount Card -->
                      <div
                        class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-4 rounded-xl border border-green-100 dark:border-green-800/30 max-w-sm mx-auto"
                      >
                        <div class="flex justify-between items-center mb-2">
                          <span class="text-sm font-medium text-slate-700 dark:text-slate-300">{{
                            t('order.total')
                          }}</span>
                          <span class="text-lg font-bold text-green-700 dark:text-green-400"
                            >${{ formatPrice(paymentAmount) }}</span
                          >
                        </div>
                        <div class="flex justify-between items-center">
                          <span class="text-xs text-slate-600 dark:text-slate-400">{{
                            t('payment.cnyAmount')
                          }}</span>
                          <span class="text-sm font-medium text-slate-700 dark:text-slate-300"
                            >¥{{ formatCnyPrice(paymentAmount) }}</span
                          >
                        </div>
                      </div>

                      <!-- Countdown Timer -->
                      <div class="flex items-center justify-center space-x-2 mt-4">
                        <div
                          class="p-2 rounded-lg bg-orange-50 dark:bg-orange-900/20 border border-orange-100 dark:border-orange-800/30 flex items-center space-x-2"
                          :class="{ 'animate-pulse': countdownTime < 60 }"
                        >
                          <svg
                            class="w-5 h-5 text-orange-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                          <span class="text-sm font-medium text-orange-700 dark:text-orange-400">
                            {{ t('payment.expiresIn') }} {{ countdownDisplay }}
                          </span>
                        </div>
                      </div>

                      <!-- Status Message -->
                      <div
                        v-if="hasScanned && isPaying"
                        class="mt-4 p-3 bg-green-50 dark:bg-green-900/30 rounded-xl border border-green-100 dark:border-green-800/30 max-w-sm mx-auto"
                      >
                        <div class="flex items-center space-x-2">
                          <svg
                            class="w-5 h-5 text-green-600 dark:text-green-400 animate-pulse"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M13 10V3L4 14h7v7l9-11h-7z"
                            />
                          </svg>
                          <p class="text-sm text-green-700 dark:text-green-400 font-medium">
                            {{ orderStatusDesc || t('payment.processingPayment') }}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Expired QR Code -->
                  <div v-else-if="isExpired || orderStatus === 'EXPIRED'" class="space-y-6 py-8">
                    <div
                      class="bg-orange-50 dark:bg-orange-900/20 p-8 rounded-2xl border border-orange-100 dark:border-orange-800/30 max-w-md mx-auto"
                    >
                      <div class="flex flex-col items-center">
                        <svg
                          class="w-16 h-16 text-orange-500 mb-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        <h3 class="text-xl font-bold text-slate-900 dark:text-white mb-2">
                          {{ t('payment.qrExpired') }}
                        </h3>
                        <p class="text-slate-700 dark:text-slate-300 mb-6 text-center">
                          {{ t('payment.qrExpiredDesc') }}
                        </p>
                        <button
                          @click="initiateWechatPayment"
                          class="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 transform hover:scale-105 active:scale-95"
                        >
                          <svg
                            class="w-5 h-5"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                            />
                          </svg>
                          <span>{{ t('payment.generateNew') }}</span>
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- Loading State -->
                  <div v-else class="space-y-6 py-12">
                    <div class="flex flex-col items-center">
                      <div
                        class="w-16 h-16 border-4 border-green-200 border-t-green-600 rounded-full animate-spin mb-6"
                      ></div>
                      <p class="text-lg font-medium text-slate-700 dark:text-slate-300">
                        {{ t('payment.generatingQR') }}
                      </p>
                    </div>
                  </div>
                </div>

                <!-- Alipay -->
                <div v-if="paymentMethod === 'alipay'" class="text-center py-12">
                  <div class="space-y-6">
                    <div
                      class="w-20 h-20 bg-gradient-to-br from-orange-400 to-orange-500 rounded-2xl flex items-center justify-center mx-auto shadow-lg"
                    >
                      <svg class="w-10 h-10 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path
                          d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"
                        />
                      </svg>
                    </div>

                    <div>
                      <h3 class="text-xl font-semibold text-slate-800 dark:text-white mb-2">
                        暂不支持该支付方式
                      </h3>
                      <p class="text-slate-600 dark:text-slate-400 mb-6">
                        支付宝支付功能正在开发中，请选择其他支付方式
                      </p>
                    </div>

                    <button
                      @click="paymentMethod = 'wechat'"
                      class="px-8 py-4 bg-gradient-to-r from-green-400 to-green-500 hover:from-green-500 hover:to-green-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      使用微信支付
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Payment Progress Modal -->
    <div
      v-if="paymentInProgress"
      class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50"
    >
      <div class="bg-white dark:bg-slate-800 rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
        <div class="text-center space-y-6">
          <div
            class="w-20 h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mx-auto"
          >
            <svg
              class="w-10 h-10 text-white animate-pulse"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
          </div>

          <div>
            <h3 class="text-xl font-bold text-slate-800 dark:text-white mb-2">
              {{ t('payment.processingPayment') }}
            </h3>
            <p class="text-slate-600 dark:text-slate-400">{{ paymentProgressMessage }}</p>
          </div>

          <div class="space-y-2">
            <div class="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-3 overflow-hidden">
              <div
                class="h-full bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full transition-all duration-300 relative overflow-hidden"
                :style="{ width: paymentProgressPercent + '%' }"
              >
                <div
                  class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse"
                ></div>
              </div>
            </div>
            <p class="text-sm text-slate-600 dark:text-slate-400">
              {{ paymentProgressPercent }}% {{ t('payment.complete') }}
            </p>
          </div>

          <p class="text-xs text-slate-400 dark:text-slate-500">
            {{ t('payment.doNotClose') }}
          </p>
        </div>
      </div>
    </div>

    <!-- Payment Result Modal -->
    <div
      v-if="paymentResultVisible"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-md transition-all duration-500"
      @click.self="closePaymentResult"
    >
      <div
        class="bg-white dark:bg-slate-800/95 rounded-3xl shadow-2xl p-8 max-w-md w-full mx-4 transform transition-all duration-700 animate-zoomIn border border-slate-100 dark:border-slate-700/50"
      >
        <div class="text-center">
          <!-- Success Icon with Animation -->
          <div v-if="paymentResult.status === 'success'" class="relative mx-auto mb-8">
            <div
              class="w-28 h-28 bg-gradient-to-br from-green-100 to-emerald-50 dark:from-green-900/40 dark:to-emerald-900/20 rounded-full flex items-center justify-center mx-auto shadow-lg shadow-green-500/10"
            >
              <svg
                class="w-14 h-14 text-green-600 dark:text-green-400 animate-bounce-subtle"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <div
              class="absolute inset-0 rounded-full border-4 border-green-500 opacity-0 animate-ping-slow"
            ></div>
            <div
              class="absolute inset-0 rounded-full border-4 border-green-400 opacity-0 animate-ping-slow animation-delay-300"
            ></div>

            <!-- Decorative elements -->
            <div
              class="absolute -top-4 -right-4 w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full"
            ></div>
            <div
              class="absolute -bottom-2 -left-6 w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full"
            ></div>
            <div
              class="absolute top-6 -right-8 w-6 h-6 bg-yellow-100 dark:bg-yellow-900/30 rounded-full"
            ></div>
          </div>

          <!-- Error Icon with enhanced styling -->
          <div v-else-if="paymentResult.status === 'error'" class="relative mx-auto mb-8">
            <div
              class="w-28 h-28 bg-gradient-to-br from-red-100 to-rose-50 dark:from-red-900/40 dark:to-rose-900/20 rounded-full flex items-center justify-center mx-auto shadow-lg shadow-red-500/10"
            >
              <svg
                class="w-14 h-14 text-red-600 dark:text-red-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </div>
            <div
              class="absolute inset-0 rounded-full border-4 border-red-500/30 opacity-0 animate-pulse"
            ></div>

            <!-- Decorative elements -->
            <div
              class="absolute -top-2 -left-4 w-8 h-8 bg-red-100/50 dark:bg-red-900/20 rounded-full"
            ></div>
            <div
              class="absolute -bottom-4 -right-2 w-10 h-10 bg-orange-100/50 dark:bg-orange-900/20 rounded-full"
            ></div>
          </div>

          <!-- Pending Icon with enhanced styling -->
          <div v-else class="relative mx-auto mb-8">
            <div
              class="w-28 h-28 bg-gradient-to-br from-orange-100 to-amber-50 dark:from-orange-900/40 dark:to-amber-900/20 rounded-full flex items-center justify-center mx-auto shadow-lg shadow-orange-500/10"
            >
              <svg
                class="w-14 h-14 text-orange-600 dark:text-orange-400 animate-spin-slow"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>

            <!-- Decorative elements -->
            <div
              class="absolute -top-3 -right-3 w-8 h-8 bg-yellow-100/50 dark:bg-yellow-900/20 rounded-full"
            ></div>
            <div
              class="absolute -bottom-3 -left-3 w-10 h-10 bg-amber-100/50 dark:bg-amber-900/20 rounded-full"
            ></div>
          </div>

          <h2
            class="text-3xl font-bold mb-3 bg-gradient-to-r"
            :class="{
              'from-green-600 to-emerald-600 dark:from-green-400 dark:to-emerald-400 bg-clip-text text-transparent':
                paymentResult.status === 'success',
              'from-red-600 to-rose-600 dark:from-red-400 dark:to-rose-400 bg-clip-text text-transparent':
                paymentResult.status === 'error',
              'from-orange-600 to-amber-600 dark:from-orange-400 dark:to-amber-400 bg-clip-text text-transparent':
                paymentResult.status === 'pending',
            }"
          >
            {{ paymentResult.title }}
          </h2>

          <p class="text-slate-700 dark:text-slate-300 mb-2 text-lg">
            {{ paymentResult.messageCn }}
          </p>
          <p class="text-slate-500 dark:text-slate-400 text-sm italic mb-6">
            {{ paymentResult.messageEn }}
          </p>

          <div
            v-if="paymentResult.status === 'success' && paymentResult.orderId"
            class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/10 p-5 rounded-2xl border border-green-100 dark:border-green-800/30 mb-8 transform transition-all duration-500 hover:shadow-md"
          >
            <div class="flex items-center justify-between">
              <span
                class="text-sm font-medium text-slate-700 dark:text-slate-300 flex items-center"
              >
                <svg
                  class="w-4 h-4 mr-2 text-green-500 dark:text-green-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
                {{ t('payment.orderNumber') }}
              </span>
              <span
                class="font-mono font-medium text-green-700 dark:text-green-400 text-lg tracking-wider"
              >
                {{ paymentResult.orderId || '--' }}
              </span>
            </div>
          </div>

          <div class="flex flex-col sm:flex-row gap-4 mt-4">
            <button
              v-if="paymentResult.status === 'success'"
              @click="viewOrder"
              class="px-6 py-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex-1 flex items-center justify-center space-x-2 transform hover:scale-105 active:scale-95"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
              <span>{{ t('payment.viewOrder') }}</span>
            </button>

            <button
              v-if="paymentResult.status === 'error'"
              @click="retryPayment"
              class="px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex-1 flex items-center justify-center space-x-2 transform hover:scale-105 active:scale-95"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              <span>{{ t('payment.retry') }}</span>
            </button>

            <button
              @click="closePaymentResult"
              class="px-6 py-4 bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 font-medium rounded-xl transition-all duration-300 flex-1 flex items-center justify-center space-x-2 transform hover:scale-105 active:scale-95"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
              <span>{{
                paymentResult.status === 'success' ? t('payment.continue') : t('payment.close')
              }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单构建加载动画 -->
    <Teleport to="body">
      <div
        v-if="isCreatingOrder"
        class="fixed inset-0 z-[9999] flex items-center justify-center bg-black/60 backdrop-blur-md"
      >
        <div
          class="bg-white dark:bg-slate-800 rounded-3xl shadow-2xl p-8 mx-4 max-w-md w-full transform transition-all duration-500 animate-fadeIn"
        >
          <!-- 主要加载动画 -->
          <div class="text-center">
            <!-- 3D立体加载器 -->
            <div class="relative mb-8">
              <div class="order-building-loader">
                <div class="cube-container">
                  <div class="cube cube-1"></div>
                  <div class="cube cube-2"></div>
                  <div class="cube cube-3"></div>
                  <div class="cube cube-4"></div>
                  <div class="cube cube-5"></div>
                  <div class="cube cube-6"></div>
                </div>
                <div class="building-ring">
                  <div class="ring-segment ring-1"></div>
                  <div class="ring-segment ring-2"></div>
                  <div class="ring-segment ring-3"></div>
                  <div class="ring-segment ring-4"></div>
                </div>
              </div>

              <!-- 粒子效果 -->
              <div class="particles-container">
                <div
                  v-for="i in 12"
                  :key="i"
                  class="particle"
                  :style="{ '--delay': i * 0.1 + 's' }"
                ></div>
              </div>
            </div>

            <!-- 文字内容 -->
            <div class="space-y-4">
              <h3
                class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent dark:from-blue-400 dark:to-purple-400"
              >
                正在构建您的订单
              </h3>
              <p class="text-slate-600 dark:text-slate-300 text-lg">
                支付成功！正在为您生成订单详情...
              </p>

              <!-- 进度指示器 -->
              <div class="mt-6">
                <div
                  class="flex items-center justify-center space-x-2 text-sm text-slate-500 dark:text-slate-400"
                >
                  <div class="step-indicator active">
                    <div class="step-dot"></div>
                    <span>支付确认</span>
                  </div>
                  <div class="step-line"></div>
                  <div class="step-indicator processing">
                    <div class="step-dot"></div>
                    <span>订单构建</span>
                  </div>
                  <div class="step-line"></div>
                  <div class="step-indicator">
                    <div class="step-dot"></div>
                    <span>完成</span>
                  </div>
                </div>
              </div>

              <!-- 温馨提示 -->
              <div
                class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800"
              >
                <div class="flex items-center space-x-2 text-blue-700 dark:text-blue-300">
                  <svg
                    class="w-5 h-5 animate-pulse"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span class="text-sm font-medium">请稍候，正在处理您的订单信息</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { loadStripe } from '@stripe/stripe-js'
import type { Stripe, StripeCardElement, StripeError } from '@stripe/stripe-js'
import QRCode from 'qrcode'
import {
  CheckCircle as CheckCircleIcon,
  XCircle as XCircleIcon,
  AlertCircle as AlertCircleIcon,
  RefreshCw as RefreshCwIcon,
} from 'lucide-vue-next'
import type { Component } from 'vue'
import request from '@/utils/request'
import AddressSelector from '../components/AddressSelector.vue'
import { type USAddress } from '@/api/address'
import { addressApi } from '@/api/address'
import { ElMessage } from 'element-plus'

// 定义通用响应类型
interface ApiResponse<T = any> {
  code: number
  data: T
  msg?: string
}

// Types
interface ProductAttribute {
  key: string
  value: string
}

enum PayMethod {
  CREDIT_CARD = 1,
  WECHAT = 2,
  ALIPAY = 3,
}

interface OrderSubmitDTO {
  addressId: number
  payMethod: PayMethod
  shippingMethodId: number
  orderRemark: string
  amount: number
  paymentTransactionId: string
  payTimeStamp: number
  orderItems: Array<{
    productId: number
    quantity: number
    unitPrice: number
  }>
}

// Language system
const currentLanguage = ref(localStorage.getItem('language') || 'zh')
const hasAddAddress = ref(false)
const translations = {
  zh: {
    nav: {
      subtitle: '企业级支付网关',
      sslSecured: 'SSL 安全认证',
    },
    steps: {
      orderConfirmed: '订单确认',
      payment: '安全支付',
      complete: '完成',
    },
    order: {
      summary: '订单摘要',
      quantity: '数量',
      attributes: '规格',
      subtotal: '商品金额',
      serviceFee: '服务费',
      processingFee: '处理费',
      free: '免费',
      total: '总计',
    },
    address: {
      title: '收货地址',
      select: '请选择收货地址',
      add: '添加新地址',
      empty: '暂无收货地址',
      needAddress: '请添加一个收货地址以继续',
      addNew: '添加新地址',
      edit: '编辑地址',
      delete: '删除地址',
      deleteConfirm: '确定要删除此地址吗？',
      cancel: '取消',
      confirm: '确定',
      save: '保存地址',
      name: '收件人姓名',
      phone: '联系电话',
      state: '省/州',
      city: '城市',
      street: '街道',
      detail: '详细地址',
      zipCode: '邮政编码',
      setDefault: '设为默认地址',
      loading: '加载中...',
      addSuccess: '地址添加成功',
      updateSuccess: '地址更新成功',
      deleteSuccess: '地址删除成功',
      error: '操作失败',
      required: '此项为必填项',
      default: '默认',
    },
    security: {
      title: '安全支付',
      ssl: '256位SSL加密',
      pci: 'PCI DSS合规',
      fraud: '欺诈防护',
    },
    payment: {
      method: '支付方式',
      chooseMethod: '选择您的首选支付方式',
      sessionExpires: '会话过期时间',
      creditCard: '信用卡支付',
      cardTypes: 'Visa, Mastercard, Amex',
      wechatPay: '微信支付',
      alipay: '支付宝',
      emailAddress: '邮箱地址',
      cardholderName: '持卡人姓名',
      cardInformation: '卡片信息',
      saveCard: '保存此卡片以备将来使用',
      processing: '处理中...',
      pay: '支付',
      scanQR: '使用微信扫描二维码',
      wechatInstruction: '请使用微信扫描二维码完成支付',
      expiresIn: '过期时间',
      qrScanned: '二维码已扫描 - 请在微信中完成支付',
      qrExpired: '二维码已过期',
      qrExpiredDesc: '二维码已过期，请重新生成',
      generateNew: '生成新二维码',
      generatingQR: '正在生成微信支付二维码...',
      generateWechatQR: '生成微信支付二维码',
      alipayPayment: '支付宝支付',
      alipayRedirect: '您将被重定向到支付宝完成支付',
      continueAlipay: '继续使用支付宝',
      processingPayment: '正在处理您的支付',
      complete: '完成',
      doNotClose: '支付处理期间请勿关闭此窗口',
      orderId: '订单号',
      viewOrder: '查看订单详情',
      tryAgain: '重试',
    },
  },
  en: {
    nav: {
      subtitle: 'Enterprise Payment Gateway',
      sslSecured: 'SSL Secured',
    },
    steps: {
      orderConfirmed: 'Order Confirmed',
      payment: 'Payment',
      complete: 'Complete',
    },
    order: {
      summary: 'Order Summary',
      quantity: 'Quantity',
      attributes: 'Attributes',
      subtotal: 'Subtotal',
      serviceFee: 'Service Fee',
      processingFee: 'Processing Fee',
      free: 'FREE',
      total: 'Total',
    },
    address: {
      title: 'Shipping Address',
      select: 'Please select a shipping address',
      add: 'Add new address',
      empty: 'No shipping address',
      addNew: 'Add New Address',
      edit: 'Edit Address',
      delete: 'Delete Address',
      deleteConfirm: 'Are you sure you want to delete this address?',
      cancel: 'Cancel',
      confirm: 'Confirm',
      save: 'Save Address',
      name: 'Recipient Name',
      phone: 'Phone Number',
      state: 'State/Province',
      city: 'City',
      street: 'Street',
      detail: 'Address Detail',
      zipCode: 'ZIP Code',
      setDefault: 'Set as default address',
      loading: 'Loading...',
      addSuccess: 'Address added successfully',
      updateSuccess: 'Address updated successfully',
      deleteSuccess: 'Address deleted successfully',
      error: 'Operation failed',
      required: 'This field is required',
      default: 'Default',
    },
    security: {
      title: 'Secure Payment',
      ssl: '256-bit SSL encryption',
      pci: 'PCI DSS compliant',
      fraud: 'Fraud protection enabled',
    },
    payment: {
      method: 'Payment Method',
      chooseMethod: 'Choose your preferred payment option',
      sessionExpires: 'Session expires in',
      creditCard: 'Credit Card',
      cardTypes: 'Visa, Mastercard, Amex',
      wechatPay: 'WeChat Pay',
      alipay: 'Alipay',
      emailAddress: 'Email Address',
      cardholderName: 'Cardholder Name',
      cardInformation: 'Card Information',
      saveCard: 'Save this card for future purchases',
      processing: 'Processing...',
      pay: 'Pay',
      scanQR: 'Scan QR Code with WeChat',
      wechatInstruction: 'Please use WeChat to scan the QR code to complete payment',
      expiresIn: 'Expires in',
      qrScanned: 'QR Code scanned - Complete payment in WeChat',
      qrExpired: 'QR Code Expired',
      qrExpiredDesc: 'QR code has expired, please generate a new one',
      generateNew: 'Generate New QR Code',
      generatingQR: 'Generating WeChat Pay QR Code...',
      generateWechatQR: 'Generate WeChat Pay QR Code',
      alipayPayment: 'Alipay Payment',
      alipayRedirect: 'You will be redirected to Alipay to complete your payment',
      continueAlipay: 'Continue with Alipay',
      processingPayment: 'Processing Payment',
      complete: 'Complete',
      doNotClose: 'Please do not close this window during payment processing',
      orderId: 'Order ID',
      viewOrder: 'View Order Details',
      tryAgain: 'Try Again',
    },
  },
}
// Stripe返回的客户端秘钥
const clientSecret = ref('')
const t = (key: string) => {
  const keys = key.split('.')
  let value: any = translations[currentLanguage.value as keyof typeof translations]
  for (const k of keys) {
    value = value?.[k]
  }
  return value || key
}

const setLanguage = (lang: 'zh' | 'en') => {
  currentLanguage.value = lang
  localStorage.setItem('language', lang)
}

// Router
const route = useRoute()
const router = useRouter()

// 从路由参数中获取商品信息
const productData = ref({
  id: 0,
  name: '未知商品',
  price: 0,
  pic: '',
  image: '', // 添加image字段
})
const deleteAddress = async (id: number) => {
  await addressApi.deleteAddress(id).then(() => {
    ElMessage.success('删除成功')
    addressSelectorRef.value?.fetchAddresses?.()
  })
  loadUserAddresses()
}
const product = computed(() => {
  try {
    // 尝试从路由查询参数中解析商品信息
    if (route.query.product) {
      return JSON.parse(route.query.product as string)
    }
    // 如果没有商品信息，返回默认值
    return productData.value
  } catch (error) {
    console.error('解析商品信息失败:', error)
    return productData.value
  }
})

const quantityValue = ref(1)
const quantity = computed(() => {
  // 从路由查询参数中获取数量
  const queryQuantity = route.query.quantity
  return queryQuantity ? Number(queryQuantity) : quantityValue.value
})

const attrListValue = ref<ProductAttribute[]>([])
const selectAttrList = computed<ProductAttribute[]>(() => {
  try {
    // 尝试从路由查询参数中解析商品属性
    if (route.query.attrs) {
      return JSON.parse(route.query.attrs as string)
    }
    return attrListValue.value
  } catch (error) {
    console.error('解析商品属性失败:', error)
    return attrListValue.value
  }
})

// Theme management
const isDarkMode = ref(localStorage.getItem('theme') === 'dark')

const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value
  localStorage.setItem('theme', isDarkMode.value ? 'dark' : 'light')
  applyDarkMode()
}
// 切换支付方式
const changePaymentMethod = (payment: string) => {
  paymentMethod.value = payment
  console.log('支付方式切换为：', paymentMethod.value)
}
const applyDarkMode = () => {
  if (isDarkMode.value) {
    document.documentElement.classList.add('dark')
  } else {
    document.documentElement.classList.remove('dark')
  }
}

// Session timer
const sessionTime = ref(1800) // 30 minutes
const sessionTimer = computed(() => {
  const minutes = Math.floor(sessionTime.value / 60)
  const seconds = sessionTime.value % 60
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
})

// Payment state
const paymentMethod = ref('stripe')
const paymentEmail = ref('')
const emailError = ref('')
const cardError = ref('')
const isProcessingPayment = ref(false)
const stripeInstance = ref<Stripe | null>(null)
const cardElement = ref<StripeCardElement | null>(null)
const paymentOrderId = ref(0)
// WeChat Pay - Fixed implementation
const wechatPayDialogVisible = ref(false)
const wechatPayQrCode = ref('')
const wechatPayLoading = ref(false)
const currentOrderNumber = ref('')
const paymentRetryCount = ref(0)
const paymentName = ref('')
const maxRetryCount = 2
const orderStatus = ref('')
const orderStatusDesc = ref('')
const hasScanned = ref(false)
const isPaying = ref(false)
const qrCodeGenerated = ref(false)
// 添加支付金额状态
const paymentAmount = ref(0)
const paymentAmountCny = ref(0)

// Countdown
const countdownTime = ref(1200)
const countdownDisplay = ref('20:00')
const isExpired = ref(false)
let countdownInterval: NodeJS.Timeout | undefined = undefined
let paymentStatusInterval: NodeJS.Timeout | undefined = undefined
let sessionInterval: NodeJS.Timeout | undefined = undefined

// Payment progress
const paymentInProgress = ref(false)
const paymentProgressPercent = ref(0)
const paymentProgressMessage = ref('Initializing payment...')
let paymentProgressInterval: NodeJS.Timeout | undefined = undefined

// Payment result
const paymentResultVisible = ref(false)
const isCreatingOrder = ref(false) // 添加订单构建状态
const paymentResult = reactive({
  status: 'success' as 'success' | 'error' | 'pending',
  title: 'Payment Successful',
  messageCn: 'Your payment has been processed successfully',
  messageEn: 'Your payment has been successfully processed',
  icon: CheckCircleIcon as Component,
  iconClass: 'text-green-500',
  textClass: 'text-green-600 dark:text-green-400',
  errorCode: '',
  orderId: '',
})

// Price calculations
const serviceFeeRate = computed(() => {
  return paymentMethod.value === 'stripe' ? 0.04 : 0.02
})
const baseAmount = computed(() => {
  return product.value.price * quantity.value
})
const serviceFee = computed(() => {
  return parseFloat((baseAmount.value * serviceFeeRate.value).toFixed(2))
})
const stripeServiceFee = 0.3
const totalAmount = computed(() => {
  if (paymentMethod.value === 'stripe') {
    return baseAmount.value + serviceFee.value + stripeServiceFee
  } else {
    return baseAmount.value + serviceFee.value
  }
})

const formatPrice = (price: number): string => price.toFixed(2)

// 添加人民币价格转换函数
const formatCnyPrice = (price: number): string => {
  // 使用更准确的汇率，可以根据实际情况调整
  const cnyRate = 7.23
  return (price * cnyRate).toFixed(2)
}

// 使用导入的request模块

// Stripe initialization
const initStripeElements = async () => {
  try {
    const stripe = await loadStripe(import.meta.env.VITE_STRIPE_PUBLISH_KEY)
    if (!stripe) throw new Error('Failed to load Stripe.js')

    stripeInstance.value = stripe
    await nextTick()

    const cardElementContainer = document.getElementById('card-element')
    if (!cardElementContainer) return

    const elements = stripe.elements()
    const card = elements.create('card', {
      style: {
        base: {
          color: isDarkMode.value ? '#ffffff' : '#334155',
          fontFamily: 'system-ui, sans-serif',
          fontSize: '16px',
          fontWeight: '500',
          fontSmoothing: 'antialiased',
          '::placeholder': {
            color: isDarkMode.value ? '#94a3b8' : '#94a3b8',
          },
          ':-webkit-autofill': {
            color: isDarkMode.value ? '#ffffff' : '#334155',
            backgroundColor: isDarkMode.value ? '#1e293b' : 'transparent',
          },
          ':focus': {
            color: isDarkMode.value ? '#ffffff' : '#334155',
          },
          iconColor: isDarkMode.value ? '#60a5fa' : '#3b82f6',
        },
        invalid: {
          color: '#ef4444',
          iconColor: '#ef4444',
        },
      },
    })

    card.mount('#card-element')
    cardElement.value = card

    card.on('change', (event: { brand?: string; error?: { message: string } }) => {
      // paymentMethod.value = event.brand as string
      // console.log('支付类型：', paymentMethod.value)
      cardError.value = event.error ? event.error.message : ''
    })
  } catch (error) {
    console.error('Stripe initialization failed:', error)
    cardError.value = 'Failed to load payment processor'
  }
}

// Payment handling - Fixed implementation
const handlePayment = async () => {
  if (isProcessingPayment.value) return

  isProcessingPayment.value = true

  // 只有在信用卡支付方式下才启动进度条
  if (paymentMethod.value === 'stripe') {
    startPaymentProgress()
  }

  try {
    if (paymentMethod.value === 'stripe') {
      await processStripePayment()
    } else if (paymentMethod.value === 'wechat') {
      await initiateWechatPayment()
    } else if (paymentMethod.value === 'alipay') {
      // 验证是否选择了收货地址
      if (!selectedAddress.value || !selectedAddress.value.id) {
        ElMessage.error('请先选择收货地址')
        return
      }

      showPaymentResult({
        status: 'error',
        title: '支付方式暂未开放',
        messageCn: '支付宝支付功能正在开发中，暂未开放',
        messageEn: 'Alipay payment is currently under development and not available yet',
        icon: AlertCircleIcon,
        iconClass: 'text-orange-500',
        textClass: 'text-orange-600 dark:text-orange-400',
      })

      // 如果后续支持支付宝支付，可以在这里添加支付宝支付的处理逻辑
      // 并在支付成功后调用 handleAlipayPaymentSuccess 函数
    }
  } catch (error) {
    console.error('Payment error:', error)
    showPaymentResult({
      status: 'error',
      title: 'Payment Error',
      messageCn: 'An error occurred during payment processing',
      messageEn: 'Please try again or contact support',
      icon: XCircleIcon,
      iconClass: 'text-red-500',
      textClass: 'text-red-600 dark:text-red-400',
    })
  } finally {
    if (paymentMethod.value === 'stripe') {
      isProcessingPayment.value = false
      stopPaymentProgress()
    }
  }
}

const processStripePayment = async () => {
  if (!validateEmail()) {
    return
  }

  isProcessingPayment.value = true
  startPaymentProgress()

  try {
    if (!stripeInstance.value || !cardElement.value) {
      showPaymentResult({
        status: 'error',
        title: 'Payment System Error',
        messageCn: '支付系统未初始化，请重试',
        messageEn: 'Payment system not initialized, please try again',
        icon: XCircleIcon,
        iconClass: 'text-red-500',
        textClass: 'text-red-600 dark:text-red-400',
      })
      return
    }
    if (product.value.price * quantity.value < 0.5) {
      showPaymentResult({
        status: 'error',
        title: 'Invalid Order',
        messageCn: '银行卡支付方式最低额度为0.5$，请切换支付方式',
        messageEn:
          'Bank card payment requires a minimum of $0.5. Please choose a different payment method.',
        icon: XCircleIcon,
        iconClass: 'text-red-500',
        textClass: 'text-red-600 dark:text-red-400',
      })
      return
    }

    // 创建支付方法
    const result = await stripeInstance.value.createPaymentMethod({
      type: 'card',
      card: cardElement.value,
      billing_details: {
        address: {
          city: selectedAddress.value?.city,
          country: 'US',
          line1: selectedAddress.value?.street,
          line2: selectedAddress.value?.addressDetail,
          postal_code: selectedAddress.value?.zipCode,
          state: selectedAddress.value?.state,
        },
        email: paymentEmail.value,
        name: paymentName.value,
        phone: selectedAddress.value?.phoneNumber,
      },
    })

    const paymentMethodError = result.error
    const paymentMethod = result.paymentMethod

    if (paymentMethodError) {
      // 显示错误信息
      cardError.value = paymentMethodError.message || '支付方法创建失败'
      isProcessingPayment.value = false
      stopPaymentProgress()
      return
    }

    if (!paymentMethod) {
      showPaymentResult({
        status: 'error',
        title: 'Payment Method Error',
        messageCn: '支付方法创建失败',
        messageEn: 'Failed to create payment method',
        icon: XCircleIcon,
        iconClass: 'text-red-500',
        textClass: 'text-red-600 dark:text-red-400',
      })
      isProcessingPayment.value = false
      stopPaymentProgress()
      return
    }

    paymentOrderId.value = Number(Date.now() + '' + (Math.floor(Math.random() * 90000) + 10000))
    // 构建支付数据
    const paymentData = {
      paymentMethod: paymentMethod.id,
      amount: product.value.price * quantity.value,
      currency: 'usd',
      description: `购买${product.value.name}`,
      email: paymentEmail.value,
      metadata: {
        productId: product.value.id,
        quantity: quantityValue.value,
        attributes: JSON.stringify(selectAttrList.value),
      },
      // 生成唯一订单ID作为幂等键
      orderId: paymentOrderId.value,
    }

    // 向后端发送支付请求
    const response = await request.post('/pay/stripePay', paymentData)

    // 检查响应是否成功 (HTTP 200)
    if (response.status === 200) {
      // 检查返回的数据结构，识别Stripe API成功响应
      console.log('Stripe API成功响应:', response.data.data.clientSecret)
      clientSecret.value = response.data.data.clientSecret
      // 如果需要，将自动进行3D验证
      const { paymentIntent, error } = await stripeInstance.value.confirmPayment({
        clientSecret: clientSecret.value,
        confirmParams: {
          return_url: `${window.location.origin + window.location.pathname}`,
          payment_method: paymentMethod.id,
        },
        redirect: 'if_required', //自动进行3D验证
      })
      console.log('支付结果：', paymentIntent, error?.code)
      isProcessingPayment.value = false
      stopPaymentProgress()
      if (error) {
        showPaymentResult({
          status: 'error',
          title: '3D验证失败 (Verification Failed)',
          messageCn: error.message as string,
          messageEn: error.message as string,
          icon: XCircleIcon,
          iconClass: 'error-icon',
          textClass: 'error-text',
          errorCode: error.code,
        })
        paymentDialogVisible.value = false
      } else if (paymentIntent.status === 'succeeded') {
        // 处理支付成功
        await handleStripePaymentSuccess(paymentOrderId.value.toString())
        isProcessingPayment.value = false
        stopPaymentProgress()
        showPaymentResult({
          status: 'success',
          title: '支付成功 (Payment Successful)',
          messageCn: '您的订单已成功支付',
          messageEn: 'Your payment has been successfully processed',
          icon: CheckCircleIcon,
          iconClass: 'success-icon',
          textClass: 'success-text',
          orderId: `${paymentIntent.description}`,
        })
      } else if (paymentIntent.status === 'processing') {
        // 启动轮询检查状态
        const pollInterval = setInterval(async () => {
          const result = await stripeInstance.value?.retrievePaymentIntent(clientSecret.value)
          if (!result) {
            clearInterval(pollInterval)
            showPaymentResult({
              status: 'failed',
              title: '支付失败 (Payment failed)',
              messageCn: '无法获取支付状态',
              messageEn: 'Unable To Get PaymentStatus',
              icon: XCircleIcon,
              iconClass: 'text-red-500',
              textClass: 'text-red-600 dark:text-red-400',
            })
            return
          }
          // 明确判断paymentIntent存在
          if (result?.paymentIntent && result.paymentIntent.status === 'succeeded') {
            clearInterval(pollInterval)
            showPaymentResult({
              status: 'success',
              title: '支付成功 (Payment Successful)',
              messageCn: '您的订单已成功支付',
              messageEn: 'Your payment has been successfully processed',
              icon: CheckCircleIcon,
              iconClass: 'success-icon',
              textClass: 'success-text',
              orderId: `${paymentIntent.description}`,
            })
            // 处理支付成功
            await handleStripePaymentSuccess(paymentOrderId.value.toString())
            isProcessingPayment.value = false
            stopPaymentProgress()
          } else if (result?.paymentIntent && result.paymentIntent.status === 'processing') {
            return
          }
        }, 5000) // 每5秒检查一次
      }
    }
  } catch (error) {
    console.error('Stripe payment error:', error)
    showPaymentResult({
      status: 'error',
      title: 'Payment Processing Error',
      messageCn: '支付处理过程中发生错误',
      messageEn: 'An error occurred during payment processing',
      icon: XCircleIcon,
      iconClass: 'text-red-500',
      textClass: 'text-red-600 dark:text-red-400',
    })
    isProcessingPayment.value = false
    stopPaymentProgress()
  }
}

// Fixed WeChat Pay implementation
const initiateWechatPayment = async () => {
  try {
    // 验证是否选择了收货地址
    if (!selectedAddress.value || !selectedAddress.value.id) {
      ElMessage.error('请先选择收货地址')
      return
    }

    // 如果是新的支付请求，重置重试计数器
    if (!wechatPayDialogVisible.value) {
      paymentRetryCount.value = 0
    }

    wechatPayLoading.value = true
    wechatPayDialogVisible.value = true
    isExpired.value = false

    // 计算支付金额（使用已有的计算属性）
    paymentAmount.value = totalAmount.value
    paymentAmountCny.value = parseFloat((paymentAmount.value * 7.23).toFixed(2))

    // Generate unique order number
    const generateOutTradeNo = () => {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const seconds = String(now.getSeconds()).padStart(2, '0')
      const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
      const randomStr = Math.random().toString(36).substring(2, 10)
      return `GLOBAL_${timestamp}_${randomStr}`
    }

    const outTradeNo = generateOutTradeNo()
    currentOrderNumber.value = outTradeNo

    // Build payment data - 按照后端接口要求构建数据
    const paymentData = {
      total: Math.round(paymentAmount.value * 100), // Convert to cents
      title: `Purchase ${product.value.name}`,
      out_trade_no: outTradeNo, // 使用后端接口要求的字段名
      description: `${product.value.name} x ${quantity.value}${selectAttrList.value && selectAttrList.value.length > 0 ? ' - ' + selectAttrList.value.map((attr: ProductAttribute) => `${attr.key}: ${attr.value}`).join(', ') : ''}`,
    }

    console.log('WeChat Pay request parameters:', paymentData)

    // Call backend API to get payment info - 调用后端接口
    const response = await request.post('/pay/wechat/native/qrcode', paymentData)
    console.log('WeChat Pay response:', response)

    // 根据后端实际返回的数据结构进行处理
    if (response && response.data && response.data.code === 1 && response.data.data) {
      // 获取二维码URL和订单号
      const codeUrl = response.data.data.codeUrl
      const orderNo = response.data.data.orderNo

      if (!codeUrl) {
        throw new Error('No QR code URL in response data')
      }

      // 更新订单号
      if (orderNo) {
        currentOrderNumber.value = orderNo
      }

      // 生成二维码
      try {
        const qrCodeDataUrl = await generateQRCode(codeUrl)
        wechatPayQrCode.value = qrCodeDataUrl
        qrCodeGenerated.value = true
      } catch (qrError) {
        console.error('Failed to generate QR code:', qrError)
        wechatPayQrCode.value = codeUrl // Fallback: show URL directly
        qrCodeGenerated.value = true
      }

      wechatPayLoading.value = false
      isProcessingPayment.value = false

      // 停止支付进度条显示
      stopPaymentProgress()

      // 开始轮询支付状态
      startPollingPaymentStatus(currentOrderNumber.value)

      // 开始倒计时
      startCountdown()
    } else {
      // 检查是否需要重试
      if (paymentRetryCount.value < maxRetryCount) {
        paymentRetryCount.value++
        console.log(`支付二维码生成失败，正在进行第${paymentRetryCount.value}次重试...`)
        wechatPayLoading.value = false
        isProcessingPayment.value = false
        stopPaymentProgress()
        // 延迟1秒后重试
        setTimeout(() => {
          initiateWechatPayment()
        }, 1000)
        return
      } else {
        throw new Error(`支付功能繁忙，请稍后重试`)
      }
    }
  } catch (error) {
    console.error('Payment initialization failed:', error)
    // 检查是否需要重试
    if (paymentRetryCount.value < maxRetryCount) {
      paymentRetryCount.value++
      console.log(`支付初始化失败，正在进行第${paymentRetryCount.value}次重试...`)
      wechatPayLoading.value = false
      isProcessingPayment.value = false
      stopPaymentProgress()
      // 延迟1秒后重试
      setTimeout(() => {
        initiateWechatPayment()
      }, 1000)
      return
    } else {
      wechatPayLoading.value = false
      isProcessingPayment.value = false
      stopPaymentProgress()

      // 显示错误信息
      showPaymentResult({
        status: 'error',
        title: 'Payment Initialization Failed',
        messageCn: '支付功能繁忙，请稍后重试',
        messageEn: 'Payment service is busy, please try again later',
        icon: XCircleIcon,
        iconClass: 'text-red-500',
        textClass: 'text-red-600 dark:text-red-400',
      })
    }
  }
}

// Poll payment status - Fixed implementation
const startPollingPaymentStatus = (outTradeNo: string) => {
  if (paymentStatusInterval) {
    clearInterval(paymentStatusInterval)
  }

  // Check payment status every 3 seconds
  paymentStatusInterval = setInterval(async () => {
    try {
      // 构建查询参数 - 按照后端接口要求
      const queryParams = {
        out_trade_no: outTradeNo, // 使用订单号查询
      }

      // 查询订单状态
      const response = await request.post('/pay/wechat/order/query', queryParams)
      console.log('Payment status response:', response)

      // 处理响应
      if (response && response.data && response.data.code === 1 && response.data.data) {
        const orderInfo = response.data.data

        // 更新订单状态
        orderStatus.value = orderInfo.trade_state || ''
        orderStatusDesc.value = orderInfo.trade_state_desc || ''

        // 根据状态更新UI
        switch (orderStatus.value) {
          case 'NOTPAY': // 未支付
            hasScanned.value = false
            isPaying.value = false
            break
          case 'USERPAYING': // 用户支付中
            hasScanned.value = true
            isPaying.value = true
            break
          case 'SUCCESS': // 支付成功
            if (paymentStatusInterval) {
              clearInterval(paymentStatusInterval)
              paymentStatusInterval = undefined
            }

            // 处理支付成功
            await handleWechatPaymentSuccess(outTradeNo)
            break
          case 'CLOSED': // 已关闭
          case 'REVOKED': // 已撤销
          case 'PAYERROR': // 支付失败
            if (paymentStatusInterval) {
              clearInterval(paymentStatusInterval)
              paymentStatusInterval = undefined
            }

            // 显示失败结果
            showPaymentResult({
              status: 'error',
              title: 'Payment Failed',
              messageCn: '支付未完成或已取消',
              messageEn: 'Payment was not completed or was cancelled',
              icon: XCircleIcon,
              iconClass: 'text-red-500',
              textClass: 'text-red-600 dark:text-red-400',
            })
            break
        }
      }
    } catch (error) {
      console.error('Failed to query payment status:', error)
    }
  }, 3000)
}

// 定义通用响应类型
interface ApiResponse<T = any> {
  code: number
  data: T
  msg?: string
}

// 支付成功后创建订单
const createOrder = async (paymentTransactionId: string, payMethod: number) => {
  try {
    // 验证必要的数据
    if (!selectedAddress.value || !selectedAddress.value.id) {
      ElMessage.error('请选择收货地址')
      return null
    }

    // 构建订单项
    const orderItems = [
      {
        productId: product.value.id || 0,
        quantity: quantityValue.value || 1,
        unitPrice: product.value.price || 0,
      },
    ]

    // 构建订单提交数据
    const orderSubmitDTO = {
      // 收货地址ID
      addressId: selectedAddress.value.id,
      // 支付方式（1: 信用卡, 2: 微信, 3: 支付宝）
      payMethod: payMethod,
      // 配送方式ID，默认为1（标准配送）
      shippingMethodId: 1,
      // 订单备注
      orderRemark: '',
      // 订单项
      orderItems: orderItems,
      // 订单金额
      amount: totalAmount.value,
      // 支付交易ID
      paymentTransactionId: paymentTransactionId,
      // 支付时间戳
      payTimeStamp: Date.now(),
    }

    console.log('提交订单数据:', orderSubmitDTO)

    // 调用后端API创建订单
    const response = await request.post('/orders/submit', orderSubmitDTO)

    if (response && response.data && response.data.code === 1) {
      console.log('订单创建成功:', response.data)
      return response.data.data
    } else {
      ElMessage.error(response?.data?.msg || '创建订单失败')
      return null
    }
  } catch (error) {
    console.error('创建订单失败:', error)
    ElMessage.error('创建订单时发生错误')
    return null
  }
}

// Generate QR code helper function
const generateQRCode = async (text: string): Promise<string> => {
  try {
    return await QRCode.toDataURL(text, {
      errorCorrectionLevel: 'H',
      margin: 1,
      width: 300,
      color: {
        dark: '#000000',
        light: '#ffffff',
      },
    })
  } catch (error) {
    console.error('Failed to generate QR code:', error)
    throw error
  }
}

// Refresh QR code
const refreshQRCode = () => {
  if (isProcessingPayment.value) return
  isProcessingPayment.value = true
  qrCodeGenerated.value = false
  initiateWechatPayment()
}

// Utility functions
const startPaymentProgress = () => {
  paymentProgressPercent.value = 0
  paymentProgressMessage.value = 'Initializing payment...'
  paymentInProgress.value = true

  let progress = 0
  paymentProgressInterval = setInterval(() => {
    if (progress < 95) {
      progress += Math.random() * 5 // 减慢进度条速度
      paymentProgressPercent.value = Math.min(progress, 95)

      if (progress < 20) {
        paymentProgressMessage.value = 'Initializing payment system...'
      } else if (progress < 40) {
        paymentProgressMessage.value = 'Connecting to payment gateway...'
      } else if (progress < 70) {
        paymentProgressMessage.value = 'Processing payment request...'
      } else {
        paymentProgressMessage.value = 'Finalizing transaction...'
      }
    }
  }, 500) // 增加间隔时间，让进度条更慢
}

const stopPaymentProgress = () => {
  if (paymentProgressInterval) {
    clearInterval(paymentProgressInterval)
    paymentProgressInterval = undefined
  }

  paymentProgressPercent.value = 100
  paymentProgressMessage.value = 'Payment completed'

  setTimeout(() => {
    paymentInProgress.value = false
  }, 1000)
}

const startCountdown = () => {
  countdownTime.value = 1200
  countdownDisplay.value = '20:00'
  isExpired.value = false

  countdownInterval = setInterval(() => {
    if (countdownTime.value > 0) {
      countdownTime.value--
      const minutes = Math.floor(countdownTime.value / 60)
      const seconds = countdownTime.value % 60
      countdownDisplay.value = `${minutes}:${seconds.toString().padStart(2, '0')}`
    } else {
      isExpired.value = true
      if (countdownInterval) {
        clearInterval(countdownInterval)
        countdownInterval = undefined
      }
      if (paymentStatusInterval) {
        clearInterval(paymentStatusInterval)
        paymentStatusInterval = undefined
      }
    }
  }, 1000)
}

const showPaymentResult = (result: any) => {
  Object.assign(paymentResult, result)
  paymentResultVisible.value = true
}

const closePaymentResult = () => {
  paymentResultVisible.value = false
  if (paymentResult.status === 'success') {
    router.push('/order/confirmation')
  }
}

const startSessionTimer = () => {
  sessionInterval = setInterval(() => {
    if (sessionTime.value > 0) {
      sessionTime.value--
    } else {
      alert('Session expired. Please refresh the page.')
    }
  }, 1000)
}

// Lifecycle
onMounted(() => {
  console.log(import.meta.env.VITE_STRIPE_PUBLISH_KEY)
  applyDarkMode()
  startSessionTimer()

  // 处理从ProductDetail.vue传递过来的参数
  try {
    // 处理products参数
    if (route.query.products) {
      const productsData = JSON.parse(route.query.products as string)
      console.log('解析到的商品数据:', productsData)

      if (productsData && productsData.length > 0) {
        const productItem = productsData[0]
        console.log('商品图片路径:', productItem.image)

        // 更新商品信息
        productData.value = {
          id: productItem.id,
          name: productItem.name,
          price: productItem.price,
          pic: productItem.image || '', // 使用image字段作为pic
          image: productItem.image || '', // 设置image字段
        }

        // 调试输出
        console.log('处理后的商品图片路径:', productData.value.image)

        console.log('处理后的商品数据:', productData.value)

        // 更新数量
        quantityValue.value = productItem.quantity
        // 更新属性列表
        if (productItem.attributes) {
          attrListValue.value = productItem.attributes
        }
      }
    }

    // 处理orderInfo参数
    if (route.query.orderInfo) {
      const orderInfoData = JSON.parse(route.query.orderInfo as string)
      if (orderInfoData) {
        // 如果有总金额，更新总金额
        if (orderInfoData.totalAmount) {
          // 更新基础金额，服务费会自动计算
          const baseAmountValue = orderInfoData.totalAmount
          // 通过调整product.price来间接影响baseAmount
          if (quantityValue.value > 0) {
            productData.value.price = baseAmountValue / quantityValue.value
          }
        }

        // 如果是直接购买，可以添加一些特殊处理
        if (orderInfoData.directBuy) {
          console.log('直接购买模式')
          // 可以在这里添加直接购买的特殊逻辑
        }
      }
    }
  } catch (error) {
    console.error('处理路由参数时出错:', error)
  }

  // 检查用户是否已登录
  const token = localStorage.getItem('token')
  if (token) {
    // 获取用户地址列表
    loadUserAddresses()
    console.log('地址列表：', addresses.value)
  } else {
    // 未登录，显示登录提示
    showCentralAddressButton.value = false
  }

  if (paymentMethod.value === 'stripe') {
    initStripeElements()
  }
})

// 加载用户地址
const loadUserAddresses = async () => {
  try {
    // 获取当前用户ID
    const userStr = localStorage.getItem('user')
    if (!userStr) return

    const user = JSON.parse(userStr)
    const userId = user?.id
    if (!userId) return

    // 获取用户地址列表
    const res = await addressApi.getAddressList(userId)
    if (res.data.code === 1 && res.data.data) {
      addresses.value = res.data.data
      console.log('获取到的地址列表:', addresses.value)

      // 如果没有地址，显示中央添加地址按钮
      if (!addresses.value || addresses.value.length === 0) {
        showCentralAddressButton.value = true
      } else {
        showCentralAddressButton.value = false

        // 选择默认地址或第一个地址
        const defaultAddress = addresses.value.find((addr) => addr.Default === 1)
        if (defaultAddress) {
          selectedAddressId.value = defaultAddress.id
          selectedAddress.value = defaultAddress
        } else if (addresses.value.length > 0) {
          selectedAddressId.value = addresses.value[addresses.value.length - 1].id
          selectedAddress.value = addresses.value[addresses.value.length - 1]
        }
      }
    } else {
      // API调用失败，显示中央添加地址按钮
      showCentralAddressButton.value = true
    }
  } catch (error) {
    console.error('获取地址列表失败:', error)
    // 出错时显示中央添加地址按钮
    showCentralAddressButton.value = true
  }
}

onUnmounted(() => {
  if (countdownInterval) clearInterval(countdownInterval)
  if (paymentStatusInterval) clearInterval(paymentStatusInterval)
  if (paymentProgressInterval) clearInterval(paymentProgressInterval)
  if (sessionInterval) clearInterval(sessionInterval)
})

watch(paymentMethod, (newMethod) => {
  if (newMethod === 'stripe') {
    nextTick(() => {
      setTimeout(initStripeElements, 100)
    })
  } else if (newMethod === 'wechat') {
    // 自动开始微信支付流程
    initiateWechatPayment()
  }
})

watch(isDarkMode, () => {
  if (cardElement.value) {
    setTimeout(initStripeElements, 100)
  }
})

// 查看订单
const viewOrder = () => {
  if (paymentResult.orderId) {
    // 保存临时订单数据并跳转
    saveOrderDataAndRedirect()
  }
}

// 重试支付
const retryPayment = () => {
  paymentResultVisible.value = false
  paymentDialogVisible.value = true

  // 保留之前填写的邮箱地址，不需要用户重新输入
  // cardElement会自动重置，所以不需要额外处理

  // 重置卡片错误提示
  cardError.value = ''
  emailError.value = ''
}

// 保存订单数据并跳转到临时订单详情页面
const saveOrderDataAndRedirect = () => {
  if (paymentResult.orderId) {
    // 保存临时订单数据到localStorage，包含更完整的信息
    const tempOrderData = {
      productImg: product.value.pic || product.value.image || '/placeholder.svg',
      productName: product.value.name,
      productAttributes: selectAttrList.value
        .map((item) => `${item.key}: ${item.value}`)
        .join(', '),
      productQuantity: quantity.value,
      productPrice: product.value.price,
      orderTime: new Date().toISOString(),
      orderId: paymentResult.orderId,
      // 添加更多订单信息
      shippingAddress: selectedAddress.value ? formatAddress(selectedAddress.value) : '',
      paymentMethod:
        paymentMethod.value === 'stripe'
          ? '信用卡支付'
          : paymentMethod.value === 'wechat'
            ? '微信支付'
            : paymentMethod.value === 'alipay'
              ? '支付宝'
              : '在线支付',
      paymentMethodCode:
        paymentMethod.value === 'stripe'
          ? 3 // 银行卡/信用卡支付
          : paymentMethod.value === 'wechat'
            ? 1 // 微信支付
            : paymentMethod.value === 'alipay'
              ? 2 // 支付宝
              : 3, // 默认使用信用卡支付
      transactionId: paymentResult.orderId,
      totalAmount: totalAmount.value,
      serviceFee: serviceFee.value,
      baseAmount: baseAmount.value,
    }

    // 保存到localStorage，不删除数据，让用户可以一直查看
    localStorage.setItem('tempOrderData', JSON.stringify(tempOrderData))

    // 跳转到临时订单详情页面
    router.push('/temp-order-detail')

    // 关闭支付结果弹窗
    paymentResultVisible.value = false
  }
}

// 地址选择相关
const selectedAddressId = ref<number>(0)
const selectedAddress = ref<USAddress | null>(null)
const showAddressForm = ref(false)
const isEditingAddress = ref(false)
const isDefaultAddress = ref(false)
const addressForm = reactive({
  id: undefined as number | undefined,
  name: '',
  phoneNumber: '',
  state: '',
  city: '',
  street: '',
  addressDetail: '',
  zipCode: '',
  Default: 0,
})

// 处理地址选择
const handleAddressSelected = (address: USAddress | null) => {
  selectedAddress.value = address
  console.log('选中地址:', address)
}

// 打开新增地址表单
const openAddAddressForm = () => {
  isEditingAddress.value = false
  resetAddressForm()
  showAddressForm.value = true
}

// 打开编辑地址表单
const openEditAddressForm = (address: USAddress) => {
  isEditingAddress.value = true
  Object.assign(addressForm, address)
  isDefaultAddress.value = address.Default === 1
  showAddressForm.value = true
}

// 重置地址表单
const resetAddressForm = () => {
  Object.assign(addressForm, {
    id: undefined,
    name: '',
    phoneNumber: '',
    state: '',
    city: '',
    street: '',
    addressDetail: '',
    zipCode: '',
    Default: 0,
  })
  isDefaultAddress.value = false
}

// 地址表单校验
const addressErrors = reactive({
  name: '',
  phoneNumber: '',
  state: '',
  city: '',
  street: '',
  addressDetail: '',
  zipCode: '',
})

// 校验规则
const validateName = (name: string) => {
  if (!name) return '姓名不能为空'
  if (name.length < 2) return '姓名至少需要2个字符'
  if (name.length > 50) return '姓名不能超过50个字符'
  return ''
}

const validatePhoneNumber = (phone: string) => {
  if (!phone) return '电话号码不能为空'
  // 美国电话号码格式：+1 XXX-XXX-XXXX 或 (XXX) XXX-XXXX 或 XXX-XXX-XXXX
  const phoneRegex = /^(\+1\s?)?(\(\d{3}\)|\d{3})[-.\s]?\d{3}[-.\s]?\d{4}$/
  if (!phoneRegex.test(phone)) return '请输入有效的美国电话号码格式'
  return ''
}

const validateState = (state: string) => {
  if (!state) return '州不能为空'
  // 美国州名或州缩写
  const stateRegex = /^[A-Za-z\s]{2,50}$/
  if (!stateRegex.test(state)) return '请输入有效的州名或州缩写'
  return ''
}

const validateCity = (city: string) => {
  if (!city) return '城市不能为空'
  const cityRegex = /^[A-Za-z\s\-'.]{2,50}$/
  if (!cityRegex.test(city)) return '请输入有效的城市名称'
  return ''
}

const validateStreet = (street: string) => {
  if (!street) return '街道不能为空'
  if (street.length < 3) return '街道地址至少需要3个字符'
  if (street.length > 100) return '街道地址不能超过100个字符'
  return ''
}

const validateAddressDetail = (detail: string) => {
  if (!detail) return '详细地址不能为空'
  if (detail.length > 200) return '详细地址不能超过200个字符'
  return ''
}

const validateZipCode = (zipCode: string) => {
  if (!zipCode) return '邮编不能为空'
  // 美国邮编格式：5位数字 或 5位数字-4位数字
  const zipRegex = /^\d{5}(-\d{4})?$/
  if (!zipRegex.test(zipCode)) return '请输入有效的美国邮编格式 (12345 或 12345-6789)'
  return ''
}

// 实时校验表单
const validateAddressForm = () => {
  addressErrors.name = validateName(addressForm.name)
  addressErrors.phoneNumber = validatePhoneNumber(addressForm.phoneNumber)
  addressErrors.state = validateState(addressForm.state)
  addressErrors.city = validateCity(addressForm.city)
  addressErrors.street = validateStreet(addressForm.street)
  addressErrors.addressDetail = validateAddressDetail(addressForm.addressDetail)
  addressErrors.zipCode = validateZipCode(addressForm.zipCode)

  // 返回是否所有字段都有效
  return !Object.values(addressErrors).some((error) => error !== '')
}

// 监听表单字段变化并实时校验
watch(
  () => addressForm.name,
  (newVal) => {
    addressErrors.name = validateName(newVal)
  },
)

watch(
  () => addressForm.phoneNumber,
  (newVal) => {
    addressErrors.phoneNumber = validatePhoneNumber(newVal)
  },
)

watch(
  () => addressForm.state,
  (newVal) => {
    addressErrors.state = validateState(newVal)
  },
)

watch(
  () => addressForm.city,
  (newVal) => {
    addressErrors.city = validateCity(newVal)
  },
)

watch(
  () => addressForm.street,
  (newVal) => {
    addressErrors.street = validateStreet(newVal)
  },
)

watch(
  () => addressForm.addressDetail,
  (newVal) => {
    addressErrors.addressDetail = validateAddressDetail(newVal)
  },
)

watch(
  () => addressForm.zipCode,
  (newVal) => {
    addressErrors.zipCode = validateZipCode(newVal)
  },
)

// 美国州列表
const usStates = [
  { code: 'AL', name: 'Alabama' },
  { code: 'AK', name: 'Alaska' },
  { code: 'AZ', name: 'Arizona' },
  { code: 'AR', name: 'Arkansas' },
  { code: 'CA', name: 'California' },
  { code: 'CO', name: 'Colorado' },
  { code: 'CT', name: 'Connecticut' },
  { code: 'DE', name: 'Delaware' },
  { code: 'FL', name: 'Florida' },
  { code: 'GA', name: 'Georgia' },
  { code: 'HI', name: 'Hawaii' },
  { code: 'ID', name: 'Idaho' },
  { code: 'IL', name: 'Illinois' },
  { code: 'IN', name: 'Indiana' },
  { code: 'IA', name: 'Iowa' },
  { code: 'KS', name: 'Kansas' },
  { code: 'KY', name: 'Kentucky' },
  { code: 'LA', name: 'Louisiana' },
  { code: 'ME', name: 'Maine' },
  { code: 'MD', name: 'Maryland' },
  { code: 'MA', name: 'Massachusetts' },
  { code: 'MI', name: 'Michigan' },
  { code: 'MN', name: 'Minnesota' },
  { code: 'MS', name: 'Mississippi' },
  { code: 'MO', name: 'Missouri' },
  { code: 'MT', name: 'Montana' },
  { code: 'NE', name: 'Nebraska' },
  { code: 'NV', name: 'Nevada' },
  { code: 'NH', name: 'New Hampshire' },
  { code: 'NJ', name: 'New Jersey' },
  { code: 'NM', name: 'New Mexico' },
  { code: 'NY', name: 'New York' },
  { code: 'NC', name: 'North Carolina' },
  { code: 'ND', name: 'North Dakota' },
  { code: 'OH', name: 'Ohio' },
  { code: 'OK', name: 'Oklahoma' },
  { code: 'OR', name: 'Oregon' },
  { code: 'PA', name: 'Pennsylvania' },
  { code: 'RI', name: 'Rhode Island' },
  { code: 'SC', name: 'South Carolina' },
  { code: 'SD', name: 'South Dakota' },
  { code: 'TN', name: 'Tennessee' },
  { code: 'TX', name: 'Texas' },
  { code: 'UT', name: 'Utah' },
  { code: 'VT', name: 'Vermont' },
  { code: 'VA', name: 'Virginia' },
  { code: 'WA', name: 'Washington' },
  { code: 'WV', name: 'West Virginia' },
  { code: 'WI', name: 'Wisconsin' },
  { code: 'WY', name: 'Wyoming' },
  { code: 'DC', name: 'District of Columbia' },
]

// 修改保存地址函数，添加表单校验
const saveAddress = async () => {
  try {
    // 表单验证
    if (!validateAddressForm()) {
      ElMessage.error('请修正表单中的错误')
      return
    }

    // 获取当前用户ID
    const userStr = localStorage.getItem('user')
    if (!userStr) {
      ElMessage.error(t('address.error'))
      return
    }

    const user = JSON.parse(userStr)
    const userId = user?.id

    if (!userId) {
      ElMessage.error(t('address.error'))
      return
    }

    // 准备保存的地址数据
    const addressData = {
      ...addressForm,
      userId,
      Default: isDefaultAddress.value ? 1 : 0,
    }

    console.log('保存地址数据:', addressData)

    let res
    if (isEditingAddress.value && addressForm.id) {
      // 更新地址
      res = await addressApi.updateAddress(addressData)
    } else {
      // 添加地址
      res = await addressApi.addAddress(addressData)
    }

    if (res.data.code === 1) {
      ElMessage.success(
        isEditingAddress.value ? t('address.updateSuccess') : t('address.addSuccess'),
      )
      hasAddAddress.value = true
      // 如果设为默认地址
      if (isDefaultAddress.value) {
        // 如果是编辑模式，使用现有ID；如果是新增模式，使用返回的ID
        const addressId = addressForm.id || (res.data.data && res.data.data.id)
        if (addressId) {
          await addressApi.setDefaultAddress(addressId)
        }
      }

      // 重置表单
      resetAddressForm()

      // 关闭表单
      showAddressForm.value = false

      // 重新获取地址列表 - 使用可选链操作符避免类型错误
      addressSelectorRef.value?.fetchAddresses?.()
      loadUserAddresses()
      // 刷新页面，确保地址列表更新
      // window.location.reload()
    } else {
      ElMessage.error(res.data.msg || t('address.error'))
    }
  } catch (error) {
    console.error(isEditingAddress.value ? '更新地址失败:' : '添加地址失败:', error)
    ElMessage.error(t('address.error'))
  }
}

// 地址选择器组件引用
const addressSelectorRef = ref<InstanceType<typeof AddressSelector> | null>(null)

// 创建订单前验证地址
const validateAddress = () => {
  if (!selectedAddressId.value || !selectedAddress.value) {
    showPaymentResult({
      status: 'error',
      title: 'Address Error',
      messageCn: '请选择收货地址',
      messageEn: 'Please select a shipping address',
      icon: AlertCircleIcon,
      iconClass: 'text-orange-500',
      textClass: 'text-orange-600 dark:text-orange-400',
    })
    return false
  }
  return true
}

// 控制支付界面对话框
const paymentDialogVisible = ref(false)

// 地址为空时的提示信息
const hasAddresses = computed(() => {
  return addresses.value && addresses.value.length > 0
})

// 中央添加地址按钮的显示状态
const showCentralAddressButton = ref(false)

// 地址列表
const addresses = ref<USAddress[]>([])

// 检查用户地址
const checkUserAddresses = async () => {
  try {
    // 获取当前用户ID
    const userStr = localStorage.getItem('user')
    if (!userStr) return

    const user = JSON.parse(userStr)
    const userId = user?.id
    if (!userId) return

    // 获取用户地址列表
    const res = await addressApi.getAddressList(userId)
    if (res.data.code === 1 && res.data.data) {
      addresses.value = res.data.data
      console.log('获取到的地址列表:', addresses.value)

      // 如果没有地址，显示中央添加地址按钮
      if (!addresses.value || addresses.value.length === 0) {
        showCentralAddressButton.value = true
      } else {
        showCentralAddressButton.value = false

        // 选择默认地址或第一个地址
        const defaultAddress = addresses.value.find((addr) => addr.Default === 1)
        if (defaultAddress) {
          selectedAddressId.value = defaultAddress.id
          selectedAddress.value = defaultAddress
        } else if (addresses.value.length > 0) {
          selectedAddressId.value = addresses.value[0].id
          selectedAddress.value = addresses.value[0]
        }
      }
    } else {
      // API调用失败，显示中央添加地址按钮
      showCentralAddressButton.value = true
    }
  } catch (error) {
    console.error('获取地址列表失败:', error)
    // 出错时显示中央添加地址按钮
    showCentralAddressButton.value = true
  }
}

// 选择地址
const selectAddress = (address: USAddress) => {
  selectedAddressId.value = address.id
  selectedAddress.value = address
}

// 格式化地址
const formatAddress = (address: USAddress): string => {
  if (!address) return ''

  const parts = [
    address.state,
    address.city,
    address.street,
    address.addressDetail,
    address.zipCode,
  ].filter(Boolean)

  return parts.join(', ')
}

// 处理图片加载错误
const handleImageError = (e: Event) => {
  const target = e.target as HTMLImageElement
  if (target) {
    target.src = '/placeholder.svg'
  }
}

// 获取商品图片URL
const getProductImage = () => {
  console.log('获取商品图片，当前product数据:', product.value)

  // 直接使用 productData 中的图片，因为这是我们在 onMounted 中设置的
  const imageUrl = productData.value.image || productData.value.pic || '/placeholder.svg'

  console.log('最终使用的图片URL:', imageUrl)
  return imageUrl
}

// 修改处理Stripe支付成功的函数
const handleStripePaymentSuccess = async (paymentIntentId: string) => {
  try {
    // 创建订单
    const orderData = await createOrder(paymentIntentId, 3) // 3表示银行卡/信用卡支付

    if (orderData) {
      // 显示成功结果
      showPaymentResult({
        status: 'success',
        title: '支付成功',
        messageCn: '您的订单已成功支付',
        messageEn: 'Your payment has been successfully processed',
        icon: CheckCircleIcon,
        iconClass: 'text-green-500',
        textClass: 'text-green-600 dark:text-green-400',
        orderId: orderData.id || paymentIntentId,
      })
    }
  } catch (error) {
    console.error('处理支付成功后出错:', error)
    showPaymentResult({
      status: 'error',
      title: '订单处理错误',
      messageCn: '支付成功，但订单处理失败',
      messageEn: 'Payment successful, but order processing failed',
      icon: AlertCircleIcon,
      iconClass: 'text-orange-500',
      textClass: 'text-orange-600 dark:text-orange-400',
    })
  }
}

// 修改处理微信支付成功的函数
const handleWechatPaymentSuccess = async (transactionId: string) => {
  try {
    // 关闭微信支付对话框
    wechatPayDialogVisible.value = false

    // 显示订单构建加载状态
    isCreatingOrder.value = true

    // 创建订单
    const orderData = await createOrder(transactionId, 1) // 1表示微信支付

    // 关闭订单构建加载状态
    isCreatingOrder.value = false

    if (orderData) {
      // 显示成功结果
      showPaymentResult({
        status: 'success',
        title: '支付成功',
        messageCn: '您的订单已成功支付',
        messageEn: 'Your payment has been successfully processed',
        icon: CheckCircleIcon,
        iconClass: 'text-green-500',
        textClass: 'text-green-600 dark:text-green-400',
        orderId: orderData.id || transactionId,
      })
    }
  } catch (error) {
    console.error('处理微信支付成功后出错:', error)

    // 关闭订单构建加载状态
    isCreatingOrder.value = false

    showPaymentResult({
      status: 'error',
      title: '订单处理错误',
      messageCn: '支付成功，但订单处理失败',
      messageEn: 'Payment successful, but order processing failed',
      icon: AlertCircleIcon,
      iconClass: 'text-orange-500',
      textClass: 'text-orange-600 dark:text-orange-400',
    })
  }
}

// 添加处理支付宝支付成功的函数
const handleAlipayPaymentSuccess = async (transactionId: string) => {
  try {
    // 创建订单
    const orderData = await createOrder(transactionId, 2) // 2表示支付宝支付

    if (orderData) {
      // 显示成功结果
      showPaymentResult({
        status: 'success',
        title: '支付成功',
        messageCn: '您的订单已成功支付',
        messageEn: 'Your payment has been successfully processed',
        icon: CheckCircleIcon,
        iconClass: 'text-green-500',
        textClass: 'text-green-600 dark:text-green-400',
        orderId: orderData.id || transactionId,
      })
    }
  } catch (error) {
    console.error('处理支付宝支付成功后出错:', error)
    showPaymentResult({
      status: 'error',
      title: '订单处理错误',
      messageCn: '支付成功，但订单处理失败',
      messageEn: 'Payment successful, but order processing failed',
      icon: AlertCircleIcon,
      iconClass: 'text-orange-500',
      textClass: 'text-orange-600 dark:text-orange-400',
    })
  }
}

const validateEmail = () => {
  if (!paymentEmail.value || !/\S+@\S+\.\S+/.test(paymentEmail.value)) {
    emailError.value = 'Please enter a valid email address'
    return false
  }
  return true
}
</script>

<style scoped>
/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(100, 116, 139, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(100, 116, 139, 0.7);
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-thumb {
  background: rgba(71, 85, 105, 0.5);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 85, 105, 0.7);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.15),
    0 10px 10px -5px rgba(0, 0, 0, 0.08);
}

.dark .card-hover:hover {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.3),
    0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* Button effects */
.btn-glow {
  position: relative;
  overflow: hidden;
}

.btn-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.btn-glow:hover::before {
  left: 100%;
}

@keyframes qrScan {
  0% {
    top: 0;
    opacity: 0;
  }
  25% {
    opacity: 1;
  }
  75% {
    opacity: 1;
  }
  100% {
    top: 100%;
    opacity: 0;
  }
}

.animate-qrScan {
  animation: qrScan 2s ease-in-out infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-zoomIn {
  animation: zoomIn 0.3s ease-out forwards;
}

@keyframes ping-slow {
  0% {
    transform: scale(0.95);
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.animate-ping-slow {
  animation: ping-slow 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-spin-slow {
  animation: spin 3s linear infinite;
}

@keyframes bounce-subtle {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-bounce-subtle {
  animation: bounce-subtle 2s ease-in-out infinite;
}

.animation-delay-300 {
  animation-delay: 0.3s;
}

/* 订单构建加载动画样式 */
.order-building-loader {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto;
  perspective: 1000px;
}

.cube-container {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  animation: rotate3d 3s linear infinite;
}

.cube {
  position: absolute;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
  animation: float 2s ease-in-out infinite;
}

.cube-1 {
  top: 20px;
  left: 20px;
  animation-delay: 0s;
}

.cube-2 {
  top: 20px;
  right: 20px;
  animation-delay: 0.2s;
}

.cube-3 {
  bottom: 20px;
  right: 20px;
  animation-delay: 0.4s;
}

.cube-4 {
  bottom: 20px;
  left: 20px;
  animation-delay: 0.6s;
}

.cube-5 {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 0.8s;
}

.cube-6 {
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 1s;
}

.building-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100px;
  height: 100px;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  border: 3px solid transparent;
}

.ring-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid transparent;
  animation: ringPulse 2s ease-in-out infinite;
}

.ring-1 {
  border-top-color: #667eea;
  animation-delay: 0s;
}

.ring-2 {
  border-right-color: #764ba2;
  animation-delay: 0.5s;
}

.ring-3 {
  border-bottom-color: #667eea;
  animation-delay: 1s;
}

.ring-4 {
  border-left-color: #764ba2;
  animation-delay: 1.5s;
}

.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 50%;
  animation: particleFloat 3s ease-in-out infinite;
  animation-delay: var(--delay);
}

.particle:nth-child(1) {
  top: 10%;
  left: 20%;
}
.particle:nth-child(2) {
  top: 20%;
  left: 80%;
}
.particle:nth-child(3) {
  top: 30%;
  left: 10%;
}
.particle:nth-child(4) {
  top: 40%;
  left: 90%;
}
.particle:nth-child(5) {
  top: 50%;
  left: 15%;
}
.particle:nth-child(6) {
  top: 60%;
  left: 85%;
}
.particle:nth-child(7) {
  top: 70%;
  left: 25%;
}
.particle:nth-child(8) {
  top: 80%;
  left: 75%;
}
.particle:nth-child(9) {
  top: 90%;
  left: 35%;
}
.particle:nth-child(10) {
  top: 15%;
  left: 65%;
}
.particle:nth-child(11) {
  top: 45%;
  left: 45%;
}
.particle:nth-child(12) {
  top: 75%;
  left: 55%;
}

/* 进度指示器样式 */
.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  position: relative;
}

.step-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e2e8f0;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.step-indicator.active .step-dot {
  background: #10b981;
  border-color: #10b981;
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.2);
}

.step-indicator.processing .step-dot {
  background: #3b82f6;
  border-color: #3b82f6;
  animation: processingPulse 1.5s ease-in-out infinite;
}

.step-line {
  width: 40px;
  height: 2px;
  background: #e2e8f0;
  margin-top: 6px;
}

/* 动画定义 */
@keyframes rotate3d {
  0% {
    transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);
  }
  33% {
    transform: rotateX(120deg) rotateY(120deg) rotateZ(0deg);
  }
  66% {
    transform: rotateX(240deg) rotateY(240deg) rotateZ(120deg);
  }
  100% {
    transform: rotateX(360deg) rotateY(360deg) rotateZ(360deg);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10px) scale(1.1);
    opacity: 1;
  }
}

@keyframes ringPulse {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes particleFloat {
  0%,
  100% {
    transform: translateY(0px) scale(0.8);
    opacity: 0.4;
  }
  33% {
    transform: translateY(-15px) scale(1.2);
    opacity: 0.8;
  }
  66% {
    transform: translateY(-5px) scale(1);
    opacity: 0.6;
  }
}

@keyframes processingPulse {
  0%,
  100% {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0.4);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 响应式优化 */
@media (max-width: 640px) {
  .order-building-loader {
    width: 100px;
    height: 100px;
  }

  .cube {
    width: 16px;
    height: 16px;
  }

  .building-ring {
    width: 80px;
    height: 80px;
  }

  .step-indicator {
    font-size: 12px;
  }

  .step-line {
    width: 30px;
  }
}
</style>
