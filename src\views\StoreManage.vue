<template>
  <div class="max-w-4xl mx-auto p-6">
    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">店铺管理</h2>
        <el-button type="primary" @click="handleEdit" :loading="loading"> 编辑店铺 </el-button>
      </div>

      <el-descriptions :column="1" border>
        <el-descriptions-item label="店铺名称">
          {{ storeInfo.storeName }}
        </el-descriptions-item>
        <el-descriptions-item label="店铺照片">
          <img v-if="storeInfo.photo" :src="storeInfo.photo" class="w-24 h-24 rounded" />
          <span v-else>暂无照片</span>
        </el-descriptions-item>
        <el-descriptions-item label="店铺描述">
          {{ storeInfo.storeDescription || '暂无描述' }}
        </el-descriptions-item>
        <el-descriptions-item label="店铺地址">
          {{ storeInfo.storeAddress || '暂无地址' }}
        </el-descriptions-item>
        <el-descriptions-item label="店铺状态">
          <el-tag :type="storeInfo.storeStatus === StoreStatus.OPEN ? 'success' : 'info'">
            {{ storeInfo.storeStatus === StoreStatus.OPEN ? '营业中' : '休息中' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <div class="mt-6 flex justify-end">
        <el-button type="danger" @click="confirmDelete" :loading="deleteLoading">
          删除店铺
        </el-button>
      </div>
    </div>

    <!-- 编辑对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑店铺信息" width="500px">
      <el-form ref="editFormRef" :model="editForm" :rules="rules" label-width="120px">
        <el-form-item label="店铺名称" prop="storeName">
          <el-input v-model="editForm.storeName" />
        </el-form-item>
        <el-form-item label="店铺照片">
          <el-upload
            class="avatar-uploader"
            action="/api/upload"
            :show-file-list="false"
            :on-success="handlePhotoSuccess"
          >
            <img v-if="editForm.photo" :src="editForm.photo" class="w-24 h-24 rounded" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="店铺描述">
          <el-input v-model="editForm.storeDescription" type="textarea" />
        </el-form-item>
        <el-form-item label="店铺地址">
          <el-input v-model="editForm.storeAddress" />
        </el-form-item>
        <el-form-item label="店铺状态" prop="storeStatus">
          <el-radio-group v-model="editForm.storeStatus">
            <el-radio :label="StoreStatus.OPEN">营业中</el-radio>
            <el-radio :label="StoreStatus.CLOSED">休息中</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="handleUpdate" :loading="updateLoading">
            确认修改
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { dashboardIcons } from '@/components/icons/dashboard'
import { useRouter } from 'vue-router'
import { storeApi, StoreStatus } from '@/api/store'
import type { StoreDTO, StoreVO } from '@/api/store'

const router = useRouter()
const loading = ref(false)
const deleteLoading = ref(false)
const updateLoading = ref(false)
const showEditDialog = ref(false)
const editFormRef = ref<FormInstance>()

const storeInfo = ref<StoreVO>({} as StoreVO)
const editForm = reactive<StoreDTO>({} as StoreDTO)

// 获取店铺信息
const getStoreInfo = async () => {
  loading.value = true
  try {
    const storeId = storeApi.getCurrentStoreId()
    if (!storeId) {
      ElMessage.error('店铺不存在')
      return
    }
    const response = await storeApi.getStoreInfo(storeId)
    if (response.code === 1) {
      storeInfo.value = response.data
    }
  } catch (error) {
    console.error('Get store info error:', error)
    ElMessage.error('获取店铺信息失败')
  } finally {
    loading.value = false
  }
}

// 处理编辑
const handleEdit = () => {
  Object.assign(editForm, storeInfo.value)
  showEditDialog.value = true
}

// 处理更新
const handleUpdate = async () => {
  if (!editFormRef.value) return

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      updateLoading.value = true
      try {
        const response = await storeApi.updateStore(editForm)
        if (response.code === 1) {
          ElMessage.success('更新成功')
          showEditDialog.value = false
          getStoreInfo()
        }
      } catch (error) {
        console.error('Update store error:', error)
        ElMessage.error('更新失败')
      } finally {
        updateLoading.value = false
      }
    }
  })
}

// 确认删除
const confirmDelete = () => {
  ElMessageBox.confirm('确定要删除店铺吗？此操作不可恢复', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    deleteLoading.value = true
    try {
      const response = await storeApi.deleteStore(storeInfo.value.id)
      if (response?.data?.code === 1) {
        ElMessage.success('删除成功')
        localStorage.removeItem('storeId')
        router.push('/user')
      }
    } catch (error) {
      console.error('Delete store error:', error)
      ElMessage.error('删除失败')
    } finally {
      deleteLoading.value = false
    }
  })
}

onMounted(() => {
  getStoreInfo()
})
</script>

<style scoped>
.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 96px;
  height: 96px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}
</style>
