import {
  User,
  Edit,
  Delete,
  ShoppingBag,
  Heart,
  Settings,
  LogOut,
  Mail,
  Phone,
  CreditCard,
  Bell,
  Calendar,
  Star,
  Gift,
  Tag,
  Truck,
  Clock,
  Package,
  MapPin,
  Plus,
  Home,
  Search,
  Menu,
  ShoppingCart
} from 'lucide-vue-next'

export const icons = {
  UserIcon: User,
  EditIcon: Edit,
  DeleteIcon: Delete,
  OrderIcon: ShoppingBag,
  HeartIcon: Heart,
  TruckIcon: Truck,
  ClockIcon: Clock,
  PackageIcon: Package,
  MapPinIcon: MapPin,
  SettingsIcon: Settings,
  LogOutIcon: LogOut,
  MailIcon: Mail,
  PhoneIcon: Phone,
  WalletIcon: CreditCard,
  BellIcon: Bell,
  CalendarIcon: Calendar,
  StarIcon: Star,
  GiftIcon: Gift,
  TagIcon: Tag,
  PlusIcon: Plus,
  HomeIcon: Home,
  SearchIcon: Search,
  MenuIcon: Menu,
  CartIcon: ShoppingCart
}

export default {
  install(app) {
    Object.entries(icons).forEach(([name, component]) => {
      app.component(name, component)
    })
  }
}
