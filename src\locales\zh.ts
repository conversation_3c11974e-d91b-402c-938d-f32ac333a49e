export default {
  header: {
    language: '中文',
    userGuide: '更新公告',
    search: '搜索商品...',
    cart: '购物车',
    userInfo: '用户个人信息',
    login: '登录查看价格',
  },
  nav: {
    nbs: 'NBS精选',
    selected: '精选商品',
    newArrivals: '新品上市',
    bestSellers: '热销商品',
    allProducts: '全部商品',
    categories: {
      all: '全部',
      electronics: '电子产品',
      pets: '宠物用品',
      kitchen: '厨房用品',
      outdoor: '户外装备',
      home: '家居用品',
      fashion: '服装服饰',
      toys: '玩具娱乐',
      beauty: '美妆护肤',
      food: '食品饮料',
      mobile: '手机',
      laptop: '笔记本电脑',
      appliances: '家电',
      tv: '电视',
      refrigerator: '冰箱',
      kitchenAppliances: '厨房电器',
      clothing: '服装',
      menswear: '男装',
      womenswear: '女装',
      shoes: '鞋类',
      sneakers: '运动鞋',
      leatherShoes: '皮鞋',
      accessories: '饰品',
      jewelry: '珠宝',
      watches: '手表',
      books: '书籍',
      novels: '小说',
    },
  },
  cart: {
    title: '购物车',
    empty: '购物车是空的',
    total: '总计',
    checkout: '结算',
  },
  product: {
    buyNow: '立即购买',
    addToCart: '加入购物车',
    featured: '精选商品',
    all: '全部商品',
    categories: '商品分类',
  },
  auth: {
    register: '用户注册',
    login: '登录',
    accountName: '账号名',
    accountOrEmail: '账号或邮箱',
    accountOrEmailPlaceholder: '请输入账号名或邮箱',
    forgotUsername: '找回用户名',
    forgotUsernameTip: '忘记用户名或邮箱？',
    forgotPassword: '找回密码',
    forgotPasswordTip: '忘记密码？',
    password: '密码',
    gender: '性别',
    male: '男',
    female: '女',
    phone: '联系电话',
    email: '电子邮箱',
    registering: '注册中',
    registerSuccess: '注册成功',
    registerFailed: '注册失败',
    alreadyHaveAccount: '已有账号？',
    goToLogin: '去登录',
    loggingIn: '登录中',
    loginSuccess: '登录成功',
    loginFailed: '登录失败',
    noAccount: '还没有账号？',
    goToRegister: '去注册',
    pleaseInputAll: '请输入账号和密码',
    logout: '退出登录',
    userRegister: '用户注册',
    sellerRegister: '商家注册',
    terms: {
      agreement: '继续，即表示您同意',
      userAgreement: '用户协议',
      and: '和',
      privacyPolicy: '隐私政策',
    },
  },
  store: {
    startJourney: '开启您的电商之旅',
    createStore: '创建自己的店铺，开始在线销售之旅',
  },
}
