<template>
  <div class="space-y-4">
    <div v-if="!favorites.length" class="text-center py-8">
      <el-empty description="暂无收藏" />
    </div>

    <div
      v-for="item in favorites"
      :key="item.id"
      class="bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow"
    >
      <div class="flex justify-between items-start">
        <div class="flex-1">
          <h3 class="text-lg font-medium mb-2">
            <router-link
              :to="`/product/${item.productId}`"
              class="text-purple-600 hover:text-purple-800"
            >
              {{ item.productName }}
            </router-link>
          </h3>
          <p class="text-gray-600 text-sm">
            店铺：{{ item.storeName }}
          </p>
          <p class="text-gray-500 text-sm">
            收藏时间：{{ formatDate(item.collectTime) }}
          </p>
        </div>
        <el-button
          type="danger"
          link
          @click="handleCancelFavorite(item.id)"
        >
          取消收藏
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { favoritesApi } from '@/api/favorites'
import type { FavoritesVO } from '@/api/favorites'
import { formatDate } from '@/utils/format'

const favorites = ref<FavoritesVO[]>([])

const getFavorites = async () => {
  try {
    const userId = JSON.parse(localStorage.getItem('user') || '{}').id
    const response = await favoritesApi.getFavoritesList(userId)
    if (response.code === 1) {
      favorites.value = response.data
    }
  } catch (error) {
    console.error('Get favorites error:', error)
    ElMessage.error('获取收藏列表失败')
  }
}

const handleCancelFavorite = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要取消收藏吗？', '提示', {
      type: 'warning'
    })

    const response = await favoritesApi.deleteFavorite(id)
    if (response.code === 1) {
      ElMessage.success('取消成功')
      await getFavorites()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Cancel favorite error:', error)
      ElMessage.error('取消收藏失败')
    }
  }
}

onMounted(() => {
  getFavorites()
})
</script>
