import request from '@/utils/request'
import type { ApiResponse, MerchantRegisterResponse, SendCodeResponse } from './types'

// 商家注册接口的请求数据类型
export interface MerchantRegisterDTO {
  accountName: string
  password: string
  storeName: string
  businessLicense: string
  address: string
  phone: string
  email: string
  verificationCode: string
}

// 商家注册接口的响应类型
export interface MerchantRegisterResponse extends ApiResponse<null> {
  code: number
  msg: string
  data: null
}

// 发送验证码请求类型
export interface SendCodeDTO {
  phone: string
}

// 发送验证码响应类型
export interface SendCodeResponse extends ApiResponse<null> {
  code: number
  msg: string
  data: null
}

// 商家API
export const merchantApi = {
  // 商家注册
  register(data: MerchantRegisterDTO): Promise<MerchantRegisterResponse> {
    return request.post('/MerChant/register', data)
  },

  // 发送验证码
  sendCode(data: { phone: string }): Promise<SendCodeResponse> {
    return request.post('/merchant/sendCode', data)
  },
}
