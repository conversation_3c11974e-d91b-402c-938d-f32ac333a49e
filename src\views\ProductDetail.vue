<template>
  <div
    class="min-h-screen bg-gradient-to-br from-rose-50 via-violet-50 to-teal-50 transition-all duration-700"
  >
      <Teleport to="body">
      <div
        v-if="showAddressForm"
        class="fixed inset-0 z-[100] flex items-center justify-center bg-black/50 backdrop-blur-sm"
        @click.self="showAddressForm = false"
      >
        <div
          class="bg-white dark:bg-slate-800 rounded-2xl shadow-2xl w-full max-w-lg mx-4 transform transition-all duration-300 animate-fadeIn overflow-y-auto max-h-[90vh]"
        >
          <div
            class="p-6 border-b border-slate-200 dark:border-slate-700 flex justify-between items-center"
          >
            <h3
              class="text-xl font-bold bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent dark:from-blue-400 dark:to-indigo-400"
            >
              {{ isEditingAddress ? t('address.edit') : t('address.addNew') }}
            </h3>
            <button
              @click="showAddressForm = false"
              class="p-2 rounded-full hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors duration-200"
            >
              <svg
                class="w-5 h-5 text-slate-500 dark:text-slate-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <div class="p-6">
            <div class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div class="col-span-2 sm:col-span-1">
                  <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    {{ t('address.name') }} *
                  </label>
                  <input
                    v-model="addressForm.name"
                    type="text"
                    class="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
                    :class="{ 'border-red-500 dark:border-red-400': addressErrors.name }"
                    :placeholder="t('address.name')"
                  />
                  <p v-if="addressErrors.name" class="mt-1 text-sm text-red-500 dark:text-red-400">
                    {{ addressErrors.name }}
                  </p>
                  <p v-else class="mt-1 text-xs text-slate-500 dark:text-slate-400">
                    请输入收件人全名
                  </p>
                </div>

                <div class="col-span-2 sm:col-span-1">
                  <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    {{ t('address.phone') }} *
                  </label>
                  <input
                    v-model="addressForm.phoneNumber"
                    type="text"
                    class="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
                    :class="{ 'border-red-500 dark:border-red-400': addressErrors.phoneNumber }"
                    :placeholder="'(*************'"
                  />
                  <p
                    v-if="addressErrors.phoneNumber"
                    class="mt-1 text-sm text-red-500 dark:text-red-400"
                  >
                    {{ addressErrors.phoneNumber }}
                  </p>
                  <p v-else class="mt-1 text-xs text-slate-500 dark:text-slate-400">
                    格式：(************* 或 ************
                  </p>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div class="col-span-2 sm:col-span-1">
                  <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    {{ t('address.state') }} *
                  </label>
                  <select
                    v-model="addressForm.state"
                    class="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
                    :class="{ 'border-red-500 dark:border-red-400': addressErrors.state }"
                  >
                    <option value="" disabled>请选择州</option>
                    <option v-for="state in usStates" :key="state.code" :value="state.name">
                      {{ state.code }} - {{ state.name }}
                    </option>
                  </select>
                  <p v-if="addressErrors.state" class="mt-1 text-sm text-red-500 dark:text-red-400">
                    {{ addressErrors.state }}
                  </p>
                  <p v-else class="mt-1 text-xs text-slate-500 dark:text-slate-400">请选择美国州</p>
                </div>

                <div class="col-span-2 sm:col-span-1">
                  <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    {{ t('address.city') }} *
                  </label>
                  <input
                    v-model="addressForm.city"
                    type="text"
                    class="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
                    :class="{ 'border-red-500 dark:border-red-400': addressErrors.city }"
                    :placeholder="t('address.city')"
                  />
                  <p v-if="addressErrors.city" class="mt-1 text-sm text-red-500 dark:text-red-400">
                    {{ addressErrors.city }}
                  </p>
                  <p v-else class="mt-1 text-xs text-slate-500 dark:text-slate-400">
                    请输入城市名称
                  </p>
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                  {{ t('address.street') }} *
                </label>
                <input
                  v-model="addressForm.street"
                  type="text"
                  class="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
                  :class="{ 'border-red-500 dark:border-red-400': addressErrors.street }"
                  placeholder="123 Main St"
                />
                <p v-if="addressErrors.street" class="mt-1 text-sm text-red-500 dark:text-red-400">
                  {{ addressErrors.street }}
                </p>
                <p v-else class="mt-1 text-xs text-slate-500 dark:text-slate-400">
                  请输入街道地址，如：123 Main St
                </p>
              </div>

              <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                  {{ t('address.detail') }} *
                </label>
                <input
                  v-model="addressForm.addressDetail"
                  type="text"
                  class="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
                  :class="{ 'border-red-500 dark:border-red-400': addressErrors.addressDetail }"
                  placeholder="Apt 4B, Floor 2, etc."
                />
                <p
                  v-if="addressErrors.addressDetail"
                  class="mt-1 text-sm text-red-500 dark:text-red-400"
                >
                  {{ addressErrors.addressDetail }}
                </p>
                <p v-else class="mt-1 text-xs text-slate-500 dark:text-slate-400">
                  请输入详细地址，如：公寓号、楼层等
                </p>
              </div>

              <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                  {{ t('address.zipCode') }} *
                </label>
                <input
                  v-model="addressForm.zipCode"
                  type="text"
                  class="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent"
                  :class="{ 'border-red-500 dark:border-red-400': addressErrors.zipCode }"
                  placeholder="12345 或 12345-6789"
                />
                <p v-if="addressErrors.zipCode" class="mt-1 text-sm text-red-500 dark:text-red-400">
                  {{ addressErrors.zipCode }}
                </p>
                <p v-else class="mt-1 text-xs text-slate-500 dark:text-slate-400">
                  请输入5位数字邮编，或5位数字-4位数字格式
                </p>
              </div>

              <div class="flex items-center">
                <input
                  id="isDefaultAddress"
                  v-model="isDefaultAddress"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label
                  for="isDefaultAddress"
                  class="ml-2 text-sm text-slate-700 dark:text-slate-300"
                >
                  {{ t('address.setDefault') }}
                </label>
              </div>
            </div>

            <div class="mt-6 flex justify-end space-x-4">
              <button
                @click="showAddressForm = false"
                class="px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors duration-200"
              >
                {{ t('address.cancel') }}
              </button>
              <button
                @click="saveAddress"
                class="px-4 py-2 rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-md hover:shadow-lg transition-all duration-200"
              >
                {{ t('address.save') }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </Teleport>
    <!-- Loading Skeleton -->
    <div v-if="loading" class="animate-pulse">
      <div class="h-16 bg-white/70 backdrop-blur-xl border-b border-violet-100"></div>
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="h-8 bg-white/80 w-1/3 rounded-xl mb-6"></div>
        <div class="grid md:grid-cols-2 gap-8">
          <div class="aspect-square bg-white/80 rounded-2xl"></div>
          <div class="space-y-4">
            <div class="h-8 bg-white/80 w-3/4 rounded-xl"></div>
            <div class="h-12 bg-white/80 w-1/3 rounded-xl"></div>
            <div class="space-y-2">
              <div class="h-4 bg-white/80 w-full rounded-lg"></div>
              <div class="h-4 bg-white/80 w-5/6 rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div v-else class="relative">
      <!-- 浮动购物车按钮 -->
      <button
        v-show="!loading && cartItemCount > 0"
        @click="navigateToCart"
        class="fixed bottom-6 right-6 z-50 bg-gradient-to-r from-violet-600 to-indigo-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 group"
      >
        <div class="relative">
          <ShoppingCartIcon class="w-6 h-6 transition-transform group-hover:scale-110" />
          <span
            class="absolute -top-2 -right-2 bg-red-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center"
          >
            {{ cartItemCount }}
          </span>
        </div>
      </button>

      <!-- 导航栏 -->
      <nav
        class="sticky top-0 z-40 bg-white/70 backdrop-blur-xl border-b border-violet-100 shadow-sm"
        :class="{ 'shadow-lg': isScrolled }"
      >
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex items-center justify-between h-16">
            <div class="flex items-center space-x-4">
              <button
                @click="router.go(-1)"
                class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium text-violet-700 hover:bg-violet-50 hover:text-violet-800 transition-all duration-300 ease-in-out group"
              >
                <ArrowLeft class="h-4 w-4 mr-2 transition-transform group-hover:-translate-x-1" />
                返回
              </button>
              <div class="h-4 w-px bg-violet-200"></div>
              <span
                class="text-sm font-medium bg-gradient-to-r from-violet-600 to-indigo-600 text-transparent bg-clip-text"
              >
                商品详情
              </span>
            </div>
          </div>
        </div>
      </nav>

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Status Banner -->
        <TransitionGroup name="notification" tag="div" class="space-y-4 mb-4">
          <div
            v-if="product.publishStatus === 0"
            key="unpublished"
            class="bg-gradient-to-r from-red-50 to-rose-50 border-l-4 border-red-400 p-4 rounded-xl shadow-sm animate-fade-in"
          >
            <div class="flex items-center">
              <AlertTriangleIcon class="h-5 w-5 text-red-400 mr-2 animate-bounce" />
              <p class="text-sm font-medium text-red-700">该商品已下架，暂时无法购买</p>
            </div>
          </div>
          <div
            v-if="isNew"
            key="new"
            class="bg-gradient-to-r from-amber-50 to-yellow-50 border-l-4 border-amber-400 p-4 rounded-xl shadow-sm animate-fade-in"
          >
            <div class="flex items-center">
              <SparklesIcon class="h-5 w-5 text-amber-400 mr-2" />
              <p class="text-sm font-medium text-amber-700">新品上市</p>
            </div>
          </div>
        </TransitionGroup>

        <!-- 产品信息标签 -->
        <div
          class="mb-4 bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-lg shadow-violet-100/50 hover:shadow-xl transition-all duration-300"
        >
          <div class="flex flex-wrap gap-3">
            <span
              v-for="(tag, index) in productTags"
              :key="index"
              class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition-all duration-300 transform hover:scale-105"
              :class="tag.classes"
            >
              <component :is="tag.icon" class="w-3 h-3 mr-1" />
              {{ tag.text }}
            </span>
          </div>
        </div>

        <!-- 商品展示区域 - 优化布局 -->
        <div class="grid grid-cols-1 md:grid-cols-12 gap-6  items-start">
          <!-- 左侧图片部分 - 占据5列 -->
          <div class="md:col-span-5 flex flex-col  overflow-hidden ">
            <!-- 可滚动内容区 -->
            <div class="flex-1 overflow-y-auto custom-scrollbar pr-2 ml-2"> 
              <!-- 主图显示 -->
              <div
                class="relative overflow-hidden rounded-2xl shadow-lg shadow-violet-100/50 aspect-square bg-white group mb-4"
              >
                <img
                  :src="currentDisplayImage"
                  :alt="product.name"
                  class="w-full h-full object-contain transition-transform duration-500 group-hover:scale-105"
                  @click="openImagePreview(currentDisplayImage, currentImageIndex)"
                />

                <!-- Carousel Navigation -->
                <button
                  v-if="totalImages > 1"
                  @click.stop="prevImage"
                  class="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 backdrop-blur-sm p-2 rounded-full shadow-md hover:bg-violet-100 transition-all duration-300 z-20"
                  aria-label="Previous image"
                >
                  <ChevronLeftIcon class="w-5 h-5 text-violet-700" />
                </button>
                <button
                  v-if="totalImages > 1"
                  @click.stop="nextImage"
                  class="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 backdrop-blur-sm p-2 rounded-full shadow-md hover:bg-violet-100 transition-all duration-300 z-20"
                  aria-label="Next image"
                >
                  <ChevronRightIcon class="w-5 h-5 text-violet-700" />
                </button>

                <!-- Download Button -->
                <div
                  class="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20"
                >
                  <button
                    @click.stop="downloadImage(currentDisplayImage, product.name)"
                    class="bg-white/80 backdrop-blur-sm p-2 rounded-full shadow-lg hover:bg-violet-100 transition-all duration-300"
                    title="下载图片"
                  >
                    <svg
                      class="w-5 h-5 text-violet-700"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                      />
                    </svg>
                  </button>
                </div>

                <!-- Image Counter -->
                <div
                  class="absolute top-3 right-3 bg-black/50 text-white text-xs px-2 py-1 rounded-full z-20"
                >
                  {{ currentImageIndex + 1 }} / {{ totalImages }}
                </div>

                <!-- Status Badges -->
                <div class="absolute top-3 left-3 space-x-2 z-20">
                  <TransitionGroup name="badge" tag="div" class="inline-flex gap-2">
                    <span
                      v-if="product.publishStatus === 1"
                      key="on-sale"
                      class="inline-block bg-gradient-to-r from-violet-600 to-indigo-600 text-white px-3 py-1.5 text-xs font-medium rounded-full shadow-lg"
                    >
                      热销中
                    </span>
                    <span
                      v-if="isNew"
                      key="new-arrival"
                      class="inline-block bg-gradient-to-r from-amber-500 to-orange-500 text-white px-3 py-1.5 text-xs font-medium rounded-full shadow-lg"
                    >
                      新品
                    </span>
                  </TransitionGroup>
                </div>
              </div>

              <!-- 缩略图导航 -->
              <div class="grid grid-cols-5 gap-2 mb-4 pl-2 scale-200">
                <div class="grid grid-cols-5 gap-2" ref="thumbnailsRef">
                  <button
                    v-for="(image, index) in allImages"
                    :key="index"
                    @click="setCurrentImageIndex(index)"
                    class="aspect-square rounded-lg overflow-hidden transition-all duration-300 transform hover:scale-105 bg-white"
                    :class="
                      currentImageIndex === index
                        ? 'ring-2 ring-violet-500 shadow-md'
                        : 'opacity-70 hover:opacity-100'
                    "
                  >
                    <img
                      :src="image"
                      :alt="`${product.name} - 缩略图 ${index + 1}`"
                      class="w-full h-full object-cover"
                    />
                  </button>
                </div>
              </div>

              <!-- 图文介绍图片区 -->
              <div class="bg-white/80 rounded-xl p-4 shadow-sm max-h-[250px] overflow-y-auto" v-if="product.introductPics">
                <h3 class="text-lg font-semibold mb-3 flex items-center">
                  <PhotoIcon class="w-5 h-5 mr-2 text-violet-600" />
                  图文详情
                </h3>
                <div class="space-y-3">
                  <div
                    v-for="(imgUrl, index) in product.introductPics.split(',')"
                    :key="index"
                    class="rounded-lg overflow-hidden border border-gray-100"
                  >
                    <img
                      :src="imgUrl"
                      :alt="`${product.name}介绍图${index + 1}`"
                      class="w-full h-auto max-h-[500px] object-contain hover:shadow-md transition-shadow duration-300"
                      @click="openImagePreview(imgUrl, index)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧商品信息 - 占据7列 -->
          <div class="md:col-span-7">
            <div
              class="bg-white/80 backdrop-blur-sm rounded-2xl p-5 shadow-lg shadow-violet-100/50"
            >
              <h2
                class="text-2xl md:text-3xl font-bold bg-gradient-to-r from-violet-900 to-indigo-900 text-transparent bg-clip-text mb-3"
              >
                {{ product.name }}
              </h2>

              <!-- Price Section -->
              <div class="flex items-baseline space-x-2 mb-4">
                <div
                  class="text-3xl md:text-4xl font-bold text-transparent bg-gradient-to-r from-violet-600 to-indigo-600 bg-clip-text"
                >
                  ${{ formatPrice(product.price) }}
                </div>
              </div>

              <!-- Product Description 商品详情区域 -->
              <div
                class="bg-gradient-to-br from-violet-50/50 to-white p-4 rounded-xl prose prose-violet max-w-none max-h-[150px] overflow-y-auto custom-scrollbar mb-4"
              >
                <!-- 标题行（flex布局使按钮右对齐） -->
                <div class="flex items-center justify-between mb-2">
                  <h3 class="text-lg font-semibold flex items-center">
                    <InfoIcon class="w-5 h-5 mr-2 text-violet-600" />
                    商品详情
                  </h3>
                  <button
                  v-if="product.pdfDocument"
                    @click="showPdfModal = true"
                    class="inline-flex items-center justify-center px-4 py-2 bg-gradient-to-r from-violet-600 to-indigo-600 text-white rounded-lg text-sm shadow-md hover:shadow-lg transition-all duration-300 hover:scale-[1.02] whitespace-nowrap leading-none"
                  >
                    点击此处查看该商品详细文档
                  </button>
                </div>

                <!-- 详情内容 -->
                <div v-html="product.detailHtml"></div>
              </div>

              <!-- 属性选择区域 - 优化布局 -->
              <div>
                <span class="spec-prompt">请选择您需要的商品规格</span>
                <!-- 属性块区域 -->
                <div class="attribute-blocks">
                  <div
                    v-for="(attr, attrTypeIndex) in parsedAttributes"
                    :key="attrTypeIndex"
                    class="attribute-block"
                  >
                    <!-- 属性类型标题 -->
                    <span class="attribute-type">{{ attr.key }}</span>

                    <!-- 属性值区域 -->
                    <div class="attribute-grid"></div>
                    <!-- 商品属性区 -->
                    <div class="attribute-grid">
                      <div
                        v-for="(attrValue, attrValueIndex) in attr.value"
                        :key="attrValueIndex"
                        class="attribute-value"
                        @click="handelSelect(attr.key, attrValue)"
                      >
                        <div
                          class="attr-value"
                          :style="{
                            backgroundColor:
                              selectAttrList.some((item) => item.value === attrValue) ||
                              attr.value.length === 1
                                ? '#7c3aed'
                                : '',
                            color:
                              selectAttrList.some((item) => item.value === attrValue) ||
                              attr.value.length === 1
                                ? 'white'
                                : '',
                          }"
                        >
                          {{ attrValue }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="number-inventory">
                  <!-- 数量选择器 -->
                  <div class="quantity-selector">
                    <label class="quantity-label">数量</label>
                    <div class="quantity-controls">
                      <button
                        @click="decreaseQuantity"
                        class="quantity-btn minus-btn"
                        :disabled="quantity <= 1"
                      >
                        <MinusIcon class="quantity-icon" />
                      </button>
                      <input
                        type="number"
                        v-model="quantity"
                        min="1"
                        :max="product.inventory"
                        class="quantity-input"
                        @input="handleQuantityInput"
                      />
                      <button
                        @click="increaseQuantity"
                        class="quantity-btn plus-btn"
                        :disabled="quantity >= 99"
                      >
                        <PlusIcon class="quantity-icon" />
                      </button>
                    </div>
                  </div>
                  <!-- 当前库存 -->
                  <div class="product-inventory">
                    <span class="inventory-text">当前库存:</span>
                    <span class="inventory-number">{{
                      product.inventory === 0 || product.inventory === null
                        ? '暂无存货'
                        : product.inventory
                    }}</span>
                    <span
                      v-if="product.inventory !== 0 && product.inventory"
                      class="inventory-unit"
                      >{{ product.unit }}</span
                    >
                  </div>
                </div>
              </div>

              <!-- 产品规格 -->
              <div class="text-gray-700 mb-4">
                <h3 class="text-lg font-semibold mb-2 flex items-center">
                  <TagIcon class="w-5 h-5 mr-2 text-violet-600" />
                  商品属性
                </h3>
                <div
                  class="grid grid-cols-2 gap-3 bg-gradient-to-br from-violet-50/50 to-white p-4 rounded-xl"
                >
                  <div
                    v-for="(spec, index) in productSpecs"
                    :key="index"
                    class="flex items-center space-x-2 group"
                  >
                    <span
                      class="text-violet-600 group-hover:text-violet-700 transition-colors duration-300"
                    >
                      {{ spec.label }}：
                    </span>
                    <span class="group-hover:text-violet-900 transition-colors duration-300">
                      {{ spec.value }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 底部按钮组 -->
              <div class="grid grid-cols-2 gap-4">
                <button
                  @click="handleAddToCart"
                  class="bg-white border-2 border-violet-600 text-violet-600 hover:bg-violet-50 px-4 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:hover:scale-100 flex items-center justify-center"
                  :disabled="product.publishStatus === 0"
                >
                  <ShoppingCartIcon class="w-5 h-5 mr-2" />
                  加入购物车
                </button>

                <button
                  @click="handleBuyNow"
                  class="bg-violet-600 hover:bg-violet-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg disabled:opacity-50 disabled:hover:scale-100 flex items-center justify-center"
                  :disabled="product.publishStatus === 0"
                >
                  <ShoppingBagIcon class="w-5 h-5 mr-2" />
                  立即购买
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 产品保证 -->
        <div
          class="mt-6 bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg shadow-violet-100/50 hover:shadow-xl transition-all duration-300"
        >
          <h3 class="font-semibold text-xl text-gray-900 mb-4 flex items-center">
            <ShieldCheckIcon class="w-6 h-6 text-violet-600 mr-2" />
            商品保障
          </h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div
              v-for="(guarantee, index) in guarantees"
              :key="index"
              class="group p-3 rounded-xl bg-gradient-to-br from-violet-50/50 to-white transition-all duration-300 hover:scale-105 hover:shadow-md"
            >
              <div class="flex items-center space-x-3">
                <component
                  :is="guarantee.icon"
                  class="w-5 h-5 text-violet-600 transition-transform duration-300 group-hover:scale-110"
                />
                <span
                  class="text-sm font-medium text-gray-700 group-hover:text-violet-700 transition-colors duration-300"
                >
                  {{ guarantee.text }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Mobile Details -->
        <div v-if="product.detailMobileHtml" class="mt-6 md:hidden">
          <div class="prose prose-violet max-w-none" v-html="product.detailMobileHtml"></div>
        </div>

        <!-- 图像预览对话框 -->
        <Transition name="fade">
          <div
            v-if="showImagePreview"
            class="fixed inset-0 z-50 bg-black/95 backdrop-blur-sm flex items-center justify-center"
            @click="closeImagePreview"
            @keydown="handlePreviewKeydown"
            tabindex="0"
            ref="previewDialogRef"
          >
            <div class="relative w-full max-w-5xl p-4" @click.stop>
              <button
                @click="closeImagePreview"
                class="absolute top-4 right-4 text-white hover:text-gray-300 transition-all duration-300 p-2 rounded-full hover:bg-white/10"
              >
                <XIcon class="w-6 h-6" />
              </button>

              <!-- Preview Navigation -->
              <button
                v-if="totalImages > 1"
                @click="prevPreviewImage"
                class="absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 backdrop-blur-sm p-3 rounded-full hover:bg-white/30 transition-all duration-300 z-10"
              >
                <ChevronLeftIcon class="w-6 h-6 text-white" />
              </button>
              <button
                v-if="totalImages > 1"
                @click="nextPreviewImage"
                class="absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 backdrop-blur-sm p-3 rounded-full hover:bg-white/30 transition-all duration-300 z-10"
              >
                <ChevronRightIcon class="w-6 h-6 text-white" />
              </button>

              <!-- 添加下载按钮 -->
              <div class="absolute top-4 right-16 flex space-x-2">
                <button
                  @click.stop="downloadImage(currentPreviewImage, product.name)"
                  class="text-white hover:text-gray-300 transition-all duration-300 p-2 rounded-full hover:bg-white/10"
                  title="下载当前图片"
                >
                  <svg
                    class="w-6 h-6"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                    />
                  </svg>
                </button>
                <button
                  v-if="totalImages > 1"
                  @click.stop="downloadAllImages(product.name)"
                  class="text-white hover:text-gray-300 transition-all duration-300 p-2 rounded-full hover:bg-white/10"
                  title="下载所有图片"
                >
                  <svg
                    class="w-6 h-6"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </button>
              </div>

              <!-- Main Preview Image with Zoom Effect -->
              <div class="relative overflow-hidden rounded-lg">
                <img
                  :src="currentPreviewImage"
                  :alt="product.name"
                  class="max-h-[85vh] mx-auto object-contain rounded-lg transition-transform duration-500"
                  :style="{
                    transform: `scale(${zoomLevel})`,
                    transformOrigin: `${zoomOriginX} ${zoomOriginY}`,
                  }"
                  @mousemove="handlePreviewZoom"
                  @mouseleave="resetZoom"
                  @touchstart="enableMobileZoom"
                  @touchend="resetZoom"
                />
              </div>

              <!-- Image Counter -->
              <div class="absolute top-4 left-4 bg-black/50 text-white px-3 py-1 rounded-full">
                {{ currentPreviewIndex + 1 }} / {{ totalImages }}
              </div>

              <!-- Preview Gallery -->
              <div
                v-if="totalImages > 1"
                class="absolute bottom-4 left-1/2 transform -translate-x-1/2"
              >
                <div
                  class="flex space-x-2 bg-black/50 backdrop-blur-sm p-2 rounded-xl overflow-x-auto hide-scrollbar max-w-[90vw]"
                >
                  <div v-for="(image, index) in allImages" :key="index" class="relative group">
                    <button
                      class="w-16 h-16 rounded-lg overflow-hidden transition-transform duration-300 hover:scale-105"
                      :class="{ 'ring-2 ring-white': currentPreviewIndex === index }"
                      @click="setPreviewImage(index)"
                    >
                      <img
                        :src="image"
                        :alt="`预览图 ${index + 1}`"
                        class="w-full h-full object-cover"
                      />
                    </button>
                    <button
                      @click.stop="downloadImage(image, `${product.name}_${index + 1}`)"
                      class="absolute bottom-0 right-0 bg-black/50 p-1 rounded-bl-lg rounded-tr-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                      title="下载此图片"
                    >
                      <svg
                        class="w-3 h-3 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Transition>

        <!-- Share 对话框 -->
        <Transition name="fade">
          <div
            v-if="showShareDialog"
            class="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center"
            @click="closeShareDialog"
          >
            <div
              class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 transform transition-all duration-300 scale-100"
              @click.stop
            >
              <h3
                class="text-xl font-semibold mb-6 bg-gradient-to-r from-violet-600 to-indigo-600 text-transparent bg-clip-text"
              >
                分享商品
              </h3>
              <div class="grid grid-cols-4 gap-6">
                <button
                  v-for="platform in sharePlatforms"
                  :key="platform.name"
                  class="flex flex-col items-center space-y-2 p-4 rounded-xl hover:bg-violet-50 transition-all duration-300 group"
                  @click="handleShareTo(platform.name)"
                >
                  <component
                    :is="platform.icon"
                    class="w-7 h-7 transition-all duration-300 group-hover:scale-110"
                    :class="platform.iconClass"
                  />
                  <span
                    class="text-sm text-gray-600 group-hover:text-violet-600 transition-colors duration-300"
                  >
                    {{ platform.name }}
                  </span>
                </button>
              </div>
              <button
                @click="closeShareDialog"
                class="mt-8 w-full py-3 text-sm text-gray-500 hover:text-violet-600 transition-colors duration-300 rounded-xl hover:bg-violet-50"
              >
                取消
              </button>
            </div>
          </div>
        </Transition>

        <!-- 支付界面对话框 -->
        <el-dialog
          v-model="paymentDialogVisible"
          title="支付确认"
          width="500px"
          align-center
          :close-on-click-modal="false"
          destroy-on-close
        >
          <div class="payment-dialog-content">
            <!-- 订单信息摘要 -->
            <div class="order-summary mb-6 p-4 bg-violet-50 rounded-xl">
              <h3 class="text-lg font-medium text-violet-800 mb-3">订单信息</h3>
              <div class="flex items-center space-x-3 mb-3">
                <img :src="product.pic" alt="商品图片" class="w-16 h-16 object-cover rounded-lg" />
                <div>
                  <p class="font-medium text-gray-800">{{ product.name }}</p>
                  <div class="flex items-center text-sm text-gray-600">
                    <span>数量: {{ quantity }}</span>
                    <span class="mx-2">|</span>
                    <span v-for="(attr, index) in selectAttrList" :key="index">
                      {{ attr.key }}: {{ attr.value
                      }}{{ index < selectAttrList.length - 1 ? ', ' : '' }}
                    </span>
                  </div>
                </div>
              </div>
              <div class="text-right text-lg font-bold text-violet-700">
                总计: ${{ formatPrice(product.price * quantity) }}
              </div>
            </div>

            <!-- Stripe支付表单 -->
            <div class="stripe-container">
              <!-- 邮箱输入 -->
              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">邮箱地址</label>
                <input
                  v-model="paymentEmail"
                  type="email"
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-violet-500 focus:border-violet-500"
                  placeholder="<EMAIL>"
                  @input="emailError = ''"
                />
                <p v-if="emailError" class="mt-1 text-sm text-red-600">{{ emailError }}</p>
              </div>

              <!-- 卡片元素容器 -->
              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">卡片信息</label>
                <div
                  id="card-element"
                  class="p-4 border border-gray-300 rounded-lg bg-white"
                  :class="{ 'border-red-500': cardError }"
                ></div>
                <p v-if="cardError" class="mt-1 text-sm text-red-600">{{ cardError }}</p>
              </div>

              <!-- 支付按钮 -->
              <div class="flex justify-between items-center mt-6">
                <button
                  type="button"
                  @click="paymentDialogVisible = false"
                  class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  type="button"
                  @click="handleStripePayment"
                  class="px-6 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500 disabled:opacity-50"
                  :disabled="isProcessingPayment"
                >
                  <span v-if="isProcessingPayment" class="flex items-center">
                    <svg
                      class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      ></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    处理中...
                  </span>
                  <span v-else>确认支付 ${{ formatPrice(product.price * quantity) }}</span>
                </button>
              </div>
            </div>
          </div>
        </el-dialog>

        <!-- 支付结果弹窗 -->
        <el-dialog
          v-model="paymentResultVisible"
          :title="paymentResult.title"
          width="400px"
          align-center
          :close-on-click-modal="true"
          :show-close="true"
          destroy-on-close
        >
          <div class="payment-result-dialog">
            <!-- 图标区域 -->
            <div class="result-icon" :class="paymentResult.iconClass">
              <component :is="paymentResult.icon" class="w-16 h-16" />
            </div>

            <!-- 结果标题 -->
            <h3 class="result-title" :class="paymentResult.textClass">
              {{ paymentResult.title }}
            </h3>

            <!-- 中文详细信息 -->
            <p class="result-message-cn">
              {{ paymentResult.messageCn }}
            </p>

            <!-- 英文详细信息 -->
            <p class="result-message-en">
              {{ paymentResult.messageEn }}
            </p>

            <!-- 错误代码 (如果有) -->
            <p v-if="paymentResult.errorCode" class="result-error-code">
              错误代码 (Error Code): {{ paymentResult.errorCode }}
            </p>

            <!-- 操作按钮 -->
            <div class="result-actions">
              <button
                v-if="paymentResult.status === 'success'"
                @click="viewOrder"
                class="action-button success-button"
              >
                查看订单 (View Order)
              </button>

              <button v-else @click="retryPayment" class="action-button retry-button">
                重试支付 (Retry)
              </button>

              <button @click="closePaymentResult" class="action-button close-button">
                {{ paymentResult.status === 'success' ? '继续购物 (Continue)' : '关闭 (Close)' }}
              </button>
            </div>
          </div>
        </el-dialog>

        <!-- Toast 通知 -->
        <TransitionGroup
          tag="div"
          name="notification"
          class="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50 space-y-2"
        >
          <div
            v-for="toast in toasts"
            :key="toast.id"
            class="bg-gradient-to-r px-6 py-3 rounded-full shadow-xl flex items-center space-x-2 text-white"
            :class="
              toast.type === 'success'
                ? 'from-violet-600 to-indigo-600'
                : toast.type === 'error'
                  ? 'from-red-600 to-rose-600'
                  : 'from-yellow-600 to-amber-600'
            "
          >
            <component :is="toast.icon" class="w-4 h-4" v-if="toast.icon" />
            <span class="font-medium">{{ toast.message }}</span>
          </div>
        </TransitionGroup>

        <!-- 支付方式选择对话框 -->
        <el-dialog
          v-model="showPaymentMethodDialog"
          title="选择支付方式"
          width="400px"
          align-center
          :close-on-click-modal="true"
          destroy-on-close
        >
          <div class="payment-method-dialog">
            <div class="payment-methods">
              <button class="payment-method-btn" @click="handlePaymentMethodSelect('wechat')">
                <img src="/icons/wechat-pay.svg" alt="微信支付" class="payment-icon" />
                <span>微信支付</span>
              </button>

              <button class="payment-method-btn" @click="handlePaymentMethodSelect('stripe')">
                <CreditCardIcon class="payment-icon credit-card-icon" />
                <span>信用卡支付</span>
              </button>
            </div>
            <button @click="showPaymentMethodDialog = false" class="cancel-payment-btn">
              取消
            </button>
          </div>
        </el-dialog>

        <!-- 微信支付对话框 -->
        <el-dialog
          v-model="wechatPayDialogVisible"
          title="微信支付"
          width="400px"
          align-center
          :close-on-click-modal="false"
          :show-close="true"
          destroy-on-close
          @closed="closeWechatPayDialog"
          class="wechat-pay-dialog-container"
        >
          <div class="wechat-pay-dialog">
            <!-- 加载状态 -->
            <div v-if="wechatPayLoading" class="wechat-pay-loading">
              <svg
                class="animate-spin h-10 w-10 text-violet-600"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              <p class="mt-4 text-gray-600">正在生成支付二维码...</p>
            </div>

            <!-- 二维码 -->
            <div v-else class="wechat-pay-content">
              <!-- 顶部品牌区域 -->
              <div class="pay-header">
                <div class="pay-brand">
                  <img src="/icons/wechat-pay.svg" alt="微信支付" class="pay-logo" />
                  <h3 class="pay-title">微信安全支付</h3>
                </div>
                <div
                  class="pay-status"
                  :class="{
                    'pay-status-success': orderStatus === 'SUCCESS',
                    'pay-status-expired': isExpired || orderStatus === 'EXPIRED',
                  }"
                >
                  {{
                    isExpired || orderStatus === 'EXPIRED'
                      ? '已过期'
                      : orderStatus === 'SUCCESS'
                        ? '支付成功'
                        : '等待支付'
                  }}
                </div>
              </div>

              <!-- 商品信息摘要 -->
              <div class="product-summary">
                <img :src="product.pic" alt="商品图片" class="product-thumbnail" />
                <div class="product-info">
                  <p class="product-name">{{ product.name }}</p>
                  <p class="product-attrs">
                    <span v-for="(attr, index) in selectAttrList" :key="index">
                      {{ attr.key }}: {{ attr.value
                      }}{{ index < selectAttrList.length - 1 ? ', ' : '' }}
                    </span>
                  </p>
                </div>
              </div>

              <div class="pay-content-container">
                <div class="qrcode-container">
                  <div
                    class="qrcode-wrapper"
                    :class="{
                      'qrcode-scanned': hasScanned,
                      'qrcode-expired': isExpired || orderStatus === 'EXPIRED',
                    }"
                  >
                    <img
                      v-if="wechatPayQrCode && !isExpired && orderStatus !== 'EXPIRED'"
                      :src="wechatPayQrCode"
                      alt="微信支付二维码"
                      class="wechat-qrcode"
                      :class="{ 'blur-qrcode': hasScanned }"
                    />
                    <div
                      v-if="hasScanned && !isExpired && orderStatus !== 'EXPIRED'"
                      class="scan-status-overlay"
                    >
                      <div v-if="isPaying" class="scan-status-message">
                        <RefreshCwIcon class="animate-spin h-8 w-8 text-green-500 mb-2" />
                        <span>正在支付中...</span>
                      </div>
                      <div v-else class="scan-status-message">
                        <CheckIcon class="h-8 w-8 text-green-500 mb-2" />
                        <span>已扫码</span>
                      </div>
                    </div>
                    <div
                      v-else-if="isExpired || orderStatus === 'EXPIRED'"
                      class="scan-status-overlay"
                    >
                      <div class="scan-status-message expired-message">
                        <ClockIcon class="h-8 w-8 text-amber-500 mb-2" />
                        <span>二维码已过期</span>
                        <button @click="initiateWechatPayment" class="refresh-btn">
                          <RefreshCwIcon class="h-4 w-4 mr-1" />
                          重新生成
                        </button>
                      </div>
                    </div>
                    <div v-else-if="!wechatPayQrCode" class="qrcode-error">
                      <AlertTriangleIcon class="h-12 w-12 text-amber-500" />
                      <p class="mt-2 text-gray-600">二维码加载失败</p>
                    </div>
                  </div>
                  <!-- 订单号显示 -->
                  <div class="order-number-display">
                    <span class="order-number-label">订单号:</span>
                    <span class="order-number-value">{{ currentOrderNumber }}</span>
                  </div>
                </div>

                <div class="payment-info">
                  <!-- 价格信息 -->
                  <div class="price-info">
                    <div class="original-price">
                      <span class="price-label">商品金额:</span>
                      <span class="price-value">${{ formatPrice(product.price * quantity) }}</span>
                    </div>
                    <div class="service-fee">
                      <span class="price-label">服务费(2%):</span>
                      <span class="price-value">${{ formatPrice(serviceFee) }}</span>
                    </div>
                    <div class="payment-amount-display">
                      <span class="price-label">应付金额:</span>
                      <span class="price-value">${{ formatPrice(paymentAmount) }}</span>
                    </div>
                    <!-- 添加人民币显示 -->
                    <div class="cny-amount">
                      <span class="cny-label">约</span>
                      <span class="cny-value">¥{{ formatCnyPrice(paymentAmount) }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 底部指引区域 -->
              <div class="pay-footer">
                <div class="pay-instructions">
                  <div class="instruction-icon-wrapper">
                    <SmartphoneIcon class="instruction-icon" />
                  </div>
                  <p class="instruction-text">
                    {{
                      isExpired || orderStatus === 'EXPIRED'
                        ? '二维码已过期，请重新生成'
                        : hasScanned
                          ? orderStatusDesc
                          : '请使用微信扫描二维码完成支付'
                    }}
                  </p>
                </div>
                <p class="service-fee-note">*包含2%平台服务费</p>

                <!-- 倒计时组件 -->
                <div class="pay-countdown" :class="{ 'countdown-critical': countdownTime < 60 }">
                  <ClockIcon class="countdown-icon" />
                  <span class="countdown-text">
                    {{
                      isExpired || orderStatus === 'EXPIRED'
                        ? '支付已超时'
                        : `支付有效期还剩 ${countdownDisplay}`
                    }}
                  </span>
                </div>
              </div>

              <!-- 安全提示 -->
              <div class="pay-security">
                <ShieldCheckIcon class="security-icon" />
                <span class="security-text">全程加密，安全支付</span>
              </div>
            </div>
          </div>
        </el-dialog>
            <el-dialog
      v-model="showPdfModal"
      title="商品详细介绍文档"
      width="82%"
      top="8vh"
      custom-class="pdf-preview-dialog"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div class="pdf-dialog-container">
        <!-- PDF预览区域 -->
        <div class="pdf-viewport">
          <vue-pdf-embed
            :source="pdfSource"
            :page="currentPage"
            :scale="scale"
            @loaded="handleDocumentLoad"
          />
        </div>

        <!-- 控制工具栏 -->
        <div class="pdf-controls">
          <div class="page-navigation">
            <el-button size="small" :disabled="currentPage <= 1" @click="prevPage" class="mr-2">
              <ArrowLeftIcon class="w-4 h-4 mr-1" />
              上一页
            </el-button>

            <span class="page-info"> 第 {{ currentPage }} 页 / 共 {{ pageCount }} 页 </span>

            <el-button
              size="small"
              :disabled="currentPage >= pageCount"
              @click="nextPage"
              class="ml-2"
            >
              下一页
              <ArrowRightIcon class="w-4 h-4 ml-1" />
            </el-button>
          </div>

          <div class="toolbar-right">


            <el-button size="small" type="primary" @click="downloadPdf" class="download-btn">
              下载
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { cartApi } from '@/api/cart'
import type { ProductVO } from '@/types/product'
import request from '@/utils/request'
import type { Stripe, StripeCardElement, StripeError } from '@stripe/stripe-js'
import { loadStripe } from '@stripe/stripe-js'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  AlertCircle as AlertCircleIcon,
  AlertTriangle as AlertTriangleIcon,
  ArrowLeft,
  BadgeCheck as BadgeCheckIcon,
  CheckCircle as CheckCircleIcon,
  Check as CheckIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Circle as CircleIcon,
  Clock as ClockIcon,
  CreditCard as CreditCardIcon,
  Hash as HashIcon,
  Info as InfoIcon,
  Minus as MinusIcon,
  Package as PackageIcon,
  Plus as PlusIcon,
  RefreshCw as RefreshCwIcon,
  ServerCrash as ServerCrashIcon,
  Share as ShareIcon,
  ShieldCheck as ShieldCheckIcon,
  ShoppingBag as ShoppingBagIcon,
  ShoppingCart as ShoppingCartIcon,
  Smartphone as SmartphoneIcon,
  Sparkles as SparklesIcon,
  Tag as TagIcon,
  Truck as TruckIcon,
  XCircle as XCircleIcon,
  X as XIcon,
} from 'lucide-vue-next'
import type { Component } from 'vue'
import { computed, nextTick, onMounted, onUnmounted, reactive, ref, watchEffect } from 'vue'
import { useRoute, useRouter } from 'vue-router'
// 在script部分的import区域添加qrcode库
import QRCode from 'qrcode'
import VuePdfEmbed from 'vue-pdf-embed'
import addressApi, { USAddress } from '@/api/address'

// 定义扩展的StripeError接口，包含可能存在的requestId属性
interface ExtendedStripeError extends StripeError {
  requestId?: string
}

// Types
interface ProductAttr {
  key: string
  value: string | string[]
}

interface Toast {
  id: number
  message: string
  type: 'success' | 'error' | 'warning'
  icon?: Component
}

interface PaymentResult {
  status: 'success' | 'error' | 'pending'
  title: string
  messageCn: string
  messageEn: string
  icon: Component
  iconClass: string
  textClass: string
  errorCode?: string
  orderId?: string
}

// Router
const route = useRoute()
const router = useRouter()
const pdfSource = ref('')
const currentPage = ref(1)
const pageCount = ref(0)
const scale = ref(1)

// 地址选择相关
const selectedAddressId = ref<number>(0)
const selectedAddress = ref<USAddress | null>(null)
const showAddressForm = ref(false)
const isEditingAddress = ref(false)
const isDefaultAddress = ref(false)
const addressForm = reactive({
  id: undefined as number | undefined,
  name: '',
  phoneNumber: '',
  state: '',
  city: '',
  street: '',
  addressDetail: '',
  zipCode: '',
  Default: 0,
})
const handleDocumentLoad = (doc: any) => {
  pageCount.value = doc.numPages
}

const prevPage = () => {
  if (currentPage.value > 1) currentPage.value--
}

const nextPage = () => {
  if (currentPage.value < pageCount.value) currentPage.value++
}
// State
const loading = ref(true)
const product = ref<ProductVO>({
  id: 0,
  productSnapshotId: 0,
  brandId: 0,
  categoryId: 0,
  outProductId: '',
  name: '',
  pic: '',
  albumPics: '',
  publishStatus: 0,
  pdfDocument: '',
  sort: 0,
  price: 0,
  unit: '',
  inventory: 0,
  weight: 0,
  detailHtml: '',
  detailMobileHtml: '',
  brandName: '',
  productCategoryName: '',
  createBy: 0,
  createTime: '',
  updateBy: 0,
  updateTime: '',
  productAttr: '',
}) as any // 临时使用any类型以支持inventory字段
const showPdfModal = ref(false)
const quantity = ref(1)
const showImagePreview = ref(false)
const showShareDialog = ref(false)
const currentImageIndex = ref(0)
const currentPreviewIndex = ref(0)
const currentPreviewImage = ref('')
const zoomLevel = ref(1)
const zoomOriginX = ref('50%')
const zoomOriginY = ref('50%')
const thumbnailsRef = ref<HTMLElement | null>(null)
const previewDialogRef = ref<HTMLElement | null>(null)
const selectedAttrs = reactive<Record<string, string | string[]>>({})
const isScrolled = ref(false)
const cartItemCount = ref(0)
const toasts = ref<Toast[]>([])
// 控制支付界面对话框
const paymentDialogVisible = ref(false)
const paymentMode = ref('')
// 商品属性
interface StringKeyValuePair {
  key: string
  value: string
}
// 暂存当前用户选择的商品属性列表
const selectAttrList = ref<StringKeyValuePair[]>([])

const paymentEmail = ref('')
const emailError = ref('')
const cardError = ref('')
const isProcessingPayment = ref(false)
const stripeInstance = ref<Stripe | null>(null)
const cardElement = ref<StripeCardElement | null>(null)
// 支付结果相关
const paymentResultVisible = ref(false)
const paymentResult = reactive<PaymentResult>({
  status: 'success',
  title: '支付成功 (Payment Successful)',
  messageCn: '您的订单已成功支付',
  messageEn: 'Your payment has been successfully processed',
  icon: CheckCircleIcon,
  iconClass: 'success-icon',
  textClass: 'success-text',
})
const translations = {
  zh: {
    nav: {
      subtitle: '企业级支付网关',
      sslSecured: 'SSL 安全认证',
    },
    steps: {
      orderConfirmed: '订单确认',
      payment: '安全支付',
      complete: '完成',
    },
    order: {
      summary: '订单摘要',
      quantity: '数量',
      attributes: '规格',
      subtotal: '商品金额',
      serviceFee: '服务费',
      processingFee: '处理费',
      free: '免费',
      total: '总计',
    },
    address: {
      title: '收货地址',
      select: '请选择收货地址',
      add: '添加新地址',
      empty: '暂无收货地址',
      needAddress: '请添加一个收货地址以继续',
      addNew: '添加新地址',
      edit: '编辑地址',
      delete: '删除地址',
      deleteConfirm: '确定要删除此地址吗？',
      cancel: '取消',
      confirm: '确定',
      save: '保存地址',
      name: '收件人姓名',
      phone: '联系电话',
      state: '省/州',
      city: '城市',
      street: '街道',
      detail: '详细地址',
      zipCode: '邮政编码',
      setDefault: '设为默认地址',
      loading: '加载中...',
      addSuccess: '地址添加成功',
      updateSuccess: '地址更新成功',
      deleteSuccess: '地址删除成功',
      error: '操作失败',
      required: '此项为必填项',
      default: '默认',
    },
    security: {
      title: '安全支付',
      ssl: '256位SSL加密',
      pci: 'PCI DSS合规',
      fraud: '欺诈防护',
    },
    payment: {
      method: '支付方式',
      chooseMethod: '选择您的首选支付方式',
      sessionExpires: '会话过期时间',
      creditCard: '信用卡支付',
      cardTypes: 'Visa, Mastercard, Amex',
      wechatPay: '微信支付',
      alipay: '支付宝',
      emailAddress: '邮箱地址',
      cardholderName: '持卡人姓名',
      cardInformation: '卡片信息',
      saveCard: '保存此卡片以备将来使用',
      processing: '处理中...',
      pay: '支付',
      scanQR: '使用微信扫描二维码',
      wechatInstruction: '请使用微信扫描二维码完成支付',
      expiresIn: '过期时间',
      qrScanned: '二维码已扫描 - 请在微信中完成支付',
      qrExpired: '二维码已过期',
      qrExpiredDesc: '二维码已过期，请重新生成',
      generateNew: '生成新二维码',
      generatingQR: '正在生成微信支付二维码...',
      generateWechatQR: '生成微信支付二维码',
      alipayPayment: '支付宝支付',
      alipayRedirect: '您将被重定向到支付宝完成支付',
      continueAlipay: '继续使用支付宝',
      processingPayment: '正在处理您的支付',
      complete: '完成',
      doNotClose: '支付处理期间请勿关闭此窗口',
      orderId: '订单号',
      viewOrder: '查看订单详情',
      tryAgain: '重试',
    },
  },
  en: {
    nav: {
      subtitle: 'Enterprise Payment Gateway',
      sslSecured: 'SSL Secured',
    },
    steps: {
      orderConfirmed: 'Order Confirmed',
      payment: 'Payment',
      complete: 'Complete',
    },
    order: {
      summary: 'Order Summary',
      quantity: 'Quantity',
      attributes: 'Attributes',
      subtotal: 'Subtotal',
      serviceFee: 'Service Fee',
      processingFee: 'Processing Fee',
      free: 'FREE',
      total: 'Total',
    },
    address: {
      title: 'Shipping Address',
      select: 'Please select a shipping address',
      add: 'Add new address',
      empty: 'No shipping address',
      addNew: 'Add New Address',
      edit: 'Edit Address',
      delete: 'Delete Address',
      deleteConfirm: 'Are you sure you want to delete this address?',
      cancel: 'Cancel',
      confirm: 'Confirm',
      save: 'Save Address',
      name: 'Recipient Name',
      phone: 'Phone Number',
      state: 'State/Province',
      city: 'City',
      street: 'Street',
      detail: 'Address Detail',
      zipCode: 'ZIP Code',
      setDefault: 'Set as default address',
      loading: 'Loading...',
      addSuccess: 'Address added successfully',
      updateSuccess: 'Address updated successfully',
      deleteSuccess: 'Address deleted successfully',
      error: 'Operation failed',
      required: 'This field is required',
      default: 'Default',
    },
    security: {
      title: 'Secure Payment',
      ssl: '256-bit SSL encryption',
      pci: 'PCI DSS compliant',
      fraud: 'Fraud protection enabled',
    },
    payment: {
      method: 'Payment Method',
      chooseMethod: 'Choose your preferred payment option',
      sessionExpires: 'Session expires in',
      creditCard: 'Credit Card',
      cardTypes: 'Visa, Mastercard, Amex',
      wechatPay: 'WeChat Pay',
      alipay: 'Alipay',
      emailAddress: 'Email Address',
      cardholderName: 'Cardholder Name',
      cardInformation: 'Card Information',
      saveCard: 'Save this card for future purchases',
      processing: 'Processing...',
      pay: 'Pay',
      scanQR: 'Scan QR Code with WeChat',
      wechatInstruction: 'Please use WeChat to scan the QR code to complete payment',
      expiresIn: 'Expires in',
      qrScanned: 'QR Code scanned - Complete payment in WeChat',
      qrExpired: 'QR Code Expired',
      qrExpiredDesc: 'QR code has expired, please generate a new one',
      generateNew: 'Generate New QR Code',
      generatingQR: 'Generating WeChat Pay QR Code...',
      generateWechatQR: 'Generate WeChat Pay QR Code',
      alipayPayment: 'Alipay Payment',
      alipayRedirect: 'You will be redirected to Alipay to complete your payment',
      continueAlipay: 'Continue with Alipay',
      processingPayment: 'Processing Payment',
      complete: 'Complete',
      doNotClose: 'Please do not close this window during payment processing',
      orderId: 'Order ID',
      viewOrder: 'View Order Details',
      tryAgain: 'Try Again',
    },
  },
}
// 美国州列表
const usStates = [
  { code: 'AL', name: 'Alabama' },
  { code: 'AK', name: 'Alaska' },
  { code: 'AZ', name: 'Arizona' },
  { code: 'AR', name: 'Arkansas' },
  { code: 'CA', name: 'California' },
  { code: 'CO', name: 'Colorado' },
  { code: 'CT', name: 'Connecticut' },
  { code: 'DE', name: 'Delaware' },
  { code: 'FL', name: 'Florida' },
  { code: 'GA', name: 'Georgia' },
  { code: 'HI', name: 'Hawaii' },
  { code: 'ID', name: 'Idaho' },
  { code: 'IL', name: 'Illinois' },
  { code: 'IN', name: 'Indiana' },
  { code: 'IA', name: 'Iowa' },
  { code: 'KS', name: 'Kansas' },
  { code: 'KY', name: 'Kentucky' },
  { code: 'LA', name: 'Louisiana' },
  { code: 'ME', name: 'Maine' },
  { code: 'MD', name: 'Maryland' },
  { code: 'MA', name: 'Massachusetts' },
  { code: 'MI', name: 'Michigan' },
  { code: 'MN', name: 'Minnesota' },
  { code: 'MS', name: 'Mississippi' },
  { code: 'MO', name: 'Missouri' },
  { code: 'MT', name: 'Montana' },
  { code: 'NE', name: 'Nebraska' },
  { code: 'NV', name: 'Nevada' },
  { code: 'NH', name: 'New Hampshire' },
  { code: 'NJ', name: 'New Jersey' },
  { code: 'NM', name: 'New Mexico' },
  { code: 'NY', name: 'New York' },
  { code: 'NC', name: 'North Carolina' },
  { code: 'ND', name: 'North Dakota' },
  { code: 'OH', name: 'Ohio' },
  { code: 'OK', name: 'Oklahoma' },
  { code: 'OR', name: 'Oregon' },
  { code: 'PA', name: 'Pennsylvania' },
  { code: 'RI', name: 'Rhode Island' },
  { code: 'SC', name: 'South Carolina' },
  { code: 'SD', name: 'South Dakota' },
  { code: 'TN', name: 'Tennessee' },
  { code: 'TX', name: 'Texas' },
  { code: 'UT', name: 'Utah' },
  { code: 'VT', name: 'Vermont' },
  { code: 'VA', name: 'Virginia' },
  { code: 'WA', name: 'Washington' },
  { code: 'WV', name: 'West Virginia' },
  { code: 'WI', name: 'Wisconsin' },
  { code: 'WY', name: 'Wyoming' },
  { code: 'DC', name: 'District of Columbia' },
]
// 地址表单校验
const addressErrors = reactive({
  name: '',
  phoneNumber: '',
  state: '',
  city: '',
  street: '',
  addressDetail: '',
  zipCode: '',
})
const currentLanguage = ref(localStorage.getItem('language') || 'zh')

const t = (key: string) => {
  const keys = key.split('.')
  let value: any = translations[currentLanguage.value as keyof typeof translations]
  for (const k of keys) {
    value = value?.[k]
  }
  return value || key
}
const showPaymentMethodDialog = ref(false)

// Methods
const formatPrice = (price: number): string => {
  return price.toFixed(2)
}

// 添加人民币价格转换函数
const formatCnyPrice = (price: number): string => {
  // 使用更准确的汇率，可以根据实际情况调整
  const cnyRate = 7.23
  return (price * cnyRate).toFixed(2)
}

const parseProductAttr = (attrStr: string): ProductAttr[] => {
  try {
    const productAttrData = JSON.parse(attrStr)
    // 将旧预设商品的单属性字符串转化为数组，与新上传的商品保持一致，减少后续操作
    productAttrData.forEach((element: ProductAttr) => {
      if (!Array.isArray(element.value)) {
        element.value = [element.value]
      }
    })
    console.log('JSON解析结果', productAttrData)
    // 商品属性解析完毕后，自动将只有一项的属性类型添加到selectAttrList中
    productAttrData.forEach((element: ProductAttr) => {
      if (
        element.value.length === 1 &&
        !selectAttrList.value.some(
          (item) => item.key === element.key && item.value === element.value[0],
        )
      ) {
        selectAttrList.value.push({
          key: element.key,
          value: element.value[0],
        })
      }
    })
    return productAttrData as ProductAttr[]
  } catch (error) {
    console.error('解析商品属性失败:', error)
    return []
  }
}
// 校验规则
const validateName = (name: string) => {
  if (!name) return '姓名不能为空'
  if (name.length < 2) return '姓名至少需要2个字符'
  if (name.length > 50) return '姓名不能超过50个字符'
  return ''
}

const validatePhoneNumber = (phone: string) => {
  if (!phone) return '电话号码不能为空'
  // 美国电话号码格式：+1 XXX-XXX-XXXX 或 (XXX) XXX-XXXX 或 XXX-XXX-XXXX
  const phoneRegex = /^(\+1\s?)?(\(\d{3}\)|\d{3})[-.\s]?\d{3}[-.\s]?\d{4}$/
  if (!phoneRegex.test(phone)) return '请输入有效的美国电话号码格式'
  return ''
}

const validateState = (state: string) => {
  if (!state) return '州不能为空'
  // 美国州名或州缩写
  const stateRegex = /^[A-Za-z\s]{2,50}$/
  if (!stateRegex.test(state)) return '请输入有效的州名或州缩写'
  return ''
}

const validateCity = (city: string) => {
  if (!city) return '城市不能为空'
  const cityRegex = /^[A-Za-z\s\-'.]{2,50}$/
  if (!cityRegex.test(city)) return '请输入有效的城市名称'
  return ''
}

const validateStreet = (street: string) => {
  if (!street) return '街道不能为空'
  if (street.length < 3) return '街道地址至少需要3个字符'
  if (street.length > 100) return '街道地址不能超过100个字符'
  return ''
}

const validateAddressDetail = (detail: string) => {
  if (!detail) return '详细地址不能为空'
  if (detail.length > 200) return '详细地址不能超过200个字符'
  return ''
}

const validateZipCode = (zipCode: string) => {
  if (!zipCode) return '邮编不能为空'
  // 美国邮编格式：5位数字 或 5位数字-4位数字
  const zipRegex = /^\d{5}(-\d{4})?$/
  if (!zipRegex.test(zipCode)) return '请输入有效的美国邮编格式 (12345 或 12345-6789)'
  return ''
}
// 实时校验表单
const validateAddressForm = () => {
  addressErrors.name = validateName(addressForm.name)
  addressErrors.phoneNumber = validatePhoneNumber(addressForm.phoneNumber)
  addressErrors.state = validateState(addressForm.state)
  addressErrors.city = validateCity(addressForm.city)
  addressErrors.street = validateStreet(addressForm.street)
  addressErrors.addressDetail = validateAddressDetail(addressForm.addressDetail)
  addressErrors.zipCode = validateZipCode(addressForm.zipCode)

  // 返回是否所有字段都有效
  return !Object.values(addressErrors).some((error) => error !== '')
}
// 修改保存地址函数，添加表单校验
const saveAddress = async () => {
  try {
    // 表单验证
    if (!validateAddressForm()) {
      ElMessage.error('请修正表单中的错误')
      return
    }

    // 获取当前用户ID
    const userStr = localStorage.getItem('user')
    if (!userStr) {
      ElMessage.error(t('address.error'))
      return
    }

    const user = JSON.parse(userStr)
    const userId = user?.id

    if (!userId) {
      ElMessage.error(t('address.error'))
      return
    }

    // 准备保存的地址数据
    const addressData = {
      ...addressForm,
      userId,
      Default: isDefaultAddress.value ? 1 : 0,
    }

    console.log('保存地址数据:', addressData)

    const res = await addressApi.addAddress(addressData)

    if (res.data.code === 1) {
      console.log(res.data);
      
      ElMessage.success(
        isEditingAddress.value ? t('address.updateSuccess') : t('address.addSuccess'),
      )
      // 如果设为默认地址
      if (isDefaultAddress.value) {
        // 如果是编辑模式，使用现有ID；如果是新增模式，使用返回的ID
        const addressId = addressForm.id || (res.data.data && res.data.data.id)
        if (addressId) {
          await addressApi.setDefaultAddress(addressId)
        }
      }

      // 构建商品信息
      console.log('商品图片路径:', product.value.pic)
      console.log('商品所有图片:', allImages.value)

      const productInfo = {
        id: product.value.id,
        name: product.value.name,
        price: product.value.price,
        quantity: quantity.value,
        image: product.value.pic, // 直接使用主图
        attributes: selectAttrList.value,
        merchantId: product.value.merchantId,
        unit: product.value.unit,
      }

      console.log('传递的商品信息:', productInfo)

      // 跳转到支付页面
      router.push({
        name: 'payment',
        query: {
          products: JSON.stringify([productInfo]),
          orderInfo: JSON.stringify({
            totalAmount: product.value.price * quantity.value,
            directBuy: true,
          }),
        },
      })
      // 关闭表单
      showAddressForm.value = false

    } else {
      ElMessage.error(res.data.msg || t('address.error'))
    }
  } catch (error) {
    console.error(isEditingAddress.value ? '更新地址失败:' : '添加地址失败:', error)
    ElMessage.error(t('address.error'))
  }
}
// 显示Toast消息
const showToast = (
  message: string,
  type: 'success' | 'error' | 'warning' = 'success',
  icon?: Component,
) => {
  const toast = {
    id: Date.now(),
    message,
    type,
    icon,
  }
  toasts.value.push(toast)
  setTimeout(() => {
    toasts.value = toasts.value.filter((t) => t.id !== toast.id)
  }, 3000)
}
// 下载pdf
const downloadPdf = () => {
  const link = document.createElement('a')
  link.href = pdfSource.value
  link.download = '商品介绍文档.pdf'
  link.click()
}
// Image Carousel Navigation
const setCurrentImageIndex = (index: number) => {
  currentImageIndex.value = index
}

const prevImage = () => {
  currentImageIndex.value = (currentImageIndex.value - 1 + totalImages.value) % totalImages.value
}

const nextImage = () => {
  currentImageIndex.value = (currentImageIndex.value + 1) % totalImages.value
}

// Preview Navigation
const setPreviewImage = (index: number) => {
  currentPreviewIndex.value = index
  currentPreviewImage.value = allImages.value[index]
  resetZoom()
}

const prevPreviewImage = () => {
  currentPreviewIndex.value =
    (currentPreviewIndex.value - 1 + totalImages.value) % totalImages.value
  currentPreviewImage.value = allImages.value[currentPreviewIndex.value]
  resetZoom()
}

const nextPreviewImage = () => {
  currentPreviewIndex.value = (currentPreviewIndex.value + 1) % totalImages.value
  currentPreviewImage.value = allImages.value[currentPreviewIndex.value]
  resetZoom()
}

// Handle keyboard navigation in preview
const handlePreviewKeydown = (e: KeyboardEvent) => {
  if (e.key === 'ArrowLeft') {
    prevPreviewImage()
  } else if (e.key === 'ArrowRight') {
    nextPreviewImage()
  } else if (e.key === 'Escape') {
    closeImagePreview()
  }
}

// Quantity Control
const handleQuantityInput = (e: Event) => {
  console.log(quantity.value)
  const input = e.target as HTMLInputElement
  let value = parseInt(input.value)
  if (isNaN(value) || value < 1) value = 1
  if (value > product.value.inventory) value = product.value.inventory
  quantity.value = value
}

const increaseQuantity = () => {
  if (quantity.value < product.value.inventory) quantity.value++
}

const decreaseQuantity = () => {
  if (quantity.value > 1) quantity.value--
}

// 属性选择
// const selectAttr = (name: string, value: string) => {
//   selectedAttrs[name] = value
// }

// 图像预览与缩放
const openImagePreview = (imageUrl: string, index: number) => {
  currentPreviewImage.value = imageUrl
  currentPreviewIndex.value = index
  showImagePreview.value = true
  document.body.style.overflow = 'hidden'

  // 将对话集中在键盘导航上
  nextTick(() => {
    if (previewDialogRef.value) {
      previewDialogRef.value.focus()
    }
  })
}

const closeImagePreview = () => {
  showImagePreview.value = false
  document.body.style.overflow = ''
  resetZoom()
}

const handlePreviewZoom = (e: MouseEvent) => {
  const container = e.currentTarget as HTMLElement
  const { left, top, width, height } = container.getBoundingClientRect()

  // Calculate mouse position as percentage
  const x = ((e.clientX - left) / width) * 100
  const y = ((e.clientY - top) / height) * 100

  // Set zoom origin to mouse position
  zoomOriginX.value = `${x}%`
  zoomOriginY.value = `${y}%`

  // Set zoom level
  zoomLevel.value = 2.5
}

const enableMobileZoom = () => {
  zoomLevel.value = 2
}

const resetZoom = () => {
  zoomLevel.value = 1
  zoomOriginX.value = '50%'
  zoomOriginY.value = '50%'
}

// Share Functionality

const closeShareDialog = () => {
  showShareDialog.value = false
  document.body.style.overflow = ''
}

const handleShareTo = (platform: string) => {
  showToast(`已分享到${platform}`, 'success', CheckIcon)
  closeShareDialog()
}
const findMissingKeys = (arr1: StringKeyValuePair[], arr2: ProductAttr[]): string[] => {
  return arr2
    .filter((item2) => !arr1.some((item1) => item1.key === item2.key))
    .map((item) => item.key)
}
// Cart & Purchase
const handleAddToCart = async () => {
  if (!validateSelection()) return

  // 验证用户是否登录
  const token = localStorage.getItem('token')
  if (!token) {
    showToast('请先登录才能添加到购物车', 'warning', AlertCircleIcon)
    // 显示登录确认对话框
    ElMessageBox.confirm('需要登录才能添加到购物车，是否前往登录？', '提示', {
      confirmButtonText: '前往登录',
      cancelButtonText: '继续浏览',
      type: 'warning',
    })
      .then(() => {
        // 保存当前页面URL，登录后可以返回
        localStorage.setItem('redirectPath', router.currentRoute.value.fullPath)
        router.push('/login')
      })
      .catch(() => {
        // 用户选择继续浏览，不做任何跳转
      })
    return
  }
  if (selectAttrList.value.length < parsedAttributes.value.length) {
    ElMessage.error(
      `请指定商品的${findMissingKeys(selectAttrList.value, parsedAttributes.value)}属性`,
    )
    return
  }

  try {
    // 获取用户信息
    const userStr = localStorage.getItem('user')
    const user = userStr ? JSON.parse(userStr) : null
    const userId = user?.id
    if (!userId) {
      showToast('用户信息异常', 'error', AlertCircleIcon)
      return
    }
    // 构造购物车数据
    const cartData = {
      buyerId: userId,
      productId: product.value.id.toString(),
      productName: product.value.name,
      number: quantity.value,
      amount: product.value.price * quantity.value,
      price: product.value.price,
      image: product.value.pic,
      attrs: JSON.stringify(selectAttrList.value),
    }
    // // 添加属性信息
    // if (Object.keys(selectedAttrs).length > 0) {
    //   cartData.attrs = JSON.stringify(selectedAttrs)
    // }

    console.log('购物车信息：', cartData)
    // 调用购物车API
    const res = await cartApi.addToCart(cartData)

    if (res.data.code === 1) {
      showToast('已添加到购物车', 'success', CheckIcon)
      // 更新购物车数量
      cartItemCount.value++
    } else {
      showToast(res.data.msg || '添加失败', 'error', AlertCircleIcon)
    }
  } catch (error) {
    console.error('添加购物车失败:', error)
    showToast('添加失败，请重试', 'error', AlertCircleIcon)
  }
}

const handleBuyNow = async () => {
  if (!validateSelection()) return

  // 验证用户是否登录
  const token = localStorage.getItem('token')
  if (!token) {
    showToast('请先登录才能购买商品', 'warning', AlertCircleIcon)
    // 显示登录确认对话框
    ElMessageBox.confirm('需要登录才能购买商品，是否前往登录？', '提示', {
      confirmButtonText: '前往登录',
      cancelButtonText: '继续浏览',
      type: 'warning',
    })
      .then(() => {
        // 保存当前页面URL，登录后可以返回
        localStorage.setItem('redirectPath', router.currentRoute.value.fullPath)
        router.push('/login')
      })
      .catch(() => {
        // 用户选择继续浏览，不做任何跳转
      })
    return
  }
  // 判断用户是否选择完毕商品属性
  if (selectAttrList.value.length < parsedAttributes.value.length) {
    ElMessage.error(
      `请指定商品的${findMissingKeys(selectAttrList.value, parsedAttributes.value)}属性`,
    )
    return
  }
  console.log('支付');
  
  showAddressForm.value=true
  // // 构建商品信息
  // console.log('商品图片路径:', product.value.pic)
  // console.log('商品所有图片:', allImages.value)

  // const productInfo = {
  //   id: product.value.id,
  //   name: product.value.name,
  //   price: product.value.price,
  //   quantity: quantity.value,
  //   image: product.value.pic, // 直接使用主图
  //   attributes: selectAttrList.value,
  //   merchantId: product.value.merchantId,
  //   unit: product.value.unit,
  // }

  // console.log('传递的商品信息:', productInfo)

  // // 跳转到支付页面
  // router.push({
  //   name: 'payment',
  //   query: {
  //     products: JSON.stringify([productInfo]),
  //     orderInfo: JSON.stringify({
  //       totalAmount: product.value.price * quantity.value,
  //       directBuy: true,
  //     }),
  //   },
  // })
}

// 处理支付方式选择
const handlePaymentMethodSelect = (method: 'stripe' | 'wechat') => {
  paymentMode.value = method
  showPaymentMethodDialog.value = false

  if (method === 'stripe') {
    // 显示Stripe支付对话框
    paymentDialogVisible.value = true
    // 初始化Stripe支付表单
    nextTick(() => {
      initStripeElements()
    })
  } else if (method === 'wechat') {
    // 调用微信支付接口
    initiateWechatPayment()
  }
}

// 微信支付相关状态
const wechatPayDialogVisible = ref(false)
const wechatPayQrCode = ref('')
const wechatPayLoading = ref(false)
const currentOrderNumber = ref('')
const paymentRetryCount = ref(0)
const maxRetryCount = 2
// 添加支付金额状态
const paymentAmount = ref(0)
const paymentAmountCny = ref(0)
// 添加服务费状态
const serviceFee = ref(0)
const serviceFeeRate = 0.02 // 2%服务费率
// 添加订单状态相关变量
const orderStatus = ref('')
const orderStatusDesc = ref('')
const hasScanned = ref(false)
const isPaying = ref(false)
// 添加倒计时相关变量
const countdownTime = ref(600) // 10分钟 = 600秒
const countdownDisplay = ref('10:00')
const isExpired = ref(false)
let countdownInterval: number | null = null
// 客户端秘钥
const clientSecret = ref('')
// 格式化倒计时时间
const formatCountdown = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

// 开始倒计时
const startCountdown = () => {
  // 清除可能存在的倒计时
  if (countdownInterval) {
    clearInterval(countdownInterval)
  }

  // 重置倒计时状态
  countdownTime.value = 1200
  countdownDisplay.value = '20:00'
  isExpired.value = false

  // 设置倒计时
  countdownInterval = window.setInterval(() => {
    if (countdownTime.value > 0) {
      countdownTime.value--
      countdownDisplay.value = formatCountdown(countdownTime.value)
    } else {
      // 倒计时结束，标记为过期
      isExpired.value = true
      wechatPayQrCode.value = '' // 清空二维码

      // 显示过期信息
      orderStatus.value = 'EXPIRED'
      orderStatusDesc.value = '二维码已过期，请重新生成'

      // 清除倒计时
      if (countdownInterval) {
        clearInterval(countdownInterval)
        countdownInterval = null
      }

      // 清除支付状态轮询
      if (paymentStatusInterval) {
        clearInterval(paymentStatusInterval)
        paymentStatusInterval = null
      }
    }
  }, 1000)
}

// 处理微信支付
const initiateWechatPayment = async () => {
  try {
    // 如果是新的支付请求，重置重试计数器
    if (!wechatPayDialogVisible.value) {
      paymentRetryCount.value = 0
    }

    wechatPayLoading.value = true
    wechatPayDialogVisible.value = true
    isExpired.value = false

    // 计算支付金额（添加2%服务费）
    const baseAmount = product.value.price * quantity.value
    serviceFee.value = parseFloat((baseAmount * serviceFeeRate).toFixed(2))
    paymentAmount.value = baseAmount + serviceFee.value
    paymentAmountCny.value = parseFloat((paymentAmount.value * 7.23).toFixed(2))

    // 生成唯一订单号，采用与后端一致的格式
    const generateOutTradeNo = () => {
      // 生成当前时间的格式化字符串：yyyyMMddHHmmss
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const seconds = String(now.getSeconds()).padStart(2, '0')
      const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`

      // 生成8位随机字符串
      const randomStr = Math.random().toString(36).substring(2, 10)

      // 组合订单号
      const baseNo = `GLOBAL_${timestamp}_${randomStr}`

      // 确保长度不超过32位（如果需要）
      const MAX_OUT_TRADE_NO_LENGTH = 32
      return baseNo.length <= MAX_OUT_TRADE_NO_LENGTH
        ? baseNo
        : baseNo.substring(0, MAX_OUT_TRADE_NO_LENGTH)
    }

    const outTradeNo = generateOutTradeNo()
    currentOrderNumber.value = outTradeNo

    // 构建支付数据 - 确保包含所有需要的字段
    const paymentData = {
      total: Math.round(paymentAmount.value * 100), // 转换为分
      title: `购买${product.value.name}`, // 订单标题
      outTradeNo: outTradeNo, // 商户订单号
      description: `${product.value.name} x ${quantity.value}${selectAttrList.value.length > 0 ? ' - ' + selectAttrList.value.map((attr) => `${attr.key}: ${attr.value}`).join(', ') : ''}`, // 商品描述
    }

    console.log('微信支付请求参数:', paymentData)

    // 使用接口获取支付信息
    const response = await request.post('/pay/wechat/native/qrcode', paymentData).catch((error) => {
      console.error('微信支付请求失败:', error)
      wechatPayLoading.value = false
      wechatPayDialogVisible.value = false
      return null
    })

    if (response && response.data && response.data.code === 1) {
      // 如果返回的是JSON数据
      if (response.data.data) {
        // 获取二维码URL - 适配实际返回的数据结构
        const codeUrl = response.data.data.codeUrl || response.data.data.code_url

        if (!codeUrl) {
          throw new Error('返回数据中没有二维码链接')
        }

        // 更新订单号（如果有返回）
        if (response.data.data.orderNo || response.data.data.outTradeNo) {
          currentOrderNumber.value = response.data.data.orderNo || response.data.data.outTradeNo
        }

        // 从响应中获取实际支付金额（如果有）
        if (response.data.data.totalFee) {
          // 如果返回的是分，转换为元
          paymentAmountCny.value = response.data.data.totalFee / 100
        }

        // 生成二维码
        try {
          const qrCodeDataUrl = await generateQRCode(codeUrl)
          wechatPayQrCode.value = qrCodeDataUrl
        } catch (qrError) {
          console.error('生成二维码失败:', qrError)
          wechatPayQrCode.value = codeUrl // 备用方案：直接显示链接
        }

        wechatPayLoading.value = false

        // 启动轮询检查支付状态
        startPollingPaymentStatus(currentOrderNumber.value)
      } else {
        throw new Error('返回数据格式不正确')
      }
    } else if (
      response &&
      response.status === 200 &&
      response.headers['content-type']?.includes('image')
    ) {
      // 如果返回的是图片数据
      const blob = new Blob([response.data], { type: 'image/png' })
      wechatPayQrCode.value = URL.createObjectURL(blob)
      wechatPayLoading.value = false

      // 启动轮询检查支付状态
      startPollingPaymentStatus(outTradeNo)
    } else {
      console.debug('获取二维码失败，无效的响应格式')
      // 检查是否需要重试
      if (paymentRetryCount.value < maxRetryCount) {
        paymentRetryCount.value++
        console.log(`支付二维码生成失败，正在进行第${paymentRetryCount.value}次重试...`)
        wechatPayLoading.value = false
        // 延迟1秒后重试
        setTimeout(() => {
          initiateWechatPayment()
        }, 1000)
        return
      } else {
        wechatPayLoading.value = false
        wechatPayDialogVisible.value = false
        ElMessage.error('支付功能繁忙，请稍后重试')
      }
    }

    // 启动倒计时
    startCountdown()
  } catch (error) {
    console.error('支付初始化失败:', error)
    // 检查是否需要重试
    if (paymentRetryCount.value < maxRetryCount) {
      paymentRetryCount.value++
      console.log(`支付初始化失败，正在进行第${paymentRetryCount.value}次重试...`)
      wechatPayLoading.value = false
      // 延迟1秒后重试
      setTimeout(() => {
        initiateWechatPayment()
      }, 1000)
      return
    } else {
      wechatPayLoading.value = false
      wechatPayDialogVisible.value = false
      ElMessage.error('支付功能繁忙，请稍后重试')
    }
  }
}

// 添加生成二维码的辅助函数
const generateQRCode = async (text: string): Promise<string> => {
  try {
    // 使用qrcode库生成二维码
    return await QRCode.toDataURL(text, {
      errorCorrectionLevel: 'H',
      margin: 1,
      width: 300,
      color: {
        dark: '#000000',
        light: '#ffffff',
      },
    })
  } catch (error) {
    console.error('生成二维码失败:', error)
    throw error
  }
}

// 轮询支付状态
let paymentStatusInterval: number | null = null
const startPollingPaymentStatus = (outTradeNo: string) => {
  if (paymentStatusInterval) {
    clearInterval(paymentStatusInterval)
  }

  // 每3秒检查一次支付状态
  paymentStatusInterval = window.setInterval(async () => {
    try {
      // 查询订单状态
      const response = await request
        .post(
          '/pay/wechat/order/query',
          {
            outTradeNo: outTradeNo,
          },
          {
            timeout: 5000,
          },
        )
        .catch((err) => {
          console.debug('查询支付状态请求失败:', err)
          return null
        })

      // 处理响应
      if (response && response.data && response.data.code === 1) {
        const orderInfo = response.data.data

        // 更新订单状态
        if (orderInfo) {
          orderStatus.value = orderInfo.trade_state || ''
          orderStatusDesc.value = orderInfo.trade_state_desc || ''

          // 根据状态更新UI
          switch (orderStatus.value) {
            case 'NOTPAY': // 未支付
              hasScanned.value = false
              isPaying.value = false
              break
            case 'USERPAYING': // 用户支付中
              hasScanned.value = true
              isPaying.value = true
              break
            case 'SUCCESS': // 支付成功
              if (paymentStatusInterval) {
                clearInterval(paymentStatusInterval)
              }
              wechatPayDialogVisible.value = false

              // 显示成功结果
              showPaymentResult({
                status: 'success',
                title: '支付成功 (Payment Successful)',
                messageCn: '您的订单已成功支付',
                messageEn: 'Your payment has been successfully processed',
                icon: CheckCircleIcon,
                iconClass: 'success-icon',
                textClass: 'success-text',
                orderId: outTradeNo,
              })
              break
            case 'CLOSED': // 已关闭
            case 'REVOKED': // 已撤销
            case 'PAYERROR': // 支付失败
              if (paymentStatusInterval) {
                clearInterval(paymentStatusInterval)
              }

              // 显示失败结果
              showPaymentResult({
                status: 'error',
                title: '支付失败 (Payment Failed)',
                messageCn: `支付失败: ${orderStatusDesc.value}`,
                messageEn: `Payment failed: ${orderStatusDesc.value}`,
                icon: XCircleIcon,
                iconClass: 'error-icon',
                textClass: 'error-text',
              })
              wechatPayDialogVisible.value = false
              break
          }
        }
      }
    } catch (error) {
      console.debug('查询支付状态处理错误:', error)
    }
  }, 3000)
}

// 关闭微信支付对话框时清除倒计时和轮询
const closeWechatPayDialog = () => {
  if (paymentStatusInterval) {
    clearInterval(paymentStatusInterval)
    paymentStatusInterval = null
  }

  if (countdownInterval) {
    clearInterval(countdownInterval)
    countdownInterval = null
  }

  wechatPayDialogVisible.value = false
  wechatPayQrCode.value = ''
  wechatPayLoading.value = false
  currentOrderNumber.value = ''
  isExpired.value = false
}

// 初始化Stripe元素
const initStripeElements = async () => {
  try {
    // 使用您的Stripe公钥异步加载和初始化 Stripe
    const stripe = await loadStripe(
      'pk_test_51RQg0KK18FZ5IMFpImFaV2WJV7sS5TmuG8Wt2YUiVmXMcn4oF4oKkV5xzIPhmV2XpwgUMgmJQzxh31GgDTjQYTFI00iiSBa4sL',
    )
    // stripe对象实例
    stripeInstance.value = stripe

    if (stripe) {
      // 创建Elements实例
      const elements = stripe.elements()
      // 创建Card元素
      const card = elements.create('card', {
        // paymentMethodOrder: ['card', 'unionpay'],
        style: {
          base: {
            color: '#32325d',
            fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
            fontSmoothing: 'antialiased',
            fontSize: '16px',
            '::placeholder': {
              color: '#aab7c4',
            },
          },
          invalid: {
            color: '#fa755a',
            iconColor: '#fa755a',
          },
        },
      })
      cardElement.value = card

      // 将返回的elements实例挂载卡片元素到DOM
      card.mount('#card-element')

      // 监听变化事件
      card.on('change', (event: { brand?: string; error?: { message: string } }) => {
        paymentMode.value = event.brand as string
        console.log('支付类型：', paymentMode.value)
        cardError.value = event.error ? event.error.message : ''
      })
    } else {
      showToast('支付系统初始化失败，请重试', 'error')
    }
  } catch (error) {
    console.error('初始化Stripe失败:', error)
    showToast('支付系统初始化失败，请重试', 'error')
  }
}

// 处理Stripe支付
const handleStripePayment = async () => {
  // 验证邮箱
  if (!paymentEmail.value) {
    emailError.value = '请输入邮箱地址'
    return
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(paymentEmail.value)) {
    emailError.value = '请输入有效的邮箱地址'
    return
  }

  // 开始处理支付
  isProcessingPayment.value = true

  try {
    if (!stripeInstance.value || !cardElement.value) {
      showToast('支付系统未初始化，请重试', 'error')
      return
    }

    // 创建支付方法
    const result = await stripeInstance.value.createPaymentMethod({
      type: 'card',
      card: cardElement.value, // 挂载的Stripe卡片Element实例
      billing_details: {
        email: paymentEmail.value, //绑定邮箱
      },
    })

    const paymentMethodError = result.error
    const paymentMethod = result.paymentMethod
    console.log('创建支付方法结果：', paymentMethod, paymentMethodError)

    // if (paymentMethodError) {
    //   // 显示错误信息
    //   cardError.value = paymentMethodError.message || '支付方法创建失败'
    //   showToast(paymentMethodError.message || '支付方法创建失败', 'error')
    //   return
    // }

    // if (!paymentMethod) {
    //   showToast('支付方法创建失败', 'error')
    //   return
    // }

    if (!paymentMethod) {
      // 显示错误信息
      cardError.value = '支付方法创建失败,请检查网络连接并稍后重试'
      showToast('支付方法创建失败,请检查网络连接并稍后重试', 'error')
      return
    }
    // 构建支付数据
    const paymentData = {
      paymentMethod: paymentMethod.id,
      amount: product.value.price * quantity.value,
      currency: 'usd',
      description: `购买商品: ${product.value.name}`,
      email: paymentEmail.value,
      metadata: {
        productId: product.value.id,
        quantity: quantity.value,
        attributes: JSON.stringify(selectAttrList.value),
      },
      // 生成唯一订单ID作为幂等键
      orderId: Number(Date.now() + '' + (Math.floor(Math.random() * 90000) + 10000)),
    }
    console.log('当前商品购买信息：', paymentData)

    // 向后端发送支付请求
    // const response = await request.post('/api/payments/create-payment-intent', paymentData)
    const response = await request.post('/pay/stripePay', paymentData)

    console.log('支付响应数据:', response.data)

    // 检查响应是否成功 (HTTP 200)
    if (response.status === 200) {
      // 检查返回的数据结构，识别Stripe API成功响应
      // Stripe API成功时会返回包含status字段的对象
      console.log('Stripe API成功响应:', response.data.data.clientSecret)
      clientSecret.value = response.data.data.clientSecret
      // 如果需要，将自动进行3D验证
      const { paymentIntent, error } = await stripeInstance.value.confirmPayment({
        clientSecret: clientSecret.value,
        confirmParams: {
          return_url: `${window.location.origin + window.location.pathname}`,
          payment_method: paymentMethod.id,
        },
        redirect: 'if_required', //自动进行3D验证
      })
      console.log('支付结果：', paymentIntent, error?.code)
      if (error) {
        showPaymentResult({
          status: 'error',
          title: '3D验证失败 (Verification Failed)',
          messageCn: error.message as string,
          messageEn: error.message as string,
          icon: XCircleIcon,
          iconClass: 'error-icon',
          textClass: 'error-text',
          errorCode: error.code,
        })
        paymentDialogVisible.value = false
      } else if (paymentIntent.status === 'succeeded') {
        console.log('不需要3DS认证')
        showPaymentResult({
          status: 'success',
          title: '支付成功 (Payment Successful)',
          messageCn: '您的订单已成功支付',
          messageEn: 'Your payment has been successfully processed',
          icon: CheckCircleIcon,
          iconClass: 'success-icon',
          textClass: 'success-text',
          orderId: `${paymentIntent.description}`,
        })
        paymentDialogVisible.value = false
      } else if (paymentIntent.status === 'processing') {
        // 启动轮询检查状态
        const pollInterval = setInterval(async () => {
          const result = await stripeInstance.value?.retrievePaymentIntent(clientSecret.value)
          if (!result) {
            clearInterval(pollInterval)
            showToast('无法获取支付状态', 'error')
            return
          }
          // 明确判断paymentIntent存在
          if (result?.paymentIntent && result.paymentIntent.status === 'succeeded') {
            clearInterval(pollInterval)
            showPaymentResult({
              status: 'success',
              title: '支付成功 (Payment Successful)',
              messageCn: '您的订单已成功支付',
              messageEn: 'Your payment has been successfully processed',
              icon: CheckCircleIcon,
              iconClass: 'success-icon',
              textClass: 'success-text',
              orderId: `${paymentIntent.description}`,
            })
            paymentDialogVisible.value = false
          } else if (result?.paymentIntent && result.paymentIntent.status === 'processing') {
            return
          }
          // 处理错误情况
          if (result.error) {
            clearInterval(pollInterval)
            showToast(String(result.error))
          }
        }, 5000) // 每5秒检查一次
      }
    }

    // if (
    //   response.data.status === 'succeeded' ||
    //   (response.data.paymentIntent && response.data.paymentIntent.status === 'succeeded') ||
    //   response.data.success
    // ) {
    //   // 支付直接成功
    //   paymentDialogVisible.value = false

    //   // 显示成功结果
    //   showPaymentResult({
    //     status: 'success',
    //     title: '支付成功 (Payment Successful)',
    //     messageCn: '您的订单已成功支付',
    //     messageEn: 'Your payment has been successfully processed',
    //     icon: CheckCircleIcon,
    //     iconClass: 'success-icon',
    //     textClass: 'success-text',
    //     orderId: response.data.orderId || response.data.id || response.data.paymentIntent?.id,
    //   })
    // } else if (
    //   response.data.requiresAction ||
    //   (response.data.paymentIntent && response.data.paymentIntent.status === 'requires_action') ||
    //   response.data.next_action
    // ) {
    //   // 需要3D Secure验证
    //   showToast('需要额外验证，请稍候...', 'warning')

    //   const clientSecret =
    //     response.data.clientSecret ||
    //     (response.data.paymentIntent && response.data.paymentIntent.client_secret)

    //   if (!clientSecret) {
    //     showToast('验证信息缺失，请重试', 'error')
    //     return
    //   }

    //   // 处理3D Secure验证
    //   const confirmResult = await stripeInstance.value.confirmCardPayment(clientSecret)

    //   const confirmError = confirmResult.error
    //   const paymentIntent = confirmResult.paymentIntent

    //   if (confirmError) {
    //     // 3D Secure验证失败
    //     paymentDialogVisible.value = false

    //     // 根据错误类型显示不同的错误信息
    //     handlePaymentError(confirmError)
    //   } else if (paymentIntent && paymentIntent.status === 'succeeded') {
    //     // 3D Secure验证成功
    //     paymentDialogVisible.value = false

    //     // 显示成功结果
    //     showPaymentResult({
    //       status: 'success',
    //       title: '验证成功 (Verification Successful)',
    //       messageCn: '您的支付已通过验证并成功处理',
    //       messageEn: 'Your payment has been verified and processed successfully',
    //       icon: CheckCircleIcon,
    //       iconClass: 'success-icon',
    //       textClass: 'success-text',
    //       orderId: paymentIntent.id,
    //     })
    //   } else {
    //     // 其他状态
    //     paymentDialogVisible.value = false

    //     showPaymentResult({
    //       status: 'error',
    //       title: '支付未完成 (Payment Incomplete)',
    //       messageCn: '您的支付尚未完成，请重试',
    //       messageEn: 'Your payment was not completed. Please try again.',
    //       icon: AlertCircleIcon,
    //       iconClass: 'warning-icon',
    //       textClass: 'warning-text',
    //     })
    //   }
    // } else if (response.data.error || response.data.last_payment_error) {
    //   // 处理明确的错误信息
    //   const errorMsg =
    //     response.data.error ||
    //     (response.data.last_payment_error && response.data.last_payment_error.message)
    //   const errorCode =
    //     response.data.code ||
    //     (response.data.last_payment_error && response.data.last_payment_error.code)

    //   paymentDialogVisible.value = false

    //   // 显示失败结果
    //   const { errorDescription } = handleUnknownError(errorCode, errorMsg)
    //   showPaymentResult({
    //     status: 'error',
    //     title: '支付失败 (Payment Failed)',
    //     messageCn: `支付处理失败: ${errorDescription}${errorMsg ? ` - ${errorMsg}` : ''}`,
    //     messageEn: errorMsg
    //       ? `Payment processing failed: ${errorMsg}`
    //       : 'Payment processing failed, please check your payment information',
    //     icon: XCircleIcon,
    //     iconClass: 'error-icon',
    //     textClass: 'error-text',
    //     errorCode: errorCode || 'unknown_error',
    //   })
    // } else {
    //   // 检查其他可能的成功响应格式
    //   if (response.data.id && response.data.object === 'payment_intent') {
    //     // 这是标准的Stripe PaymentIntent对象，直接检查status字段
    //     if (response.data.status === 'succeeded') {
    //       // 支付成功
    //       paymentDialogVisible.value = false

    //       // 显示成功结果
    //       showPaymentResult({
    //         status: 'success',
    //         title: '支付成功 (Payment Successful)',
    //         messageCn: '您的订单已成功支付',
    //         messageEn: 'Your payment has been successfully processed',
    //         icon: CheckCircleIcon,
    //         iconClass: 'success-icon',
    //         textClass: 'success-text',
    //         orderId: response.data.id,
    //       })
    //     } else if (
    //       response.data.status === 'requires_action' ||
    //       response.data.status === 'requires_confirmation'
    //     ) {
    //       // 需要额外操作
    //       showToast('需要额外验证，请稍候...', 'warning')

    //       if (!response.data.client_secret) {
    //         showToast('验证信息缺失，请重试', 'error')
    //         return
    //       }

    //       // 处理额外验证
    //       const confirmResult = await stripeInstance.value.confirmCardPayment(
    //         response.data.client_secret,
    //       )

    //       if (confirmResult.error) {
    //         // 验证失败
    //         paymentDialogVisible.value = false
    //         handlePaymentError(confirmResult.error)
    //       } else if (
    //         confirmResult.paymentIntent &&
    //         confirmResult.paymentIntent.status === 'succeeded'
    //       ) {
    //         // 验证成功
    //         paymentDialogVisible.value = false

    //         showPaymentResult({
    //           status: 'success',
    //           title: '验证成功 (Verification Successful)',
    //           messageCn: '您的支付已通过验证并成功处理',
    //           messageEn: 'Your payment has been verified and processed successfully',
    //           icon: CheckCircleIcon,
    //           iconClass: 'success-icon',
    //           textClass: 'success-text',
    //           orderId: confirmResult.paymentIntent.id,
    //         })
    //       }
    //     } else {
    //       // 其他状态
    //       paymentDialogVisible.value = false

    //       // 根据状态返回相应提示
    //       const statusMessages: Record<
    //         string,
    //         {
    //           title: string
    //           messageCn: string
    //           messageEn: string
    //           icon: Component
    //           iconClass: string
    //           textClass: string
    //         }
    //       > = {
    //         processing: {
    //           title: '支付处理中 (Payment Processing)',
    //           messageCn: '您的支付正在处理中，请稍后查看订单状态',
    //           messageEn: 'Your payment is being processed. Please check order status later.',
    //           icon: RefreshCwIcon,
    //           iconClass: 'warning-icon',
    //           textClass: 'warning-text',
    //         },
    //         canceled: {
    //           title: '支付已取消 (Payment Canceled)',
    //           messageCn: '您的支付已被取消',
    //           messageEn: 'Your payment has been canceled',
    //           icon: XCircleIcon,
    //           iconClass: 'error-icon',
    //           textClass: 'error-text',
    //         },
    //       }

    //       const status = response.data.status as string
    //       const statusInfo = statusMessages[status] || {
    //         title: '支付状态: ' + status,
    //         messageCn: `支付状态: ${status}，请查看订单确认状态`,
    //         messageEn: `Payment status: ${status}. Please check your order confirmation.`,
    //         icon: AlertCircleIcon,
    //         iconClass: 'warning-icon',
    //         textClass: 'warning-text',
    //       }

    //       showPaymentResult({
    //         status: 'pending',
    //         ...statusInfo,
    //       })
    //     }
    //   } else {
    //     // 无法识别的响应，但HTTP状态是200，可能是成功的
    //     console.warn('收到未识别的支付响应，但HTTP状态是200:', response.data)

    //     // 由于HTTP状态是200，我们假设支付可能成功
    //     paymentDialogVisible.value = false

    //     // 显示成功结果
    //     showPaymentResult({
    //       status: 'success',
    //       title: '支付可能成功 (Payment Likely Successful)',
    //       messageCn:
    //         '系统收到了支付响应，但无法确认最终状态。您的订单可能已成功处理，请稍后查看订单状态。',
    //       messageEn:
    //         "We received a payment response, but couldn't confirm the final status. Your order may have been processed successfully. Please check your order status later.",
    //       icon: CheckCircleIcon,
    //       iconClass: 'success-icon',
    //       textClass: 'success-text',
    //     })
    //   }
    // }
  } catch (error: unknown) {
    console.error('支付处理错误:', error)
    paymentDialogVisible.value = false

    const errorMessage = error instanceof Error ? error.message : '支付过程中出现错误，请重试'

    // 显示错误结果
    showPaymentResult({
      status: 'error',
      title: '支付错误 (Payment Error)',
      messageCn: `支付处理过程中出现错误: ${errorMessage}`,
      messageEn: `An error occurred during payment processing: ${errorMessage}`,
      icon: XCircleIcon,
      iconClass: 'error-icon',
      textClass: 'error-text',
    })
  } finally {
    isProcessingPayment.value = false
  }
}

// 处理支付错误
const handlePaymentError = (error: StripeError) => {
  console.error('支付错误:', error)

  // 错误代码映射到中文描述
  const errorCodeTranslations: Record<
    string,
    {
      titleCn: string
      messageCn: string
      messageEn: string
      icon: Component
    }
  > = {
    card_declined: {
      titleCn: '卡片被拒绝',
      messageCn: '您的卡片被拒绝，可能是余额不足或卡片存在其他问题',
      messageEn:
        'Your card was declined. You may have insufficient funds or there might be other issues with your card.',
      icon: CreditCardIcon,
    },
    insufficient_funds: {
      titleCn: '余额不足',
      messageCn: '您的账户余额不足，请使用其他卡片或支付方式',
      messageEn: 'Your card has insufficient funds. Please use another card or payment method.',
      icon: CreditCardIcon,
    },
    expired_card: {
      titleCn: '卡片已过期',
      messageCn: '您的卡片已过期，请使用其他有效卡片',
      messageEn: 'Your card has expired. Please use another card.',
      icon: CreditCardIcon,
    },
    incorrect_cvc: {
      titleCn: '安全码错误',
      messageCn: '您输入的安全码(CVC)不正确',
      messageEn: "Your card's security code (CVC) is incorrect.",
      icon: CreditCardIcon,
    },
    processing_error: {
      titleCn: '处理错误',
      messageCn: '处理您的支付时出现错误，请稍后重试',
      messageEn: 'An error occurred while processing your payment. Please try again later.',
      icon: RefreshCwIcon,
    },
    authentication_required: {
      titleCn: '需要验证',
      messageCn: '您的卡片需要额外验证，但验证失败',
      messageEn: 'Your card requires authentication, but the authentication failed.',
      icon: AlertCircleIcon,
    },
    rate_limit: {
      titleCn: '请求频率过高',
      messageCn: '您的支付请求太频繁，请稍后再试',
      messageEn: 'Too many requests made to the API too quickly. Please try again later.',
      icon: ClockIcon,
    },
  }

  // 根据错误类型显示不同的错误信息
  if (error.type === 'card_error') {
    // 从错误消息中提取详细信息
    const errorMessage = error.message || ''
    const detailedReason = error.code ? `${errorMessage}; code: ${error.code}` : errorMessage
    // 使用扩展的StripeError类型
    const requestId = (error as ExtendedStripeError).requestId
      ? `; request-id: ${(error as ExtendedStripeError).requestId}`
      : ''

    // 获取对应的翻译，如果没有则使用通用信息
    const translation = error.code ? errorCodeTranslations[error.code] : null

    showPaymentResult({
      status: 'error',
      title: translation ? `${translation.titleCn} (Card Error)` : '卡片错误 (Card Error)',
      messageCn: translation ? translation.messageCn : `支付被拒绝: ${errorMessage}`,
      messageEn: detailedReason + requestId,
      icon: translation ? translation.icon : CreditCardIcon,
      iconClass: 'error-icon',
      textClass: 'error-text',
      errorCode: error.code || 'card_error',
    })
  } else if (error.type === 'validation_error') {
    showPaymentResult({
      status: 'error',
      title: '验证错误 (Validation Error)',
      messageCn: '支付信息验证失败，请检查您输入的信息',
      messageEn: error.message || 'Payment information validation failed, please check your input',
      icon: AlertCircleIcon,
      iconClass: 'error-icon',
      textClass: 'error-text',
      errorCode: error.code || 'validation_error',
    })
  } else if (error.type === 'api_error') {
    showPaymentResult({
      status: 'error',
      title: '服务错误 (API Error)',
      messageCn: '支付服务暂时不可用，请稍后再试',
      messageEn: error.message || 'Payment service is temporarily unavailable',
      icon: ServerCrashIcon,
      iconClass: 'error-icon',
      textClass: 'error-text',
      errorCode: error.code || 'api_error',
    })
  } else {
    showPaymentResult({
      status: 'error',
      title: '支付错误 (Payment Error)',
      messageCn: error.message ? `支付处理失败: ${error.message}` : '支付处理过程中出现错误',
      messageEn: error.message
        ? `Payment processing failed: ${error.message}`
        : 'An error occurred during payment processing',
      icon: XCircleIcon,
      iconClass: 'error-icon',
      textClass: 'error-text',
      errorCode: error.code || 'unknown_error',
    })
  }
}

// 显示支付结果
const showPaymentResult = (result: PaymentResult) => {
  Object.assign(paymentResult, result)
  paymentResultVisible.value = true
}

// 关闭支付结果
const closePaymentResult = () => {
  paymentResultVisible.value = false
}

// 查看订单
const viewOrder = () => {
  if (paymentResult.orderId) {
    // 保存临时订单数据到localStorage
    const tempOrderData = {
      paymentMode: paymentMode.value,
      productImg: product.value.pic,
      productName: product.value.name,
      productAttributes: selectAttrList.value
        .map((item) => `${item.key}: ${item.value}`)
        .join(', '),
      productQuantity: quantity.value,
      productPrice: product.value.price,
      orderTime: new Date().toISOString(),
    }
    localStorage.setItem('tempOrderData', JSON.stringify(tempOrderData))

    // 跳转到订单确认页面
    router.push({
      path: '/order/confirmation',
      query: { orderId: paymentResult.orderId },
    })
  }
  paymentResultVisible.value = false
}

// 重试支付
const retryPayment = () => {
  paymentResultVisible.value = false
  paymentDialogVisible.value = true

  // 保留之前填写的邮箱地址，不需要用户重新输入
  // cardElement会自动重置，所以不需要额外处理

  // 重置卡片错误提示
  cardError.value = ''
  emailError.value = ''
}

const validateSelection = () => {
  if (product.value.productAttr) {
    const attrs = parseProductAttr(product.value.productAttr)
    const missingAttrs = attrs.filter((attr) => !selectedAttrs[attr.key])

    if (missingAttrs.length > 0) {
      showToast(`请选择${missingAttrs[0].key}`, 'error', AlertCircleIcon)
      return false
    }
  }
  return true
}

const navigateToCart = () => {
  router.push('/cart')
}

// Scroll Handler
const handleScroll = () => {
  isScrolled.value = window.scrollY > 0
}

// Download image functionality
const downloadImage = async (imageUrl: string, productName: string) => {
  try {
    // 显示正在下载的提示
    showToast('正在下载图片...', 'success')

    // 获取图片文件名
    const fileName =
      productName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_') + '_' + new Date().getTime() + '.jpg'

    // 创建一个 a 标签来触发下载
    const link = document.createElement('a')

    // 如果是跨域图片，可能需要使用 fetch 和 blob 来处理
    const response = await fetch(imageUrl)
    const blob = await response.blob()
    const objectUrl = URL.createObjectURL(blob)

    link.href = objectUrl
    link.download = fileName
    document.body.appendChild(link)
    link.click()

    // 清理
    document.body.removeChild(link)
    URL.revokeObjectURL(objectUrl)

    showToast('图片下载成功', 'success', CheckIcon)
  } catch (error) {
    console.error('下载图片失败:', error)
    showToast('下载图片失败，请重试', 'error', AlertCircleIcon)
  }
}

// 下载所有图片
const downloadAllImages = async (productName: string) => {
  try {
    // 显示正在下载的提示
    showToast('准备下载所有图片...', 'success')

    // 下载计数
    let downloadCount = 0

    // 逐个下载图片
    for (let i = 0; i < allImages.value.length; i++) {
      const imageUrl = allImages.value[i]
      if (!imageUrl) continue

      try {
        // 生成文件名
        const fileName = `${productName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')}_${i + 1}_${new Date().getTime()}.jpg`

        // 下载图片
        const response = await fetch(imageUrl)
        const blob = await response.blob()
        const objectUrl = URL.createObjectURL(blob)

        const link = document.createElement('a')
        link.href = objectUrl
        link.download = fileName
        document.body.appendChild(link)
        link.click()

        // 清理
        document.body.removeChild(link)
        URL.revokeObjectURL(objectUrl)

        downloadCount++
      } catch (err) {
        console.error(`下载图片 ${i + 1} 失败:`, err)
      }

      // 稍微延迟一下，避免浏览器同时触发太多下载
      await new Promise((resolve) => setTimeout(resolve, 500))
    }

    if (downloadCount > 0) {
      showToast(`成功下载 ${downloadCount} 张图片`, 'success', CheckIcon)
    } else {
      showToast('没有图片被下载', 'error', AlertCircleIcon)
    }
  } catch (error) {
    console.error('下载所有图片失败:', error)
    showToast('下载图片失败，请重试', 'error', AlertCircleIcon)
  }
}

// Lifecycle
onMounted(() => {
  getProductDetail()
  window.addEventListener('scroll', handleScroll)
  // Add keyboard event listener for image navigation
  window.addEventListener('keydown', (e) => {
    if (showImagePreview.value) {
      handlePreviewKeydown(e)
    }
  })
})

// API Calls
const getProductDetail = async () => {
  try {
    // 移除token头，让未登录用户也能获取商品详情
    const res = await request.get(`/products/${route.params.id}`)
    if (res.data.code === 1) {
      product.value = res.data.data
      pdfSource.value = res.data.data.pdfDocument

      // 自动选择第一个属性值
      if (product.value.productAttr) {
        const attrs = parseProductAttr(product.value.productAttr)
        console.log('当前页面商品详情：', product.value)
        attrs.forEach((attr) => {
          if (Array.isArray(attr.value)) {
            selectedAttrs[attr.key] = attr.value[0] || ''
          } else {
            selectedAttrs[attr.key] = attr.value || ''
          }
        })
      }

      loading.value = false
    } else {
      ElMessage.error(res.data.message || '获取商品详情失败')
    }
  } catch (error) {
    console.error('获取商品详情失败:', error)
    ElMessage.error('获取商品详情失败')
  }
}

// 组件卸载时清理Stripe元素
onUnmounted(() => {
  if (cardElement.value) {
    cardElement.value.unmount()
  }

  // 清理轮询定时器
  if (paymentStatusInterval) {
    clearInterval(paymentStatusInterval)
    paymentStatusInterval = null
  }
})

// Computed
const albumPicsArray = computed(() => {
  if (!product.value.albumPics) return []
  return product.value.albumPics.split(',').filter(Boolean)
})

const allImages = computed(() => {
  return [product.value.pic, ...albumPicsArray.value]
})

const totalImages = computed(() => {
  return allImages.value.length
})

const currentDisplayImage = computed(() => {
  return allImages.value[currentImageIndex.value] || product.value.pic
})

const isNew = computed(() => {
  if (!product.value.createTime) return false
  const createTime = new Date(product.value.createTime)
  const now = new Date()
  const diffDays = Math.floor((now.getTime() - createTime.getTime()) / (1000 * 60 * 60 * 24))
  return diffDays <= 7
})

const productTags = computed(() => [
  {
    icon: HashIcon,
    text: product.value.outProductId,
    classes:
      'bg-gradient-to-r from-violet-50 to-purple-50 text-violet-700 border border-violet-200 group hover:border-violet-300',
  },
  {
    icon: CircleIcon,
    text: product.value.publishStatus === 1 ? '在售' : '已下架',
    classes:
      product.value.publishStatus === 1
        ? 'bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border border-green-200'
        : 'bg-gradient-to-r from-red-50 to-rose-50 text-red-700 border border-red-200',
  },
  {
    icon: TagIcon,
    text: product.value.productCategoryName,
    classes:
      'bg-gradient-to-r from-violet-50 to-purple-50 text-violet-700 border border-violet-200',
  },
  // 添加库存标签
  {
    icon: PackageIcon,
    text:
      (product.value as any).inventory && (product.value as any).inventory > 0
        ? `库存: ${(product.value as any).inventory}${product.value.unit}`
        : '缺货',
    classes:
      (product.value as any).inventory && (product.value as any).inventory > 0
        ? 'bg-gradient-to-r from-blue-50 to-cyan-50 text-blue-700 border border-blue-200'
        : 'bg-gradient-to-r from-orange-50 to-amber-50 text-orange-700 border border-orange-200',
  },
])

const productSpecs = computed(() => [
  { label: '品牌', value: product.value.brandName },
  { label: '分类', value: product.value.productCategoryName },
  { label: '重量', value: `${product.value.weight}克` },
  { label: '编码', value: product.value.outProductId },
  {
    label: '库存',
    value: (product.value as any).inventory
      ? `${(product.value as any).inventory}${product.value.unit}`
      : '暂无库存',
  },
])

const guarantees = [
  { icon: CheckCircleIcon, text: '7天无理由退换' },
  { icon: ShieldCheckIcon, text: '正品保证' },
  { icon: BadgeCheckIcon, text: '假一赔十' },
  { icon: TruckIcon, text: '24小时发货' },
]

// Share platforms
const sharePlatforms = [
  { name: '微信', icon: ShareIcon, iconClass: 'text-green-500' },
  { name: 'Facebook', icon: ShareIcon, iconClass: 'text-blue-500' },
  { name: 'Twitter', icon: ShareIcon, iconClass: 'text-blue-400' },
  { name: '复制链接', icon: ShareIcon, iconClass: 'text-gray-500' },
]

// 产品属性
const parsedAttributes = computed(() => {
  try {
    console.log('产品属性：', product.value.productAttr)
    return parseProductAttr(product.value.productAttr)
  } catch (error) {
    console.error('解析商品属性失败:', error)
    return []
  }
})
watchEffect(() => {
  if (product.value?.productAttr) {
    console.log('商品总属性列表:', parsedAttributes.value)
    // if(parsedAttributes.value)
    // 遍历商品属性列表，将只有一个属性值的属性类型添加到selectAttrList中
  }
})

// 选择属性
const handelSelect = (attrType: string, selectAttr: string) => {
  if (selectAttrList.value.some((item) => item.key === attrType)) {
    const item = selectAttrList.value.find((item) => item.key === attrType)
    if (item) {
      item.value = selectAttr
    }
  } else {
    selectAttrList.value.unshift({ key: attrType, value: selectAttr })
  }
  console.log(selectAttrList.value)
}

// 处理支付状态未知的情况 - 显示可能错误的具体错误码和原因翻译
const handleUnknownError = (errorCode?: string, errorMessage?: string) => {
  // 错误代码映射到中文描述
  const errorCodeMap: Record<string, string> = {
    card_declined: '卡片被拒绝',
    insufficient_funds: '账户余额不足',
    expired_card: '卡片已过期',
    incorrect_cvc: '安全码错误',
    processing_error: '处理错误',
    rate_limit: '请求频率过高',
    invalid_request: '无效请求',
    authentication_required: '需要身份验证',
    approve_with_id: '需要额外批准',
    call_issuer: '请联系发卡行',
    card_not_supported: '卡片不支持此交易',
    do_not_honor: '发卡行拒绝交易',
    do_not_try_again: '请勿重试',
    duplicate_transaction: '重复交易',
    fraudulent: '可疑欺诈交易',
    generic_decline: '交易被拒',
    invalid_account: '无效账户',
    invalid_amount: '无效金额',
    issuer_not_available: '发卡行暂不可用',
    lost_card: '丢失卡',
    merchant_blacklist: '商户黑名单',
    new_account_information_available: '有新账户信息',
    no_action_taken: '未采取行动',
    not_permitted: '不允许的交易',
    pickup_card: '收回卡片',
    pin_try_exceeded: '超过PIN尝试次数',
    restricted_card: '受限制的卡片',
    revocation_of_all_authorizations: '撤销所有授权',
    revocation_of_authorization: '撤销授权',
    security_violation: '安全违规',
    service_not_allowed: '服务不允许',
    stolen_card: '被盗卡',
    stop_payment_order: '停止支付指令',
    transaction_not_allowed: '不允许的交易',
    try_again_later: '请稍后重试',
    withdrawal_count_limit_exceeded: '超过取款次数限制',
  }

  // 提取错误信息中的错误代码
  let extractedErrorCode = errorCode || 'unknown_error'
  let errorReason = ''

  if (errorMessage) {
    // 尝试从错误信息中提取错误代码
    const codeMatch = errorMessage.match(/code:\s*([a-z_]+)/i)
    if (codeMatch && codeMatch[1]) {
      extractedErrorCode = codeMatch[1]
    }

    // 尝试提取主要错误原因
    const reasonMatch = errorMessage.match(/([^;:]+)(?:;|$)/)
    if (reasonMatch && reasonMatch[1]) {
      errorReason = reasonMatch[1].trim()
    }
  }

  // 获取错误的中文描述
  const errorDescription = errorCodeMap[extractedErrorCode] || '未知错误'

  return {
    errorCode: extractedErrorCode,
    errorReason: errorReason,
    errorDescription: errorDescription,
  }
}
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-enter-from,
.notification-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.badge-enter-active,
.badge-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.badge-enter-from,
.badge-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes ripple {
  from {
    opacity: 1;
    transform: scale(0);
  }
  to {
    opacity: 0;
    transform: scale(2.5);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-ripple {
  animation: ripple 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #d8b4fe;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a78bfa;
}

button:active:not(:disabled) {
  transform: scale(0.97);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

img {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive Styles */
@media (max-width: 640px) {
  .grid-cols-4 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Gradient Animation */
.bg-gradient-animate {
  background-size: 200% 200%;
  animation: gradient 15s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
.drawer-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.drawer-content .drawer-header {
  margin: 10% 0 5% 0;
}

.drawer-content .radio-group {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}

.drawer-content .option-item {
  display: block;
  padding: 12px 16px;
  width: 100%;
  height: 5em;
  margin: 8px 0;
  border-radius: 4px;
  /* background: #f8f9fa; */
  transition: all 0.3s;
  line-height: 100%;
}

.drawer-content .option-item:hover {
  background: #e9ecef;
}

.drawer-content .option-item :deep(.el-radio__label) {
  display: block;
  width: 100%;
}

.drawer-content .option-item .option-item-span {
  height: 100%;
  font-size: 1.3em;
  line-height: 100%;
}

.drawer-content .single-option {
  display: flex;
  padding: 16px;
  margin: 20px 0;
  text-align: center;
  font-size: 16px;
  flex: 1;
}

.drawer-content .action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.drawer-content .cancel-btn,
.drawer-content .confirm-btn {
  width: 48%;
  height: 40px;
  border-color: rgb(124, 58, 237);
}

.drawer-content .cancel-btn {
  background: #f8f9fa;
  border-color: rgb(124, 58, 237);
}

.drawer-content .confirm-btn {
  background-color: rgb(124, 58, 237);
}

.drawer-content .cancel-btn,
.drawer-content .confirm-btn {
  transition: transform 0.2s ease;
  transform: translateY(0);
}

.drawer-content .cancel-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  color: rgb(105, 105, 105);
}

.drawer-content .confirm-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.spec-prompt {
  /* 基础文字样式 */
  display: inline-block;
  font-size: 16px;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 5%;

  /* 装饰效果 */
  padding: 8px 16px;
  border-radius: 8px;
  background-color: #f9fafb;

  /* 边框和阴影 */
  border-left: 4px solid #8b5cf6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 响应式调整 */
@media (max-width: 640px) {
  .spec-prompt {
    font-size: 14px;
    padding: 6px 12px;
  }
}

/* 主容器样式 */
/* 主容器样式 */
.attribute-blocks {
  margin: 20px 0;
  padding: 0 10px;
}

/* 单个属性块样式 */
.attribute-block {
  margin-bottom: 28px;
}

/* 属性类型标题样式 */
.attribute-type {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-left: 4px;
}

/* 属性网格容器 */
.attribute-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

/* 属性值通用样式 */
.attribute-value {
  cursor: pointer;
  transition: all 0.3s ease;
}

/* 属性值内容样式 - 强化边框 */
.attr-value {
  padding: 12px 10px;
  border-radius: 8px;
  border: 2px solid #d1d1d1; /* 加粗边框并调深颜色 */
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.2s ease;
  background-color: white;
  color: #333;
  min-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* 添加轻微阴影增强立体感 */
}

/* 悬停效果 - 强化边框交互 */
.attribute-value:hover .attr-value {
  border-color: #7c3aed; /* 悬停时边框变主题色 */
  border-width: 2px; /* 保持边框粗细一致 */
  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.2);
  transform: translateY(-2px);
}

/* 选中状态样式 - 强化边框效果 */
.attr-value[style*='background-color: rgb(124, 58, 237)'] {
  background-color: #7c3aed !important;
  color: white !important;
  border-color: #7c3aed !important; /* 确保边框颜色同步变化 */
  border-width: 2px; /* 保持选中状态边框粗细 */
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
  font-weight: 600;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .attribute-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .attr-value {
    font-size: 15px;
    border-width: 1.5px; /* 移动端稍细边框 */
  }
}

@media (max-width: 480px) {
  .attribute-grid {
    grid-template-columns: 1fr;
  }

  .attribute-type {
    font-size: 17px;
  }

  .attr-value {
    padding: 10px 8px;
    border-width: 1.5px;
  }
}
.number-inventory {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
/* 数量选择器容器 */
.quantity-selector {
  margin-bottom: 8px;
}
/* 当前库存区域 */
.product-inventory {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-right: 3rem;
  color: #374151;
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
}

.inventory-text {
  color: #6b7280;
  font-weight: 500;
  font-size: 1.1rem;
}

.inventory-number {
  color: #7c3aed;
  font-weight: 600;
  font-size: 1.1rem;
}

.inventory-unit {
  color: #6b7280;
  font-weight: 400;
  font-size: 1.1rem;
}
/* 标签样式 */
.quantity-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

/* 控制按钮容器 */
.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 通用按钮样式 */
.quantity-btn {
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid #e5e7eb;
  background-color: #f9fafb;
  color: #374151;
  cursor: pointer;
}

/* 减号按钮悬停效果 */
.minus-btn:hover:not(:disabled) {
  background-color: #f5f3ff;
  color: #7c3aed;
  border-color: #ddd6fe;
}

/* 加号按钮悬停效果 */
.plus-btn:hover:not(:disabled) {
  background-color: #f5f3ff;
  color: #7c3aed;
  border-color: #ddd6fe;
}

/* 按钮点击效果 */
.quantity-btn:active:not(:disabled) {
  background-color: #ede9fe;
  transform: scale(0.95);
}

/* 禁用状态 */
.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 输入框样式 */
.quantity-input {
  width: 60px;
  height: 36px;
  text-align: center;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
  font-size: 14px;
}

/* 输入框聚焦效果 */
.quantity-input:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

/* 隐藏数字输入框的上下箭头 */
.quantity-input::-webkit-outer-spin-button,
.quantity-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.quantity-input[type='number'] {
  -moz-appearance: textfield;
}

/* 按钮图标 */
.quantity-icon {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}

/* 支付结果弹窗样式 */
.payment-result-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20px 0;
}

.result-icon {
  margin-bottom: 20px;
  padding: 16px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.success-icon {
  background-color: rgba(52, 211, 153, 0.1);
  color: #10b981;
}

.error-icon {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.warning-icon {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.result-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
}

.success-text {
  color: #10b981;
}

.error-text {
  color: #ef4444;
}

.warning-text {
  color: #f59e0b;
}

.result-message-cn {
  font-size: 16px;
  color: #4b5563;
  margin-bottom: 8px;
}

.result-message-en {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 16px;
  font-style: italic;
}

.result-error-code {
  font-size: 12px;
  color: #9ca3af;
  margin-bottom: 20px;
  padding: 4px 12px;
  background-color: #f3f4f6;
  border-radius: 12px;
}

.result-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12px;
  margin-top: 8px;
}

.action-button {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.success-button {
  background-color: #8b5cf6;
  color: white;
}

.success-button:hover {
  background-color: #7c3aed;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(139, 92, 246, 0.25);
}

.retry-button {
  background-color: #8b5cf6;
  color: white;
}

.retry-button:hover {
  background-color: #7c3aed;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(139, 92, 246, 0.25);
}

.close-button {
  background-color: #f3f4f6;
  color: #4b5563;
}

.close-button:hover {
  background-color: #e5e7eb;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.payment-method-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20px 0;
}

.payment-methods {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  width: 100%;
  gap: 16px;
}

.payment-method-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  transition: all 0.3s ease;
  flex: 1;
  min-height: 120px;
  background-color: white;
  cursor: pointer;
}

.payment-method-btn:hover {
  background-color: #f5f3ff;
  border-color: #8b5cf6;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(139, 92, 246, 0.15);
}

.payment-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 12px;
  color: #8b5cf6;
}

.credit-card-icon {
  color: #8b5cf6;
}

.payment-method-btn span {
  font-size: 16px;
  font-weight: 500;
  color: #4b5563;
}

.cancel-payment-btn {
  width: 100px;
  height: 40px;
  background-color: #f3f4f6;
  color: #4b5563;
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  margin-top: 20px;
}

.cancel-payment-btn:hover {
  background-color: #e5e7eb;
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.wechat-pay-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 0;
}

.wechat-pay-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
}

.wechat-pay-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

/* 顶部品牌区域 */
.pay-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #eaeaea;
}

.pay-brand {
  display: flex;
  align-items: center;
}

.pay-logo {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.pay-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.pay-status {
  font-size: 14px;
  font-weight: 500;
  color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.1);
  padding: 4px 10px;
  border-radius: 12px;
}

.pay-status-success {
  color: #10b981;
  background-color: rgba(16, 185, 129, 0.1);
}

/* 商品信息摘要样式 */
.product-summary {
  display: flex;
  align-items: center;
  background: linear-gradient(to right, #f8f9fa, #f3f4f6);
  padding: 16px 20px;
  width: 100%;
  border-bottom: 1px solid #eaeaea;
}

.product-thumbnail {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  margin-right: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.product-info {
  flex: 1;
  text-align: left;
}

.product-name {
  font-weight: 600;
  font-size: 15px;
  color: #374151;
  margin-bottom: 4px;
  /* 文本溢出处理 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.product-attrs {
  font-size: 13px;
  color: #6b7280;
  /* 文本溢出处理 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

/* 内容容器 */
.pay-content-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  width: 100%;
  background-color: white;
}

/* 二维码容器 */
.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.qrcode-wrapper {
  position: relative;
  display: inline-block;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  padding: 8px;
  background-color: white;
  transition: all 0.3s ease;
}

.qrcode-wrapper:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.wechat-qrcode {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  background: white;
  transition: all 0.3s ease;
}

.blur-qrcode {
  filter: blur(2px);
  opacity: 0.7;
}

.scan-status-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
}

.scan-status-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: white;
  padding: 16px 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-weight: 600;
  color: #4b5563;
}

.qrcode-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #ff7b7b;
  padding: 40px;
}

/* 订单号显示 */
.order-number-display {
  text-align: center;
  margin-top: 12px;
  padding: 8px 12px;
  background-color: #f9fafb;
  border-radius: 8px;
  font-size: 13px;
  color: #4b5563;
  border-left: 3px solid #41b883;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  max-width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.order-number-label {
  font-weight: 500;
  margin-right: 4px;
}

.order-number-value {
  font-family: monospace;
  letter-spacing: 0.5px;
  color: #1f2937;
}

/* 支付信息 */
.payment-info {
  text-align: center;
  background-color: #f9fafb;
  padding: 16px;
  border-radius: 12px;
  width: 100%;
  margin-top: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.price-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.original-price,
.service-fee {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #6b7280;
}

.payment-amount-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, rgba(65, 184, 131, 0.1) 0%, rgba(47, 156, 106, 0.2) 100%);
  border-radius: 10px;
  border-left: 4px solid #41b883;
}

.payment-amount-display .price-label {
  color: #374151;
  font-weight: 600;
  font-size: 15px;
}

.payment-amount-display .price-value {
  font-size: 18px;
  color: #41b883;
  font-weight: 700;
}

.cny-amount {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 4px;
  font-size: 13px;
  color: #6b7280;
}

.cny-label {
  margin-right: 4px;
}

.cny-value {
  font-weight: 500;
  color: #41b883;
}

/* 底部指引区域 */
.pay-footer {
  width: 100%;
  padding: 16px 20px;
  background-color: #f9fafb;
  border-top: 1px solid #eaeaea;
}

.pay-instructions {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.instruction-icon-wrapper {
  background-color: rgba(65, 184, 131, 0.1);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.instruction-icon {
  width: 18px;
  height: 18px;
  color: #41b883;
}

.instruction-text {
  color: #374151;
  font-size: 14px;
  font-weight: 500;
}

.service-fee-note {
  text-align: center;
  font-size: 12px;
  color: #9ca3af;
  margin-bottom: 12px;
  font-style: italic;
}

.pay-countdown {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(251, 191, 36, 0.1);
  padding: 8px 12px;
  border-radius: 8px;
}

.countdown-icon {
  width: 16px;
  height: 16px;
  color: #f59e0b;
  margin-right: 8px;
}

.countdown-text {
  color: #92400e;
  font-size: 13px;
  font-weight: 500;
}

/* 安全提示 */
.pay-security {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  background-color: white;
  width: 100%;
  border-top: 1px solid #eaeaea;
}

.security-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
  margin-right: 6px;
}

.security-text {
  color: #6b7280;
  font-size: 12px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 微信支付对话框容器样式 */
:deep(.wechat-pay-dialog-container .el-dialog) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

:deep(.wechat-pay-dialog-container .el-dialog__header) {
  background: linear-gradient(135deg, #41b883 0%, #2f9c6a 100%);
  color: white;
  padding: 16px 20px;
}

:deep(.wechat-pay-dialog-container .el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

:deep(.wechat-pay-dialog-container .el-dialog__headerbtn .el-dialog__close) {
  color: rgba(255, 255, 255, 0.9);
}

:deep(.wechat-pay-dialog-container .el-dialog__body) {
  padding: 0;
}

/* 添加二维码过期相关样式 */
.qrcode-expired {
  opacity: 0.5;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.5);
}

.expired-message {
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.refresh-btn {
  margin-top: 10px;
  padding: 6px 12px;
  background-color: #41b883;
  color: white;
  border-radius: 6px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background-color: #2f9c6a;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 过期状态样式 */
.pay-status-expired {
  color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.15);
}

/* 倒计时关键时刻样式 */
.countdown-critical {
  background-color: rgba(239, 68, 68, 0.15);
  animation: pulse 1s ease-in-out infinite;
}

.countdown-critical .countdown-icon,
.countdown-critical .countdown-text {
  color: #ef4444;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}
.pdf-preview-dialog {
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.pdf-dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.pdf-viewport {
  flex: 1;
  overflow-y: auto;
  max-height: 65vh;
  border: 1px solid #eee;
  margin-bottom: 16px;
}

.pdf-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-top: 1px solid #eee;
}

.page-navigation {
  display: flex;
  align-items: center;
}

.page-info {
  margin: 0 12px;
  font-size: 14px;
  color: #666;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.download-btn {
  margin-left: 8px;
}
</style>
