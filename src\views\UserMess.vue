<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">消息中心</h1>
    <div class="bg-white rounded-lg shadow p-6">
      <!-- 消息列表 -->
      <div class="space-y-4">
        <div v-for="message in messages" :key="message.id"
          class="p-4 border rounded-lg hover:shadow-md transition-shadow">
          <div class="flex justify-between items-start">
            <h3 class="font-medium">{{ message.title }}</h3>
            <span class="text-sm text-gray-500">{{ message.time }}</span>
          </div>
          <p class="text-gray-600 mt-2">{{ message.content }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Message {
  id: number
  title: string
  content: string
  time: string
}

const messages = ref<Message[]>([
  {
    id: 1,
    title: '系统通知',
    content: '您的店铺已经成功通过审核',
    time: '2024-01-15 10:30'
  },
  {
    id: 2,
    title: '订单提醒',
    content: '您有一个新的订单待处理',
    time: '2024-01-15 09:15'
  }
])
</script>
