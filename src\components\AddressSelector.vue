<template>
  <div class="address-selector">
    <!-- 地址列表 -->
    <div v-if="addresses.length > 0" class="address-list">
      <div
        v-for="address in addresses"
        :key="address.id"
        class="address-item"
        :class="{ 'address-selected': selectedAddressId === address.id }"
        @click="selectAddress(address)"
      >
        <div class="address-content">
          <div class="address-header">
            <div class="name-phone">
              <span class="name">{{ address.name }}</span>
              <span class="phone">{{ address.phoneNumber }}</span>
            </div>
            <div v-if="address.Default === 1" class="default-badge">{{ t('address.default') }}</div>
          </div>
          <div class="address-detail">
            {{ formatAddress(address) }}
          </div>
        </div>
        <div class="address-actions">
          <button class="edit-btn" @click.stop="editAddress(address)">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
              />
            </svg>
            {{ t('address.edit') }}
          </button>
          <button class="delete-btn" @click.stop="deleteAddressConfirm(address)">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              />
            </svg>
            {{ t('address.delete') }}
          </button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!loading" class="empty-state">
      <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
        />
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
        />
      </svg>
      <p>{{ t('address.empty') }}</p>
    </div>

    <!-- 加载状态 -->
    <div v-else class="loading-state">
      <div class="spinner"></div>
      <p>{{ t('address.loading') }}</p>
    </div>

    <!-- 添加新地址按钮 -->
    <div class="add-address-btn-container">
      <button class="add-address-btn" @click="showAddressForm = true">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
          />
        </svg>
        {{ t('address.addNew') }}
      </button>
    </div>

    <!-- 地址表单弹窗 -->
    <div v-if="showAddressForm" class="address-form-overlay">
      <div class="address-form-container">
        <div class="address-form-header">
          <h3>{{ isEditing ? t('address.edit') : t('address.addNew') }}</h3>
          <button class="close-btn" @click="showAddressForm = false">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <div class="address-form">
          <div class="form-group">
            <label for="name">{{ t('address.name') }} *</label>
            <input type="text" id="name" v-model="addressForm.name" required />
          </div>
          <div class="form-group">
            <label for="phone">{{ t('address.phone') }} *</label>
            <input type="text" id="phone" v-model="addressForm.phoneNumber" required />
          </div>
          <div class="form-row">
            <div class="form-group">
              <label for="state">{{ t('address.state') }} *</label>
              <input type="text" id="state" v-model="addressForm.state" required />
            </div>
            <div class="form-group">
              <label for="city">{{ t('address.city') }} *</label>
              <input type="text" id="city" v-model="addressForm.city" required />
            </div>
          </div>
          <div class="form-group">
            <label for="street">{{ t('address.street') }} *</label>
            <input type="text" id="street" v-model="addressForm.street" required />
          </div>
          <div class="form-group">
            <label for="addressDetail">{{ t('address.detail') }} *</label>
            <input type="text" id="addressDetail" v-model="addressForm.addressDetail" required />
          </div>
          <div class="form-group">
            <label for="zipCode">{{ t('address.zipCode') }} *</label>
            <input type="text" id="zipCode" v-model="addressForm.zipCode" required />
          </div>
          <div class="form-checkbox">
            <input type="checkbox" id="setDefault" v-model="isDefaultAddress" />
            <label for="setDefault">{{ t('address.setDefault') }}</label>
          </div>
          <div class="form-actions">
            <button class="cancel-btn" @click="showAddressForm = false">
              {{ t('address.cancel') }}
            </button>
            <button class="save-btn" @click="saveAddress">
              {{ t('address.save') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteConfirm" class="delete-confirm-overlay">
      <div class="delete-confirm-container">
        <div class="delete-confirm-header">
          <h3>{{ t('address.delete') }}</h3>
          <button class="close-btn" @click="showDeleteConfirm = false">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <div class="delete-confirm-content">
          <p>{{ t('address.deleteConfirm') }}</p>
        </div>
        <div class="delete-confirm-actions">
          <button class="cancel-btn" @click="showDeleteConfirm = false">
            {{ t('address.cancel') }}
          </button>
          <button class="confirm-btn" @click="confirmDelete">
            {{ t('address.confirm') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { addressApi } from '@/api/address'
import { ElMessage } from 'element-plus'

export default {
  name: 'AddressSelector',
  props: {
    modelValue: {
      type: Number,
      default: 0,
    },
  },
  emits: ['update:modelValue', 'address-selected'],
  setup(props, { emit }) {
    // 多语言支持
    const currentLanguage = ref(localStorage.getItem('language') || 'zh')

    const translations = {
      zh: {
        address: {
          default: '默认',
          title: '收货地址',
          select: '请选择收货地址',
          add: '添加新地址',
          empty: '暂无收货地址',
          addNew: '添加新地址',
          edit: '编辑地址',
          delete: '删除地址',
          deleteConfirm: '确定要删除此地址吗？',
          cancel: '取消',
          confirm: '确定',
          save: '保存地址',
          name: '收件人姓名',
          phone: '联系电话',
          state: '省/州',
          city: '城市',
          street: '街道',
          detail: '详细地址',
          zipCode: '邮政编码',
          setDefault: '设为默认地址',
          loading: '加载中...',
          addSuccess: '地址添加成功',
          updateSuccess: '地址更新成功',
          deleteSuccess: '地址删除成功',
          error: '操作失败',
          required: '此项为必填项',
        },
      },
      en: {
        address: {
          default: 'Default',
          title: 'Shipping Address',
          select: 'Please select a shipping address',
          add: 'Add new address',
          empty: 'No shipping address',
          addNew: 'Add New Address',
          edit: 'Edit Address',
          delete: 'Delete Address',
          deleteConfirm: 'Are you sure you want to delete this address?',
          cancel: 'Cancel',
          confirm: 'Confirm',
          save: 'Save Address',
          name: 'Recipient Name',
          phone: 'Phone Number',
          state: 'State/Province',
          city: 'City',
          street: 'Street',
          detail: 'Address Detail',
          zipCode: 'ZIP Code',
          setDefault: 'Set as default address',
          loading: 'Loading...',
          addSuccess: 'Address added successfully',
          updateSuccess: 'Address updated successfully',
          deleteSuccess: 'Address deleted successfully',
          error: 'Operation failed',
          required: 'This field is required',
        },
      },
    }

    // 翻译函数
    const t = (key) => {
      const keys = key.split('.')
      let result = translations[currentLanguage.value]
      for (const k of keys) {
        if (result && result[k]) {
          result = result[k]
        } else {
          return key
        }
      }
      return result
    }

    // 地址列表
    const addresses = ref([])
    const loading = ref(false)
    const selectedAddressId = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value),
    })

    // 地址表单
    const showAddressForm = ref(false)
    const isEditing = ref(false)
    const addressForm = reactive({
      name: '',
      phoneNumber: '',
      state: '',
      city: '',
      street: '',
      addressDetail: '',
      zipCode: '',
      Default: 0,
    })

    // UI状态，用于表示是否设为默认地址
    const isDefaultAddress = ref(false)

    // 删除确认
    const showDeleteConfirm = ref(false)
    const addressToDelete = ref(null)

    // 获取用户地址列表
    const fetchAddresses = async () => {
      try {
        loading.value = true
        // 获取当前用户ID
        const userStr = localStorage.getItem('user')
        if (!userStr) {
          ElMessage.error(t('address.error'))
          return
        }

        const user = JSON.parse(userStr)
        const userId = user?.id

        if (!userId) {
          ElMessage.error(t('address.error'))
          return
        }

        const res = await addressApi.getAddressList(userId)
        if (res.data.code === 1 && res.data.data) {
          addresses.value = res.data.data

          // 如果有默认地址，选中它
          const defaultAddress = addresses.value.find((addr) => addr.Default === 1)
          if (defaultAddress && defaultAddress.id) {
            selectedAddressId.value = defaultAddress.id
            emit('address-selected', defaultAddress)
          } else if (addresses.value.length > 0 && addresses.value[0].id) {
            // 否则选中第一个地址
            selectedAddressId.value = addresses.value[0].id
            emit('address-selected', addresses.value[0])
          }
        }
      } catch (error) {
        console.error('获取地址列表失败:', error)
        ElMessage.error(t('address.error'))
      } finally {
        loading.value = false
      }
    }

    // 选择地址
    const selectAddress = (address) => {
      if (address.id) {
        selectedAddressId.value = address.id
        emit('address-selected', address)
      }
    }

    // 编辑地址
    const editAddress = (address) => {
      isEditing.value = true
      Object.assign(addressForm, address)
      // 设置是否为默认地址的UI状态
      isDefaultAddress.value = address.Default === 1
      showAddressForm.value = true
    }

    // 保存地址
    const saveAddress = async () => {
      try {
        // 表单验证
        if (
          !addressForm.name ||
          !addressForm.phoneNumber ||
          !addressForm.state ||
          !addressForm.city ||
          !addressForm.street ||
          !addressForm.addressDetail ||
          !addressForm.zipCode
        ) {
          ElMessage.error(t('address.required'))
          return
        }

        // 获取当前用户ID
        const userStr = localStorage.getItem('user')
        if (!userStr) {
          ElMessage.error(t('address.error'))
          return
        }

        const user = JSON.parse(userStr)
        const userId = user?.id

        if (!userId) {
          ElMessage.error(t('address.error'))
          return
        }

        // 准备保存的地址数据
        const addressData = {
          ...addressForm,
          userId,
          Default: isDefaultAddress.value ? 1 : 0,
        }

        console.log('保存地址数据:', addressData)

        let res
        if (isEditing.value && addressForm.id) {
          // 更新地址
          res = await addressApi.updateAddress(addressData)
        } else {
          // 添加地址
          res = await addressApi.addAddress(addressData)
        }

        if (res.data.code === 1) {
          ElMessage.success(isEditing.value ? t('address.updateSuccess') : t('address.addSuccess'))

          // 如果设为默认地址
          if (isDefaultAddress.value) {
            // 如果是编辑模式，使用现有ID；如果是新增模式，使用返回的ID
            const addressId = addressForm.id || (res.data.data && res.data.data.id)
            if (addressId) {
              await addressApi.setDefaultAddress(addressId)
            }
          }

          // 重置表单
          resetAddressForm()

          // 关闭表单
          showAddressForm.value = false

          // 重新获取地址列表
          await fetchAddresses()

          // 刷新页面，确保地址列表和选择状态完全更新
          window.location.reload()
        } else {
          ElMessage.error(res.data.msg || t('address.error'))
        }
      } catch (error) {
        console.error(isEditing.value ? '更新地址失败:' : '添加地址失败:', error)
        ElMessage.error(t('address.error'))
      }
    }

    // 重置地址表单
    const resetAddressForm = () => {
      Object.assign(addressForm, {
        id: undefined,
        name: '',
        phoneNumber: '',
        state: '',
        city: '',
        street: '',
        addressDetail: '',
        zipCode: '',
        Default: 0,
      })
      isDefaultAddress.value = false
      isEditing.value = false
    }

    // 删除地址确认
    const deleteAddressConfirm = (address) => {
      addressToDelete.value = address
      showDeleteConfirm.value = true
    }

    // 确认删除地址
    const confirmDelete = async () => {
      if (!addressToDelete.value || !addressToDelete.value.id) return

      try {
        const res = await addressApi.deleteAddress(addressToDelete.value.id)
        if (res.data.code === 1) {
          ElMessage.success(t('address.deleteSuccess'))

          // 如果删除的是当前选中的地址，清空选中状态
          if (selectedAddressId.value === addressToDelete.value.id) {
            selectedAddressId.value = 0
            emit('address-selected', null)
          }

          // 重新获取地址列表
          await fetchAddresses()

          // 刷新页面，确保地址列表和选择状态完全更新
          window.location.reload()
        } else {
          ElMessage.error(res.data.msg || t('address.error'))
        }
      } catch (error) {
        console.error('删除地址失败:', error)
        ElMessage.error(t('address.error'))
      } finally {
        showDeleteConfirm.value = false
        addressToDelete.value = null
      }
    }

    // 格式化地址
    const formatAddress = (address) => {
      return `${address.state} ${address.city} ${address.street} ${address.addressDetail} ${address.zipCode}`
    }

    // 初始化
    onMounted(() => {
      fetchAddresses()
    })

    return {
      addresses,
      loading,
      selectedAddressId,
      showAddressForm,
      isEditing,
      addressForm,
      isDefaultAddress,
      showDeleteConfirm,
      addressToDelete,
      fetchAddresses,
      selectAddress,
      editAddress,
      saveAddress,
      resetAddressForm,
      deleteAddressConfirm,
      confirmDelete,
      formatAddress,
      t,
    }
  },
}
</script>

<style scoped>
.address-selector {
  position: relative;
  font-family: system-ui, sans-serif;
}

.address-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.address-item {
  display: flex;
  flex-direction: column;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.address-item:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.address-selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.dark .address-selected {
  border-color: #60a5fa;
  background-color: rgba(59, 130, 246, 0.1);
}

.address-content {
  flex: 1;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.name-phone {
  display: flex;
  align-items: center;
  gap: 12px;
}

.name {
  font-weight: 600;
}

.phone {
  color: #64748b;
}

.default-badge {
  background-color: #3b82f6;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.address-detail {
  color: #334155;
  line-height: 1.5;
}

.dark .address-detail {
  color: #cbd5e1;
}

.address-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #e2e8f0;
}

.dark .address-actions {
  border-top-color: #334155;
}

.edit-btn,
.delete-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.edit-btn {
  color: #3b82f6;
}

.delete-btn {
  color: #ef4444;
}

.edit-btn:hover {
  background-color: #eff6ff;
}

.delete-btn:hover {
  background-color: #fef2f2;
}

.dark .edit-btn:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.dark .delete-btn:hover {
  background-color: rgba(239, 68, 68, 0.1);
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  color: #64748b;
  text-align: center;
  border: 1px dashed #cbd5e1;
  border-radius: 8px;
}

.dark .empty-state,
.dark .loading-state {
  border-color: #475569;
  color: #94a3b8;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(99, 102, 241, 0.2);
  border-top-color: #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.add-address-btn-container {
  margin-top: 16px;
}

.add-address-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 12px;
  background-color: #f8fafc;
  border: 1px dashed #cbd5e1;
  border-radius: 8px;
  color: #3b82f6;
  font-weight: 500;
  transition: all 0.3s ease;
}

.add-address-btn:hover {
  background-color: #eff6ff;
  border-color: #93c5fd;
}

.dark .add-address-btn {
  background-color: rgba(248, 250, 252, 0.05);
  border-color: #475569;
}

.dark .add-address-btn:hover {
  background-color: rgba(239, 246, 255, 0.05);
  border-color: #60a5fa;
}

.address-form-overlay,
.delete-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  overflow-y: auto;
  padding: 20px;
}

.address-form-container,
.delete-confirm-container {
  background-color: white;
  border-radius: 12px;
  width: 95%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  margin: auto;
  display: flex;
  flex-direction: column;
}

.dark .address-form-container,
.dark .delete-confirm-container {
  background-color: #1e293b;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

.address-form-header,
.delete-confirm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 10;
}

.dark .address-form-header,
.dark .delete-confirm-header {
  border-bottom-color: #334155;
  background-color: #1e293b;
}

.address-form-header h3,
.delete-confirm-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #0f172a;
}

.dark .address-form-header h3,
.dark .delete-confirm-header h3 {
  color: #f8fafc;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  color: #64748b;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: #f1f5f9;
  color: #334155;
}

.dark .close-btn:hover {
  background-color: #334155;
  color: #f8fafc;
}

.address-form {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.form-group {
  margin-bottom: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #475569;
}

.dark .form-group label {
  color: #cbd5e1;
}

.form-group input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #cbd5e1;
  border-radius: 6px;
  font-size: 16px;
  color: #0f172a;
  background-color: #fff;
  transition: all 0.2s ease;
}

.dark .form-group input {
  border-color: #475569;
  background-color: #1e293b;
  color: #f8fafc;
}

.form-group input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.dark .form-group input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.form-checkbox {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.form-checkbox input {
  margin-right: 8px;
}

.form-checkbox label {
  font-size: 14px;
  color: #475569;
}

.dark .form-checkbox label {
  color: #cbd5e1;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-btn,
.save-btn,
.confirm-btn {
  padding: 10px 16px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancel-btn {
  background-color: #f1f5f9;
  color: #475569;
}

.save-btn,
.confirm-btn {
  background-color: #3b82f6;
  color: white;
}

.cancel-btn:hover {
  background-color: #e2e8f0;
}

.save-btn:hover,
.confirm-btn:hover {
  background-color: #2563eb;
}

.dark .cancel-btn {
  background-color: #334155;
  color: #cbd5e1;
}

.dark .cancel-btn:hover {
  background-color: #475569;
}

.delete-confirm-content {
  padding: 20px;
  text-align: center;
  color: #475569;
}

.dark .delete-confirm-content {
  color: #cbd5e1;
}

.delete-confirm-actions {
  padding: 0 20px 20px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

.confirm-btn {
  background-color: #ef4444;
}

.confirm-btn:hover {
  background-color: #dc2626;
}

@media (max-width: 480px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .address-form-container,
  .delete-confirm-container {
    width: 100%;
    height: auto;
    max-height: 80vh;
    border-radius: 12px;
  }

  .address-form-overlay,
  .delete-confirm-overlay {
    padding: 10px;
    align-items: flex-end;
  }
}
</style>
