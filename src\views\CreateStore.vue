<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-white to-purple-50 p-4 md:p-6">
    <!-- 标题区域 - 减小上下间距 -->
    <div class="max-w-2xl mx-auto text-center space-y-3 mb-6">
      <h1
        class="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-blue-500"
      >
        创建您的店铺
      </h1>
      <p class="text-sm text-gray-600">填写详细信息，开启您的电商之旅</p>

      <!-- 进度指示器 - 减小尺寸 -->
      <div class="flex items-center justify-center gap-1.5 mt-3">
        <div class="w-2.5 h-2.5 rounded-full bg-purple-600"></div>
        <div class="w-16 h-0.5 bg-purple-600"></div>
        <div class="w-2.5 h-2.5 rounded-full bg-purple-200"></div>
        <div class="w-16 h-0.5 bg-purple-200"></div>
        <div class="w-2.5 h-2.5 rounded-full bg-purple-200"></div>
      </div>
    </div>

    <!-- 主表单区域 - 减小最大宽度和内边距 -->
    <div class="max-w-2xl mx-auto">
      <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        <form @submit.prevent="handleSubmit" class="p-5">
          <div class="space-y-5">
            <!-- 基本信息卡片 - 减小内边距 -->
            <div class="bg-white rounded-lg border border-gray-100 p-4 space-y-4">
              <div class="flex items-center justify-between border-b border-gray-100 pb-3">
                <div class="flex items-center gap-2">
                  <Store class="w-4 h-4 text-purple-600" />
                  <h2 class="text-base font-medium text-gray-900">基本信息</h2>
                </div>
                <span class="text-xs px-2 py-0.5 bg-purple-50 text-purple-600 rounded-full">
                  第 1 步 / 共 3 步
                </span>
              </div>

              <!-- 店铺名称 -->
              <div class="space-y-1.5">
                <div class="flex items-center justify-between">
                  <label class="block text-sm font-medium text-gray-700">
                    店铺名称 <span class="text-red-500">*</span>
                  </label>
                  <div class="text-xs text-gray-500 flex items-center gap-1">
                    <InfoIcon class="w-3 h-3" />
                    创建后需要审核才能修改
                  </div>
                </div>
                <div class="relative group">
                  <input
                    v-model="form.storeName"
                    type="text"
                    required
                    :class="[
                      'block w-full rounded-lg px-3 py-2.5 transition-all duration-300 outline-none text-base',
                      'border-2 focus:ring-2 focus:ring-offset-1',
                      touched.storeName && errors.storeName
                        ? 'border-red-300 focus:border-red-400 focus:ring-red-500'
                        : 'border-gray-200 group-hover:border-purple-300 focus:border-purple-500 focus:ring-purple-500',
                    ]"
                    placeholder="给您的店铺起个响亮的名字"
                    @blur="handleBlur('storeName')"
                    @input="handleInput('storeName')"
                  />
                  <Transition name="fade">
                    <CheckCircle2
                      v-if="touched.storeName && !errors.storeName && form.storeName"
                      class="absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 text-green-500"
                    />
                  </Transition>
                </div>
                <Transition name="slide">
                  <p
                    v-if="touched.storeName && errors.storeName"
                    class="flex items-center gap-1 text-xs text-red-500 mt-1"
                  >
                    <AlertCircle class="w-3 h-3" />
                    {{ errors.storeName }}
                  </p>
                </Transition>
              </div>

              <!-- 店铺状态 - 减小内边距和图标尺寸 -->
              <div class="space-y-1.5">
                <label class="block text-sm font-medium text-gray-700">经营状态</label>
                <div class="grid grid-cols-2 gap-3">
                  <button
                    type="button"
                    @click="handleStatusChange('active')"
                    class="relative p-3 rounded-lg border-2 transition-all duration-300 group overflow-hidden"
                    :class="[
                      form.storeStatus === 1
                        ? 'border-purple-500 bg-purple-50/50'
                        : 'border-gray-200 hover:border-purple-300',
                    ]"
                  >
                    <div class="relative flex items-center gap-2">
                      <div
                        class="flex items-center justify-center w-8 h-8 rounded-full bg-white shadow-sm"
                      >
                        <CheckCircle2
                          class="w-4 h-4 transition-colors duration-300"
                          :class="form.storeStatus === 1 ? 'text-purple-600' : 'text-gray-400'"
                        />
                      </div>
                      <div class="text-left">
                        <div
                          class="text-sm font-medium"
                          :class="form.storeStatus === 1 ? 'text-purple-700' : 'text-gray-700'"
                        >
                          立即营业
                        </div>
                        <div class="text-xs text-gray-500">创建后即可接单</div>
                      </div>
                    </div>
                  </button>
                  <button
                    type="button"
                    @click="handleStatusChange('closed')"
                    class="relative p-3 rounded-lg border-2 transition-all duration-300 group overflow-hidden"
                    :class="[
                      form.storeStatus === 0
                        ? 'border-purple-500 bg-purple-50/50'
                        : 'border-gray-200 hover:border-purple-300',
                    ]"
                  >
                    <div class="relative flex items-center gap-2">
                      <div
                        class="flex items-center justify-center w-8 h-8 rounded-full bg-white shadow-sm"
                      >
                        <PauseCircle
                          class="w-4 h-4 transition-colors duration-300"
                          :class="form.storeStatus === 0 ? 'text-purple-600' : 'text-gray-400'"
                        />
                      </div>
                      <div class="text-left">
                        <div
                          class="text-sm font-medium"
                          :class="form.storeStatus === 0 ? 'text-purple-700' : 'text-gray-700'"
                        >
                          稍后营业
                        </div>
                        <div class="text-xs text-gray-500">创建后手动开启</div>
                      </div>
                    </div>
                  </button>
                </div>
              </div>
            </div>

            <!-- 店铺介绍卡片 -->
            <div class="bg-white rounded-lg border border-gray-100 p-4 space-y-4">
              <div class="flex items-center justify-between border-b border-gray-100 pb-3">
                <div class="flex items-center gap-2">
                  <FileText class="w-4 h-4 text-purple-600" />
                  <h2 class="text-base font-medium text-gray-900">店铺介绍</h2>
                </div>
                <div class="flex items-center gap-2">
                  <span
                    class="text-xs px-2 py-0.5 bg-green-50 text-green-600 rounded-full flex items-center gap-1"
                  >
                    <CheckCircle2 class="w-3 h-3" />
                    选填信息
                  </span>
                </div>
              </div>

              <!-- 店铺描述 -->
              <div class="space-y-1.5">
                <div class="flex items-center justify-between">
                  <label class="block text-sm font-medium text-gray-700">店铺描述</label>
                  <div class="text-xs text-gray-500 flex items-center gap-1">
                    <Lightbulb class="w-3 h-3" />
                    详细的描述有助于顾客了解您的店铺
                  </div>
                </div>
                <div class="relative group">
                  <textarea
            v-model="form.storeDescription"
                    rows="3"
                    :class="[
                      'block w-full rounded-lg px-3 py-2 transition-all duration-300 outline-none resize-none text-sm',
                      'border-2 group-hover:border-purple-300 focus:border-purple-500 focus:ring-2 focus:ring-purple-500 focus:ring-offset-1',
                      form.storeDescription.length > 180 ? 'border-yellow-300' : 'border-gray-200',
                    ]"
                    placeholder="介绍一下您的店铺特色、经营理念等..."
                    maxlength="200"
                  ></textarea>
                  <span
                    class="absolute bottom-2 right-2 text-xs px-1.5 py-0.5 rounded-full transition-colors duration-300"
                    :class="[
                      form.storeDescription.length > 180
                        ? 'text-yellow-600 bg-yellow-50'
                        : form.storeDescription.length > 0
                          ? 'text-purple-600 bg-purple-50'
                          : 'text-gray-400',
                    ]"
                  >
                    {{ form.storeDescription.length }}/200
                  </span>
                </div>
              </div>

              <!-- 店铺地址 -->
              <div class="space-y-1.5">
                <div class="flex items-center justify-between">
                  <label class="block text-sm font-medium text-gray-700">店铺地址</label>
                  <button
                    type="button"
                    class="text-xs text-purple-600 flex items-center gap-1 hover:text-purple-700"
                  >
                    <MapPin class="w-3 h-3" />
                    定位当前位置
                  </button>
                </div>
                <div class="relative group">
                  <input
                    v-model="form.storeAddress"
                    type="text"
                    class="block w-full rounded-lg px-3 py-2 text-sm border-2 border-gray-200 transition-all duration-300 outline-none group-hover:border-purple-300 focus:border-purple-500 focus:ring-2 focus:ring-purple-500 focus:ring-offset-1"
                    placeholder="请输入店铺详细地址"
                  />
                  <MapPin
                    class="absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 group-hover:text-purple-500 transition-colors duration-300"
                  />
                </div>
              </div>
            </div>

            <!-- 店铺形象卡片 -->
            <div class="bg-white rounded-lg border border-gray-100 p-4 space-y-4">
              <div class="flex items-center justify-between border-b border-gray-100 pb-3">
                <div class="flex items-center gap-2">
                  <ImageIcon class="w-4 h-4 text-purple-600" />
                  <h2 class="text-base font-medium text-gray-900">店铺形象</h2>
                </div>
                <div class="flex items-center gap-2">
                  <span
                    class="text-xs px-2 py-0.5 bg-blue-50 text-blue-600 rounded-full flex items-center gap-1"
                  >
                    <ImageIcon class="w-3 h-3" />
                    支持多种格式
                  </span>
                </div>
              </div>

              <!-- 店铺照片上传 -->
              <div class="space-y-1.5">
                <div class="flex items-center justify-between">
                  <label class="block text-sm font-medium text-gray-700">店铺照片</label>
                  <div class="text-xs text-gray-500 flex items-center gap-1">
                    <Camera class="w-3 h-3" />
                    建议尺寸 800x600
                  </div>
                </div>
                <div
                  class="relative group cursor-pointer"
                  @dragenter.prevent="isDragging = true"
                  @dragleave.prevent="isDragging = false"
                  @dragover.prevent
                  @drop.prevent="handlePhotoDrop"
                  @click="triggerFileInput"
                >
                  <div
                    class="flex justify-center items-center h-[200px] px-4 border-2 rounded-lg transition-all duration-300"
                    :class="[
                      isDragging
                        ? 'border-purple-500 bg-purple-50 scale-[1.01]'
                        : 'border-gray-300',
                      form.photo
                        ? 'border-solid hover:border-purple-400'
                        : 'border-dashed hover:border-purple-500',
                      'hover:shadow-lg',
                    ]"
                  >
                    <div v-if="form.photo" class="relative group w-full h-full p-3">
                      <img
                        :src="form.photo"
                        alt="店铺照片预览"
                        class="w-full h-full object-contain rounded-lg transition-all duration-300 group-hover:scale-[1.02]"
                      />
                      <div
                        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 rounded-lg flex items-center justify-center"
                      >
                        <div
                          class="flex gap-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0"
                        >
                          <button
                            type="button"
                            @click.stop="triggerFileInput"
                            class="p-1.5 bg-white rounded-full text-gray-700 hover:text-purple-600 hover:scale-110 transition-all duration-300"
                          >
                            <Upload class="w-4 h-4" />
                          </button>
                          <button
                            type="button"
                            @click.stop="removePhoto"
                            class="p-1.5 bg-white rounded-full text-gray-700 hover:text-red-600 hover:scale-110 transition-all duration-300"
                          >
                            <Trash class="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                    <div
                      v-else
                      class="text-center transform group-hover:scale-110 transition-all duration-300"
                    >
                      <Upload
                        class="mx-auto h-12 w-12 text-gray-400 group-hover:text-purple-500 transition-colors duration-300"
                      />
                      <p
                        class="mt-3 text-sm text-gray-500 group-hover:text-purple-600 transition-colors duration-300"
                      >
                        点击或拖拽上传店铺照片
                      </p>
                      <p class="mt-1 text-xs text-gray-400">支持 PNG, JPG, GIF 格式，不超过 5MB</p>
                    </div>
                  </div>
                  <input
                    ref="fileInput"
                    type="file"
                    class="hidden"
                    accept="image/*"
                    @change="handlePhotoUpload"
                  />
                </div>
              </div>
            </div>

            <!-- 创建提示 -->
            <div class="bg-red-50 rounded-lg p-3 border-l-4 border-red-500">
              <div class="flex">
                <div class="flex-shrink-0">
                  <AlertTriangle class="h-4 w-4 text-red-500" />
                </div>
                <div class="ml-2">
                  <h3 class="text-xs font-medium text-red-800">创建提示</h3>
                  <div class="mt-1 text-xs text-red-700">
                    <ul class="list-disc pl-4 space-y-0.5 marker:text-red-500">
                      <li>店铺名称创建后需要审核才能修改</li>
                      <li>请确保上传的店铺照片清晰且具有代表性</li>
                      <li>详细的店铺描述将帮助顾客更好地了解您的店铺</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex items-center justify-between pt-2">
              <div class="flex items-center gap-1.5 text-xs text-gray-500">
                <Clock class="w-3 h-3" />
                <span>预计完成时间：2-3 分钟</span>
              </div>
              <div class="flex items-center gap-3">
                <button
                  type="button"
                  class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium text-purple-600 bg-purple-50 hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-purple-500 transition-all duration-300"
                >
                  <Save class="w-4 h-4 mr-1.5" />
                  保存草稿
                </button>
                <button
                  type="submit"
                  :disabled="isSubmitting || hasErrors"
                  class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-blue-500 hover:from-purple-700 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-md shadow-purple-500/25 hover:shadow-lg hover:shadow-purple-500/30"
                >
                  <template v-if="isSubmitting">
                    <Loader2 class="w-4 h-4 mr-1.5 animate-spin" />
                    创建中...
                  </template>
                  <template v-else>
                    <Store class="w-4 h-4 mr-1.5" />
                    创建店铺
                  </template>
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, h } from 'vue'
import { useRouter } from 'vue-router'
import request from '@/utils/request'
import { ElMessage } from 'element-plus'
import {
  Store,
  Upload,
  Trash,
  CheckCircle2,
  PauseCircle,
  MapPin,
  Loader2,
  ImageIcon,
  AlertCircle,
  AlertTriangle,
  Save,
  InfoIcon,
  FileText,
  Lightbulb,
  Camera,
  Clock,
} from 'lucide-vue-next'
import type { ca } from 'element-plus/es/locales.mjs'

const router = useRouter()

// 类型定义
interface StoreForm {
  OSSAccessKeyId: string
  policy: string
  Signature: string
  key: string
  host: string
  dir: string
  storeName: string
  photo: string // 存储文件名
  photoFile?: File // 存储文件对象
  storeStatus: number
  storeDescription: string
  storeAddress: string
}

interface FormErrors {
  [key: string]: string
}

interface TouchedFields {
  [key: string]: boolean
}

// 状态管理
const isSubmitting = ref(false)
const isDragging = ref(false)
const fileInput = ref<HTMLInputElement | null>(null)
const photoName = ref('')
const form = reactive<StoreForm>({
  OSSAccessKeyId: '',
  policy: '',
  Signature: '',
  key: '',
  host: '',
  dir: '',
  storeName: '',
  photo: '',
  photoFile: undefined,
  storeStatus: 1,
  storeDescription: '',
  storeAddress: '',
})

const errors = reactive<FormErrors>({})
const touched = reactive<TouchedFields>({
  storeName: false,
})

// 计算属性
const hasErrors = computed(() => Object.keys(errors).length > 0)

// 表单验证
const validateField = (field: keyof StoreForm) => {
  delete errors[field]

  switch (field) {
    case 'storeName':
      if (!form.storeName.trim()) {
        errors[field] = '请输入店铺名称'
      } else if (form.storeName.length < 2) {
        errors[field] = '店铺名称至少需要2个字符'
      } else if (form.storeName.length > 50) {
        errors[field] = '店铺名称不能超过50个字符'
      }
      break
  }
}

const handleBlur = (field: keyof StoreForm) => {
  touched[field] = true
  validateField(field)
}

const handleInput = (field: keyof StoreForm) => {
  if (touched[field]) {
    validateField(field)
  }
}

// 图片处理
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handlePhotoUpload = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    processImageFile(file)
  }
}

const handlePhotoDrop = (event: DragEvent) => {
  isDragging.value = false
  const file = event.dataTransfer?.files[0]
  if (file && file.type.startsWith('image/')) {
    processImageFile(file)
  } else {
    ElMessage.warning('请上传图片文件')
  }
}

const processImageFile = async (file: File) => {
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.warning('文件大小不能超过5MB')
    return
  }
  try {
    // 步骤1: 获取 OSS 上传凭证
    const token = localStorage.getItem('token')
    const res = await request.get('/oss', {
      headers: {
        token: token,
        'Content-Type': 'application/json',
      },
    })

    console.log('完整的OSS响应:', res)
    // 构建 OSS 上传请求
    const ossData = res.data
    console.log('OSS数据:', ossData)
    // 直接使用响应数据
    const formData = new FormData()
    // 按照阿里云要求的顺序添加表单字段
    formData.append('success_action_status', '200')
    formData.append('policy', ossData.policy)
    formData.append('x-oss-signature', ossData.signature)
    formData.append('x-oss-signature-version', ossData.version)
    formData.append('x-oss-credential', ossData.credential)
    formData.append('x-oss-date', ossData.ossdate)
    // URL 编码文件名

    formData.append('key', ossData.dir + file.name)
    formData.append('x-oss-security-token', ossData.token)
    formData.append('file', file)
    // 步骤3: 上传文件到 OSS
    console.log('开始上传到OSS:', ossData.host)
    console.log('上传的表单数据:', {
      version: ossData.version,
      credential: ossData.credential,
      date: ossData.ossdate,
      token: ossData.token.substring(0, 20) + '...',
      key: `${ossData.dir}${file.name}`,
    })

    const uploadRes = await fetch(ossData.host, {
      method: 'POST',
      body: formData,
    })

    console.log('OSS上传状态:', uploadRes.status)
    const responseText = await uploadRes.text()
    if (uploadRes.ok) {
      // 步骤4: 保存 OSS 文件路径
      form.photo = `${ossData.host}/${ossData.dir}${file.name}`
      form.photoFile = file
      ElMessage.success('文件上传成功')
    } else {
      throw new Error(`文件上传失败: ${uploadRes.status} ${responseText}`)
    }
  } catch (error) {
    console.error('文件处理失败:', error)
    ElMessage.error('文件上传失败，请重试')
  }
}

const removePhoto = () => {
  form.photo = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 表单提交
const handleSubmit = async () => {
  // 验证所有必填字段
  Object.keys(form).forEach((key) => {
    if (key === 'storeName') {
      touched[key] = true
      validateField(key as keyof StoreForm)
    }
  })
  console.log(hasErrors.value)
  if (hasErrors.value) {
    ElMessage.error('请检查表单填写是否正确')
    return
  }
  isSubmitting.value = true
  try {
    const token = localStorage.getItem('token')
    console.log(token)
    if (!token) {
      ElMessage.error('请先登录')
      router.push('/login')
      return
    }

    // 创建请求数据对象
    const requestData = {
      storeName: form.storeName,
      photo: form.photo,
      storeStatus: form.storeStatus,
      storeDescription: form.storeDescription,
      storeAddress: form.storeAddress,
      sellerId: JSON.parse(localStorage.getItem('user') || '{}').id,
    }

    const res = await request.post('/store/create', requestData, {
      headers: {
        token: token,
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    })
    console.log(res)
    if (res.data.code === 1) {
          ElMessage.success('店铺创建成功')
      router.push('/home')
        } else {
      throw new Error(res.data.msg || '创建失败')
        }
  } catch (error: unknown) {
    console.error('Submit error:', error)
        ElMessage.error('创建失败，请重试')
      } finally {
    isSubmitting.value = false
      }
    }

const handleStatusChange = (status: 'active' | 'closed') => {
  form.storeStatus = status === 'active' ? 1 : 0
}
</script>

<style scoped>
/* 渐变动画 */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-animate {
  background-size: 200% 200%;
  animation: gradient 15s ease infinite;
}

/* 过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 输入框聚焦效果 */
input:focus::placeholder,
textarea:focus::placeholder {
  color: #a855f7;
  transition: color 0.3s ease;
}

/* 自定义滚动条 */
textarea::-webkit-scrollbar {
  width: 6px;
}

textarea::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

textarea::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

textarea::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
