<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-orange-50 to-blue-50">
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div
        class="absolute top-0 left-0 w-[500px] h-[500px] bg-purple-200 rounded-full opacity-20 blur-3xl -translate-x-1/2 -translate-y-1/2"
      ></div>
      <div
        class="absolute top-0 right-0 w-[500px] h-[500px] bg-orange-200 rounded-full opacity-20 blur-3xl translate-x-1/2 -translate-y-1/2"
      ></div>
      <div
        class="absolute bottom-0 left-1/2 w-[500px] h-[500px] bg-blue-200 rounded-full opacity-20 blur-3xl -translate-x-1/2 translate-y-1/2"
      ></div>
    </div>

    <el-container class="relative">
      <!-- Enhanced Wider Sidebar -->
      <el-aside width="360px" class="bg-white/70 backdrop-blur-xl shadow-lg">
        <!-- Profile Section -->
        <div class="p-6 border-b border-slate-100">
          <div class="flex items-center gap-4 mb-4">
            <div class="relative group">
              <el-avatar
                :size="60"
                :src="
                  userPhotoUrl ||
                  'https://hiram.oss-cn-beijing.aliyuncs.com/%E9%BB%98%E8%AE%A4%E5%A4%B4%E5%83%8F/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250302163653.jpg'
                "
                :icon="UserFilled"
                class="ring-2 ring-purple-500 ring-offset-2 transition-transform duration-300 group-hover:scale-105 !bg-purple-100 !text-purple-600"
              />
            </div>
            <div>
              <h2 class="text-lg font-semibold text-slate-800">{{ userEmail }}</h2>
              <div class="flex items-center gap-2">
                <el-tag size="small" type="success" effect="plain">已认证</el-tag>
                <span class="text-sm text-purple-600">Premium</span>
              </div>
            </div>
          </div>

          <!-- Account Status -->
          <div class="bg-gradient-to-r from-purple-500/10 to-purple-500/5 rounded-lg p-4 mb-4">
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-purple-700">账户状态</span>
              <el-tag size="small" type="success" effect="light">正常</el-tag>
            </div>
          </div>
        </div>

        <!-- Enhanced Menu -->
        <el-menu
          :default-active="activeMenu"
          class="border-none !bg-transparent"
          @select="handleSelect"
        >
          <div class="px-4 mb-2">
            <span class="text-xs font-medium text-purple-600 uppercase tracking-wider"
              >个人中心</span
            >
          </div>

          <el-menu-item index="profile" class="menu-item-custom">
            <el-icon><User /></el-icon>
            <template #title>
              <div class="flex justify-between items-center w-full">
                <span>个人资料</span>
                <el-tag size="small" effect="plain" type="success">已完善</el-tag>
              </div>
            </template>
          </el-menu-item>

          <el-menu-item index="security" class="menu-item-custom">
            <el-icon><Lock /></el-icon>
            <span>账户安全</span>
          </el-menu-item>

          <div class="px-4 my-2">
            <span class="text-xs font-medium text-purple-600 uppercase tracking-wider"
              >地址管理</span
            >
          </div>

          <el-menu-item index="address" class="menu-item-custom">
            <el-icon><Location /></el-icon>
            <span>地址管理</span>
          </el-menu-item>

          <div class="px-4 my-2">
            <span class="text-xs font-medium text-purple-600 uppercase tracking-wider"
              >订单管理</span
            >
          </div>

          <el-menu-item index="orders" class="menu-item-custom">
            <el-icon><ShoppingBag /></el-icon>
            <template #title>
              <div class="flex justify-between items-center w-full">
                <span>我的订单</span>
                <el-tag size="small" effect="plain" type="info">全部</el-tag>
              </div>
            </template>
          </el-menu-item>

          <el-menu-item index="tracking" class="menu-item-custom">
            <el-icon><Van /></el-icon>
            <span>物流追踪</span>
          </el-menu-item>

          <el-menu-item index="refunds" class="menu-item-custom">
            <el-icon><RefreshLeft /></el-icon>
            <span>退款管理</span>
          </el-menu-item>
        </el-menu>

        <!-- Bottom Status Section -->
        <div
          class="absolute bottom-0 w-full p-4 border-t border-slate-100 bg-white/50 backdrop-blur-sm"
        >
          <div class="flex flex-col gap-3">
            <!-- 语言切换按钮 -->
            <div class="mb-2">
              <ul class="ignore flex items-center justify-center space-x-2 text-sm">
                <li>
                  <a
                    href="javascript:void(0);"
                    class="text-purple-600 hover:text-purple-800"
                    @click="changeLanguage('english')"
                    >English</a
                  >
                </li>
                <li class="text-gray-400">|</li>
                <li>
                  <a
                    href="javascript:void(0);"
                    class="text-purple-600 hover:text-purple-800"
                    @click="changeLanguage('chinese_simplified')"
                    >简体中文</a
                  >
                </li>
                <li class="text-gray-400">|</li>
                <li>
                  <a
                    href="javascript:void(0);"
                    class="text-purple-600 hover:text-purple-800"
                    @click="changeLanguage('chinese_traditional')"
                    >繁體中文</a
                  >
                </li>
              </ul>
            </div>

            <!-- 返回主页按钮 -->
            <el-button
              type="primary"
              class="w-full flex items-center justify-center gap-2"
              @click="goToHome"
            >
              <el-icon><HomeFilled /></el-icon>
              <span>返回主页</span>
            </el-button>

            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <div class="relative">
                  <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div
                    class="w-2 h-2 bg-green-500 rounded-full absolute top-0 left-0 animate-ping"
                  ></div>
                </div>
                <span class="text-sm text-slate-600">系统运行正常</span>
              </div>
              <el-tooltip content="刷新状态" placement="top">
                <el-button
                  type="primary"
                  size="small"
                  class="!bg-purple-500 !border-purple-500 hover:!bg-purple-600 transition-all duration-300"
                >
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </div>
        </div>
      </el-aside>

      <!-- Main Content Area -->
      <el-main class="p-6 relative">
        <router-view v-slot="{ Component, route }">
          <transition name="fade" mode="out-in">
            <keep-alive :include="[]">
              <component :is="Component" :key="route.fullPath" />
            </keep-alive>
          </transition>
        </router-view>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, inject, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  User,
  Lock,
  Shop,
  Box,
  Medal,
  ShoppingCart,
  Wallet,
  CreditCard,
  Refresh,
  UserFilled,
  HomeFilled,
  Location,
  ShoppingBag,
  Van,
  RefreshLeft,
} from '@element-plus/icons-vue'
import { getBuyerInfo } from '@/api/buyer'
import { ElMessage } from 'element-plus'
import { profile } from 'console'

const router = useRouter()

// 注入全局语言变量和方法
const globalCurrentLanguage = inject('currentLanguage')
const globalChangeLanguage = inject('changeLanguage')

// 本地语言状态，绑定到全局状态
const currentLanguage = computed({
  get: () => globalCurrentLanguage?.value || 'chinese_simplified',
  set: (value) => {
    if (globalCurrentLanguage) {
      globalCurrentLanguage.value = value
    }
  },
})

// 语言切换函数使用全局方法
const changeLanguage = (lang: string) => {
  if (globalChangeLanguage) {
    globalChangeLanguage(lang)
  } else {
    // 后备方案
    localStorage.setItem('selectedLanguage', lang)
    currentLanguage.value = lang
    if (window.translate) {
      window.translate.changeLanguage(lang)
    }
  }
}

// 返回主页面
const goToHome = () => {
  router.push('/')
}
const activeMenu = ref(localStorage.getItem('activeMenu') || 'profile')
const userEmail = ref('')
const userPhotoUrl = ref('')
const loading = ref(false)

// 从后端获取用户信息
const getUserInfo = async () => {
  loading.value = true
  try {
    const response = await getBuyerInfo()
    if (response.data && response.data.code === 1 && response.data.data) {
      const userInfo = response.data.data
      userEmail.value = userInfo.accountName || userInfo.email || '未知用户'
      userPhotoUrl.value = userInfo.photoUrl
        ? userInfo.photoUrl.trim()
        : 'https://hiram.oss-cn-beijing.aliyuncs.com/%E9%BB%98%E8%AE%A4%E5%A4%B4%E5%83%8F/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250302163653.jpg'
    } else {
      throw new Error('获取用户信息失败')
    }
  } catch (error) {
    console.error('获取用户信息出错:', error)
    ElMessage.error('获取用户信息失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取用户信息
onMounted(() => {
  router
    .push({
      path: `/dashboard/profile`,
    })
    .catch((err) => {
      // 避免NavigationDuplicated错误显示在控制台
      if (err.name !== 'NavigationDuplicated') {
        console.error('路由导航错误:', err)
        ElMessage.error('页面加载失败，请重试')
      }
    })
  getUserInfo()

  // 动态加载translate.js
  if (!document.getElementById('translate-js')) {
    const script = document.createElement('script')
    script.id = 'translate-js'
    script.src = 'https://cdn.staticfile.net/translate.js/3.12.0/translate.js'
    script.onload = () => {
      // 初始化translate.js
      window.translate.selectLanguageTag.show = false
      window.translate.service.use('client.edge')

      // 读取保存的语言设置
      const savedLanguage = localStorage.getItem('selectedLanguage') || 'chinese_simplified'
      currentLanguage.value = savedLanguage

      // 应用语言设置
      window.translate.changeLanguage(savedLanguage)
      window.translate.execute()
    }
    document.head.appendChild(script)
  }
})

const handleSelect = (key: string) => {
  console.log(key)

  activeMenu.value = key
  localStorage.setItem('activeMenu', key)
  // 使用子路由路径进行导航
  router
    .push({
      path: `/dashboard/${key}`,
    })
    .catch((err) => {
      // 避免NavigationDuplicated错误显示在控制台
      if (err.name !== 'NavigationDuplicated') {
        console.error('路由导航错误:', err)
        ElMessage.error('页面加载失败，请重试')
      }
    })
}
</script>

<style scoped>
.el-aside {
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  transition: all 0.3s ease;
}

.el-main {
  margin-left: 360px;
}

.menu-item-custom {
  @apply transition-all duration-200 hover:bg-purple-50;
  height: 50px !important;
  line-height: 50px !important;
  margin: 4px 8px !important;
  border-radius: 8px !important;
}

.menu-sub-custom :deep(.el-menu-item) {
  margin: 4px 8px !important;
  border-radius: 8px !important;
}

:deep(.el-menu-item.is-active) {
  background: linear-gradient(to right, rgb(237 233 254), rgb(245 243 255)) !important;
  color: rgb(147 51 234) !important;
}

:deep(.el-menu-item:hover) {
  background-color: rgb(243 244 246) !important;
}

:deep(.el-sub-menu__title) {
  margin: 4px 8px !important;
  border-radius: 8px !important;
}

:deep(.el-sub-menu__title:hover) {
  background-color: rgb(243 244 246) !important;
}

.el-menu {
  border-right: none !important;
}

/* Transition for route changes */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

:deep(.el-button--primary) {
  --el-button-bg-color: rgb(168 85 247) !important;
  --el-button-border-color: rgb(168 85 247) !important;
  --el-button-hover-bg-color: rgb(147 51 234) !important;
  --el-button-hover-border-color: rgb(147 51 234) !important;
}

/* 添加ignore类样式 */
.ignore {
  pointer-events: auto; /* 确保按钮可以点击 */
}
</style>
