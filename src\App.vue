<template>
  <router-view />
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, provide, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()
let tokenCheckInterval: number
// 添加全局语言状态
const currentLanguage = ref(localStorage.getItem('selectedLanguage') || 'chinese_simplified')

const clearLoginInfo = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('user')
  localStorage.removeItem('tokenTimestamp')
}

// 全局语言切换方法
const changeLanguage = (lang: string) => {
  localStorage.setItem('selectedLanguage', lang)
  currentLanguage.value = lang

  if (window.translate) {
    window.translate.changeLanguage(lang)
  }
}

// 提供全局语言方法和状态
provide('currentLanguage', currentLanguage)
provide('changeLanguage', changeLanguage)

onMounted(() => {
  // 每分钟检查一次 token
  tokenCheckInterval = setInterval(() => {
    const token = localStorage.getItem('token')
    if (token) {
      clearLoginInfo()
      ElMessage.warning('登录已过期，请重新登录')
      // router.push('/login')
    }
  }, 300 * 10000)

  // 全局初始化语言
  const initTranslate = () => {
    // 确保状态已经被提供
    provide('currentLanguage', currentLanguage)
    provide('changeLanguage', changeLanguage)

    if (!document.getElementById('translate-js')) {
      const script = document.createElement('script')
      script.id = 'translate-js'
      script.src = 'https://cdn.staticfile.net/translate.js/3.12.0/translate.js'
      script.onload = () => {
        const savedLanguage = localStorage.getItem('selectedLanguage') || 'chinese_simplified'
        currentLanguage.value = savedLanguage

        window.translate.selectLanguageTag.show = false
        window.translate.service.use('client.edge')
        window.translate.changeLanguage(savedLanguage)
        window.translate.execute()
      }
      document.head.appendChild(script)
    } else if (window.translate) {
      // 如果脚本已加载，确保应用正确的语言设置
      const savedLanguage = localStorage.getItem('selectedLanguage') || 'chinese_simplified'
      window.translate.changeLanguage(savedLanguage)
    }
  }

  // 执行初始化
  initTranslate()
})

onUnmounted(() => {
  // 清除定时器
  clearInterval(tokenCheckInterval)
})
</script>

<style>
/* 全局样式 */
</style>
