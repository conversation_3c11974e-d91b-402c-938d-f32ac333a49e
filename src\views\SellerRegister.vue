<template>
  <div
    class="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50"
  >
    <!-- Animated background elements -->
    <div class="absolute inset-0 overflow-hidden">
      <div
        class="absolute top-0 left-0 w-[500px] h-[500px] bg-purple-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"
      ></div>
      <div
        class="absolute top-0 right-0 w-[500px] h-[500px] bg-indigo-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000"
      ></div>
      <div
        class="absolute -bottom-8 left-20 w-[500px] h-[500px] bg-pink-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-4000"
      ></div>
      <div
        class="absolute bottom-20 right-20 w-[500px] h-[500px] bg-blue-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-6000"
      ></div>
    </div>

    <!-- Floating elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div
        class="floating-element absolute top-20 left-[20%] w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-400 rounded-lg opacity-40"
      ></div>
      <div
        class="floating-element animation-delay-2000 absolute top-40 right-[25%] w-6 h-6 bg-gradient-to-br from-blue-400 to-indigo-400 rounded-full opacity-40"
      ></div>
      <div
        class="floating-element animation-delay-4000 absolute bottom-32 left-[35%] w-10 h-10 bg-gradient-to-br from-pink-400 to-purple-400 rounded-lg rotate-45 opacity-40"
      ></div>
      <div
        class="floating-element animation-delay-6000 absolute bottom-40 right-[30%] w-8 h-8 bg-gradient-to-br from-indigo-400 to-blue-400 rounded-full opacity-40"
      ></div>
    </div>

    <!-- Main content -->
    <div class="w-full max-w-md relative z-10">
      <!-- Glass morphism card -->
      <div class="backdrop-blur-xl bg-white/80 rounded-3xl shadow-2xl p-8 space-y-8">
        <!-- Logo and Title -->
        <div class="text-center space-y-4">
          <div
            class="w-20 h-20 mx-auto bg-gradient-to-br from-purple-600 to-indigo-600 rounded-2xl shadow-lg transform rotate-45 overflow-hidden animate-pulse"
          >
            <div
              class="w-full h-full transform -rotate-45 flex items-center justify-center text-white font-bold text-2xl"
            >
              MB
            </div>
          </div>
          <div>
            <h2
              class="text-3xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent"
            >
              {{ $t('sellerRegister.title') }}
            </h2>
            <p class="mt-2 text-gray-600">{{ $t('sellerRegister.subtitle') }}</p>
          </div>
        </div>

        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          class="space-y-6"
          @submit.prevent="handleRegister"
        >
          <el-form-item prop="shopName" class="form-item-hover">
            <el-input
              v-model="form.shopName"
              :placeholder="$t('sellerRegister.shopNamePlaceholder')"
              prefix-icon="Shop"
              class="custom-input"
            />
          </el-form-item>

          <el-form-item prop="mobile" class="form-item-hover">
            <el-input
              v-model="form.mobile"
              :placeholder="$t('sellerRegister.mobilePlaceholder')"
              prefix-icon="Phone"
              class="custom-input"
            />
          </el-form-item>

          <el-form-item prop="contactName" class="form-item-hover">
            <el-input
              v-model="form.contactName"
              :placeholder="$t('sellerRegister.contactNamePlaceholder')"
              prefix-icon="User"
              class="custom-input"
            />
          </el-form-item>

          <el-form-item prop="province" class="form-item-hover">
            <el-input
              v-model="form.province"
              :placeholder="$t('sellerRegister.provincePlaceholder')"
              prefix-icon="Location"
              class="custom-input"
            />
          </el-form-item>

          <el-form-item prop="city" class="form-item-hover">
            <el-input
              v-model="form.city"
              :placeholder="$t('sellerRegister.cityPlaceholder')"
              prefix-icon="Location"
              class="custom-input"
            />
          </el-form-item>

          <el-form-item prop="detailAddress" class="form-item-hover">
            <el-input
              v-model="form.detailAddress"
              :placeholder="$t('sellerRegister.detailAddressPlaceholder')"
              prefix-icon="Location"
              class="custom-input"
            />
          </el-form-item>

          <el-form-item>
            <button
              type="submit"
              :disabled="loading"
              class="w-full py-3 px-4 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <div class="flex items-center justify-center">
                <LoaderIcon v-if="loading" class="w-5 h-5 mr-2 animate-spin" />
                <span>{{
                  loading ? $t('sellerRegister.submitting') : $t('sellerRegister.submitButton')
                }}</span>
              </div>
            </button>
          </el-form-item>

          <!-- Login Link -->
          <div class="text-center mt-6">
            <p class="text-sm text-gray-600">
              {{ $t('sellerRegister.haveAccount') }}
              <router-link to="/login" class="text-purple-600 hover:text-purple-500 font-medium">
                {{ $t('sellerRegister.loginNow') }}
              </router-link>
            </p>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { merchantApi } from '@/api/merchant'
import type { AxiosError } from 'axios'
import type { MerchantRegisterDTO } from '@/api/types'
import { useI18n } from 'vue-i18n' // 导入 i18n

const { t } = useI18n() // 使用 i18n

const router = useRouter()
const formRef = ref<FormInstance>()
const loading = ref(false)

const form = ref<MerchantRegisterDTO>({
  shopName: '',
  mobile: '',
  contactName: '',
  province: '',
  city: '',
  detailAddress: '',
})

const rules = reactive<FormRules>({
  shopName: [
    { required: true, message: '请输入店铺名称', trigger: 'blur' },
    { min: 2, max: 50, message: '店铺名称长度在2到50个字符之间', trigger: 'blur' },
  ],
  mobile: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' },
  ],
  contactName: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '联系人姓名长度在2到50个字符之间', trigger: 'blur' },
  ],
  province: [
    { required: true, message: '请输入省份', trigger: 'blur' },
    { min: 2, max: 50, message: '省份长度在2到50个字符之间', trigger: 'blur' },
  ],
  city: [
    { required: true, message: '请输入城市', trigger: 'blur' },
    { min: 2, max: 50, message: '城市长度在2到50个字符之间', trigger: 'blur' },
  ],
  detailAddress: [
    { required: true, message: '请输入详细地址', trigger: 'blur' },
    { min: 5, max: 100, message: '地址长度在5到100个字符之间', trigger: 'blur' },
  ],
})

const handleRegister = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate(async (valid) => {
      if (valid) {
        loading.value = true
        try {
          const response = await merchantApi.register(form.value)
          console.log('后端返回数据：', response)

          const { code, msg } = response.data

          if (code === 1) {
            console.log('显示弹窗')
            ElMessage.success('提交成功')
            router.push({
              path: '/login',
              query: { showSuccess: 'true' },
            })
          } else {
            ElMessage.error(msg || '提交失败')
          }
        } catch (error) {
          console.error('Register error:', error)
          const axiosError = error as AxiosError<{ msg: string }>
          const errorMsg = axiosError.response?.data?.msg
          ElMessage.error(errorMsg || '提交失败，请重试')
        } finally {
          loading.value = false
        }
      }
    })
  } catch (error) {
    console.error('Form validation error:', error)
    ElMessage.error('表单验证失败，请检查输入')
  }
}
</script>

<style scoped>
/* 背景动画 */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(10deg);
  }
  100% {
    transform: translateY(0px) rotate(0deg);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.floating-element {
  animation: float 6s ease-in-out infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

.animation-delay-6000 {
  animation-delay: 6s;
}

/* 输入框样式 */
.custom-input :deep(.el-input__wrapper) {
  background: white;
  border: 2px solid transparent;
  border-radius: 1rem;
  height: 48px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.custom-input :deep(.el-input__wrapper:hover) {
  border-color: #e9d5ff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.custom-input :deep(.el-input__wrapper.is-focus) {
  border-color: #9333ea;
  box-shadow: 0 0 0 4px rgba(147, 51, 234, 0.1);
}

.custom-input :deep(.el-input__inner) {
  height: 48px;
  color: #1f2937;
}

.custom-input :deep(.el-input__inner::placeholder) {
  color: #9ca3af;
}

.custom-input :deep(.el-input__prefix-icon) {
  color: #9333ea;
  font-size: 1.2em;
}

/* 表单项悬浮效果 */
.form-item-hover {
  transition: transform 0.3s ease;
}

.form-item-hover:hover {
  transform: translateY(-2px);
}
</style>
