<template>
  <div class="min-h-screen bg-gradient-to-br from-violet-50 via-indigo-50 to-blue-50 py-10 px-4">
    <div class="max-w-3xl mx-auto">
      <!-- 顶部标题 -->
      <div class="bg-white rounded-t-2xl shadow-md p-6 mb-1 relative overflow-hidden">
        <div
          class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-violet-500 to-indigo-500"
        ></div>
        <div class="flex justify-between items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">订单支付成功</h1>
            <p class="text-gray-500 mt-1">感谢您的购买，以下是您的订单详情</p>
          </div>
          <div class="bg-green-50 px-4 py-2 rounded-lg">
            <span class="text-green-600 font-medium">支付成功</span>
          </div>
        </div>
      </div>

      <!-- 订单信息卡片 -->
      <div class="bg-white rounded-2xl shadow-md p-6 mb-6">
        <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-100">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-violet-100 rounded-full flex items-center justify-center mr-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-violet-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div>
              <h2 class="text-lg font-semibold text-gray-800">订单号：{{ orderData.orderId }}</h2>
              <p class="text-sm text-gray-500">{{ formatDate(orderData.orderTime) }}</p>
            </div>
          </div>
        </div>

        <!-- 订单详细信息 -->
        <div class="mb-6 pb-4 border-b border-gray-100">
          <h3 class="text-lg font-semibold text-gray-800 mb-3">订单详情</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="bg-gray-50 p-3 rounded-lg">
              <p class="text-sm text-gray-500">订单编号</p>
              <p class="font-medium text-gray-800">{{ orderData.orderId }}</p>
            </div>
            <div class="bg-gray-50 p-3 rounded-lg">
              <p class="text-sm text-gray-500">下单时间</p>
              <p class="font-medium text-gray-800">{{ formatDate(orderData.orderTime) }}</p>
            </div>
            <div class="bg-gray-50 p-3 rounded-lg">
              <p class="text-sm text-gray-500">支付状态</p>
              <p class="font-medium text-green-600">已支付</p>
            </div>
            <div class="bg-gray-50 p-3 rounded-lg">
              <p class="text-sm text-gray-500">订单状态</p>
              <p class="font-medium text-blue-600">待发货</p>
            </div>
          </div>
        </div>

        <!-- 商品信息 -->
        <div class="mb-6 pb-6 border-b border-gray-100">
          <h3 class="text-lg font-semibold text-gray-800 mb-3">商品信息</h3>
          <div class="flex items-center">
            <div class="w-20 h-20 rounded-lg overflow-hidden mr-4 bg-gray-50 flex-shrink-0">
              <img
                :src="orderData.productImg"
                :alt="orderData.productName"
                class="w-full h-full object-cover"
                @error="handleImageError"
              />
            </div>
            <div class="flex-grow">
              <h3 class="font-medium text-gray-800">{{ orderData.productName }}</h3>
              <p class="text-sm text-gray-500 mt-1">{{ orderData.productAttributes }}</p>
              <div class="flex justify-between items-center mt-2">
                <span class="text-sm text-gray-600">数量: {{ orderData.productQuantity }}</span>
                <span class="font-medium text-violet-600"
                  >${{ formatPrice(orderData.productPrice) }}</span
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 收货信息 -->
        <div class="mb-6 pb-6 border-b border-gray-100">
          <h3 class="text-lg font-semibold text-gray-800 mb-3">收货信息</h3>
          <div class="bg-gray-50 p-4 rounded-lg">
            <p v-if="orderData.shippingAddress" class="text-gray-700">
              {{ orderData.shippingAddress }}
            </p>
            <p v-else class="text-gray-500 italic">订单处理中，稍后将显示收货信息</p>
          </div>
        </div>

        <!-- 订单摘要 -->
        <div class="space-y-2 mb-6 pb-6 border-b border-gray-100">
          <h3 class="text-lg font-semibold text-gray-800 mb-3">订单摘要</h3>
          <div class="flex justify-between text-gray-600">
            <span>商品总价</span>
            <span>${{ formatPrice(orderData.productPrice * orderData.productQuantity) }}</span>
          </div>
          <div class="flex justify-between text-gray-600">
            <span>运费</span>
            <span>$0.00</span>
          </div>
          <div class="flex justify-between text-gray-600">
            <span>服务费</span>
            <span
              >${{ formatPrice(orderData.productPrice * orderData.productQuantity * 0.02) }}</span
            >
          </div>
          <div class="flex justify-between font-medium text-lg pt-2 border-t border-gray-100">
            <span>总计</span>
            <span class="text-violet-600">
              ${{ formatPrice(orderData.productPrice * orderData.productQuantity * 1.02) }}
            </span>
          </div>
        </div>

        <!-- 付款信息 -->
        <div class="mb-6 pb-6 border-b border-gray-100">
          <h3 class="text-lg font-semibold text-gray-800 mb-3">付款信息</h3>
          <div class="bg-gray-50 p-4 rounded-lg">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p class="text-sm text-gray-500">支付方式</p>
                <p class="font-medium text-gray-800">{{ orderData.paymentMethod || '在线支付' }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">支付时间</p>
                <p class="font-medium text-gray-800">{{ formatDate(orderData.orderTime) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">交易流水号</p>
                <p class="font-medium text-gray-800">
                  {{ orderData.transactionId || orderData.orderId }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
          <button
            @click="goToOrderManagement"
            class="flex-1 bg-violet-600 text-white py-3 px-4 rounded-lg hover:bg-violet-700 transition-colors duration-300 flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
            查看所有订单
          </button>
          <button
            @click="continueShopping"
            class="flex-1 border border-violet-600 text-violet-600 py-3 px-4 rounded-lg hover:bg-violet-50 transition-colors duration-300 flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
              />
            </svg>
            继续购物
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 订单数据结构
interface OrderData {
  orderId: string
  orderTime: string
  productImg: string
  productName: string
  productAttributes: string
  productQuantity: number
  productPrice: number
  shippingAddress?: string
  paymentMethod?: string
  transactionId?: string
}

// 从localStorage获取临时订单数据
const orderData = ref<OrderData>({
  orderId: '',
  orderTime: '',
  productImg: '',
  productName: '',
  productAttributes: '',
  productQuantity: 0,
  productPrice: 0,
})

// 格式化价格
const formatPrice = (price: number): string => {
  return price.toFixed(2)
}

// 格式化日期
const formatDate = (dateString: string): string => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 处理图片加载错误
const handleImageError = (e: Event) => {
  const target = e.target as HTMLImageElement
  target.src = '/placeholder.svg'
}

// 跳转到订单管理页面
const goToOrderManagement = () => {
  // 直接跳转到订单页面，使用完整路径避免重定向问题
  router.push({
    path: '/dashboard/orders',
    query: { highlight: orderData.value.orderId },
    replace: true, // 使用replace模式，替换当前历史记录，避免返回按钮返回到此页面
  })
}

// 继续购物
const continueShopping = () => {
  router.push('/')
}

onMounted(() => {
  // 从localStorage获取临时订单数据
  const tempOrderDataStr = localStorage.getItem('tempOrderData')
  if (tempOrderDataStr) {
    orderData.value = JSON.parse(tempOrderDataStr)
  } else {
    // 如果没有临时订单数据，跳转到订单管理页面
    router.replace('/user/orders')
  }
})
</script>

<style scoped>
/* 添加一些动画效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(124, 58, 237, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(124, 58, 237, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(124, 58, 237, 0);
  }
}

.bg-violet-50 {
  animation: pulse 2s infinite;
}
</style>
