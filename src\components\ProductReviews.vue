<template>
  <div class="space-y-6">
    <!-- 评论统计 -->
    <div v-if="reviews.length" class="mb-6">
      <div class="flex items-center gap-4">
        <div class="text-2xl font-bold">
          {{ averageRating.toFixed(1) }}
        </div>
        <div>
          <el-rate
            v-model="averageRating"
            disabled
            show-score
            text-color="#ff9900"
          />
          <div class="text-gray-500 text-sm">
            {{ reviews.length }} 条评价
          </div>
        </div>
      </div>
    </div>

    <!-- 评论列表 -->
    <div v-if="!reviews.length" class="text-center py-8">
      <el-empty description="暂无评价" />
    </div>

    <div
      v-for="review in reviews"
      :key="review.id"
      class="border-b last:border-0 pb-6"
    >
      <div class="flex justify-between items-start mb-2">
        <div class="flex items-center gap-2">
          <span class="font-medium">{{ review.buyerName }}</span>
          <el-rate
            v-model="review.rating"
            disabled
            size="small"
          />
        </div>
        <span class="text-gray-500 text-sm">
          {{ formatDate(review.commentTime) }}
        </span>
      </div>

      <p class="text-gray-700 mb-2">{{ review.comment }}</p>

      <div class="flex items-center gap-4">
        <el-tag
          v-if="review.isRecommended"
          type="success"
          size="small"
          effect="light"
        >
          推荐
        </el-tag>
        <span class="text-gray-500 text-sm">
          来自：{{ review.storeName }}
        </span>
      </div>

      <!-- 删除按钮（仅对自己的评论显示） -->
      <div
        v-if="review.buyerId === currentUserId"
        class="flex justify-end mt-2"
      >
        <el-button
          type="danger"
          link
          size="small"
          @click="handleDeleteReview(review.id)"
        >
          删除
        </el-button>
      </div>
    </div>

    <!-- 添加评论 -->
    <div v-if="showAddReview" class="mt-8">
      <h3 class="text-lg font-medium mb-4">添加评论</h3>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="评分" prop="rating">
          <el-rate
            v-model="form.rating"
            :rules="[
              { required: true, message: '请选择评分' }
            ]"
          />
        </el-form-item>

        <el-form-item label="评论" prop="comment">
          <el-input
            v-model="form.comment"
            type="textarea"
            rows="4"
            placeholder="请输入评论内容"
          />
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="form.isRecommended">
            推荐该商品
          </el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitReview">
            提交评论
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { reviewsApi } from '@/api/reviews'
import type { ReviewsVO, ReviewsDTO } from '@/api/reviews'
import { formatDate } from '@/utils/format'

const props = defineProps<{
  productId: number
  storeName: string
  productName: string
  showAddReview?: boolean
}>()

const reviews = ref<ReviewsVO[]>([])
const formRef = ref<FormInstance>()
const currentUserId = JSON.parse(localStorage.getItem('user') || '{}').id

const form = ref<ReviewsDTO>({
  buyerId: currentUserId,
  productId: props.productId,
  storeName: props.storeName,
  productName: props.productName,
  rating: 5,
  comment: '',
  isRecommended: true
})

const rules: FormRules = {
  rating: [
    { required: true, message: '请选择评分' }
  ],
  comment: [
    { required: true, message: '请输入评论内容' },
    { min: 5, max: 500, message: '评论长度在 5 到 500 个字符' }
  ]
}

const averageRating = computed(() => {
  if (!reviews.value.length) return 0
  const total = reviews.value.reduce((sum, review) => sum + review.rating, 0)
  return total / reviews.value.length
})

const getReviews = async () => {
  try {
    const response = await reviewsApi.getProductReviews(props.productId)
    if (response.code === 1) {
      reviews.value = response.data
    }
  } catch (error) {
    console.error('Get reviews error:', error)
    ElMessage.error('获取评论失败')
  }
}

const submitReview = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const response = await reviewsApi.addReview(form.value)
        if (response.code === 1) {
          ElMessage.success('评论成功')
          form.value.comment = ''
          form.value.rating = 5
          form.value.isRecommended = true
          await getReviews()
        }
      } catch (error) {
        console.error('Add review error:', error)
        ElMessage.error('评论失败')
      }
    }
  })
}

const handleDeleteReview = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这条评论吗？', '提示', {
      type: 'warning'
    })

    const response = await reviewsApi.deleteReview(id)
    if (response.code === 1) {
      ElMessage.success('删除成功')
      await getReviews()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete review error:', error)
      ElMessage.error('删除失败')
    }
  }
}

onMounted(() => {
  getReviews()
})
</script>
