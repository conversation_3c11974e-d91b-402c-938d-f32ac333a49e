<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 主要内容卡片 -->
    <div class="rounded-2xl p-8">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-2xl font-bold text-gray-800">个人中心</h1>
        <p class="text-gray-600 mt-2">管理您的个人信息</p>
      </div>

      <!-- 查看模式 -->
      <template v-if="!isEditing">
        <!-- 用户信息卡片 -->
        <div class="rounded-xl p-6 mb-8">
          <div class="flex flex-col md:flex-row items-center md:items-start gap-6">
            <!-- 头像部分 -->
            <div class="relative">
              <img
                :src="userInfo.photoUrl"
                alt="用户头像"
                class="w-24 h-24 rounded-full object-cover border-4 border-white/50 shadow-lg"
              />
              <div
                class="absolute -bottom-2 -right-2 bg-green-500 w-6 h-6 rounded-full border-4 border-white/50"
              ></div>
            </div>

            <!-- 用户基本信息 -->
            <div class="flex-1 text-center md:text-left">
              <h2 class="text-xl font-semibold text-gray-800">{{ userInfo.accountName }}</h2>
              <div class="mt-2">
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"
                >
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  已启用
                </span>
              </div>
              <button
                @click="startEditing"
                class="mt-4 px-6 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:opacity-90 transition-all duration-200 shadow-md hover:shadow-lg"
              >
                编辑资料
              </button>
            </div>
          </div>
        </div>

        <!-- 详细信息网格 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 性别 -->
          <div class="info-card">
            <div class="flex items-center gap-4">
              <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center">
                <span class="text-xl">👤</span>
              </div>
              <div>
                <div class="text-sm text-gray-500">性别</div>
                <div class="text-gray-800 font-medium mt-1">{{ userInfo.gender }}</div>
              </div>
            </div>
          </div>

          <!-- 手机号码 -->
          <div class="info-card">
            <div class="flex items-center gap-4">
              <div class="w-12 h-12 rounded-full bg-orange-50 flex items-center justify-center">
                <span class="text-xl">📱</span>
              </div>
              <div>
                <div class="text-sm text-gray-500">手机号码</div>
                <div class="text-gray-800 font-medium mt-1">{{ formatPhone(userInfo.phone) }}</div>
              </div>
            </div>
          </div>

          <!-- 邮箱 -->
          <div class="info-card">
            <div class="flex items-center gap-4">
              <div class="w-12 h-12 rounded-full bg-purple-50 flex items-center justify-center">
                <span class="text-xl">✉️</span>
              </div>
              <div>
                <div class="text-sm text-gray-500">邮箱</div>
                <div class="text-gray-800 font-medium mt-1">{{ formatEmail(userInfo.email) }}</div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 编辑模式 -->
      <template v-else>
        <div class="max-w-2xl mx-auto">
          <div
            class="backdrop-blur-lg bg-white/40 rounded-3xl shadow-2xl p-8 space-y-8 border border-white/20"
          >
            <!-- 表单标题 -->
            <div class="text-center space-y-4">
              <div class="w-20 h-20 mx-auto">
                <img src="@/assets/logo1.jpg" alt="Logo" class="w-full h-full object-contain" />
              </div>
              <h2
                class="text-2xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent"
              >
                修改个人信息
              </h2>
              <p class="text-gray-600">请填写需要修改的信息</p>
            </div>

            <el-form
              ref="formRef"
              :model="editForm"
              :rules="rules"
              label-position="top"
              class="space-y-6"
            >
              <!-- 用户名 -->
              <el-form-item label="用户名" prop="accountName" class="form-item-hover">
                <el-input
                  v-model="editForm.accountName"
                  placeholder="请输入用户名"
                  class="custom-input"
                />
              </el-form-item>

              <!-- 性别 -->
              <el-form-item label="性别" prop="gender" class="form-item-hover">
                <el-radio-group v-model="editForm.gender" class="custom-radio-group">
                  <el-radio label="男" class="custom-radio">男</el-radio>
                  <el-radio label="女" class="custom-radio">女</el-radio>
                </el-radio-group>
              </el-form-item>

              <!-- 手机号码 -->
              <el-form-item label="手机号码" prop="phone" class="form-item-hover">
                <el-input
                  v-model="editForm.phone"
                  placeholder="请输入手机号码"
                  class="custom-input"
                />
              </el-form-item>

              <!-- 邮箱 -->
              <el-form-item label="邮箱" prop="email" class="form-item-hover">
                <el-input
                  v-model="editForm.email"
                  placeholder="请输入邮箱，用于接收验证码"
                  class="custom-input"
                >
                  <template #append>
                    <button
                      type="button"
                      :disabled="!editForm.email || isCountingDown"
                      @click.prevent="handleSendCode"
                      class="verification-button-new group relative overflow-hidden flex items-center justify-center gap-2 min-w-[140px] h-[48px] px-4 text-white font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span class="relative z-10 flex items-center justify-center gap-2">
                        <span v-if="sendingCode" class="animate-spin">
                          <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none">
                            <circle
                              class="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              stroke-width="4"
                            ></circle>
                            <path
                              class="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                        </span>
                        <span v-else class="transform transition-transform group-hover:scale-110">
                          <svg
                            class="w-5 h-5"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="2"
                          >
                            <path
                              d="M21.75 9v.906a2.25 2.25 0 01-1.183 1.981l-6.478 3.488M2.25 9v.906a2.25 2.25 0 001.183 1.981l6.478 3.488m8.839 2.51l-4.66-2.51m0 0l-1.023-.55a2.25 2.25 0 00-2.134 0l-1.022.55m0 0l-4.661 2.51m16.5 1.615a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V8.844a2.25 2.25 0 011.183-1.98l7.5-4.04a2.25 2.25 0 012.134 0l7.5 4.04a2.25 2.25 0 011.183 1.98V19.5z"
                            />
                          </svg>
                        </span>
                        {{ countDownText }}
                      </span>
                      <div
                        class="absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 transition-transform duration-300 group-hover:scale-110"
                      ></div>
                    </button>
                  </template>
                </el-input>
                <div class="mt-1 text-sm text-gray-600 bg-white/60 rounded-lg p-2">
                  <el-icon class="align-middle mr-1"><InfoFilled /></el-icon>
                  请填写真实邮箱，验证码将通过邮箱发送
                </div>
              </el-form-item>

              <!-- 验证码 -->
              <el-form-item label="验证码" prop="verificationCode" class="form-item-hover">
                <el-input
                  v-model="editForm.verificationCode"
                  placeholder="请输入邮箱验证码"
                  class="custom-input"
                />
                <div
                  class="mt-1 text-sm bg-white/60 rounded-lg p-2"
                  :class="sendingCode ? 'text-purple-600' : 'text-gray-600'"
                >
                  <el-icon class="align-middle mr-1">
                    <component :is="sendingCode ? 'Loading' : 'Message'" />
                  </el-icon>
                  {{ sendingCode ? '正在发送验证码...' : '验证码将发送至您的邮箱' }}
                </div>
              </el-form-item>

              <!-- 按钮组 -->
              <div class="flex justify-end gap-4 mt-8">
                <button
                  @click="cancelEdit"
                  class="px-6 py-2 rounded-lg border border-gray-300 hover:border-gray-400 transition-all duration-200"
                >
                  取消
                </button>
                <button
                  @click="submitForm"
                  :disabled="submitting"
                  class="px-6 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg transition-all duration-200 hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
                >
                  <div class="flex items-center justify-center">
                    <i v-if="submitting" class="el-icon-loading mr-2"></i>
                    <span>{{ submitting ? '保存中...' : '保存修改' }}</span>
                  </div>
                </button>
              </div>
            </el-form>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { InfoFilled, Message } from '@element-plus/icons-vue'
import request from '@/utils/request'
import type { AxiosError } from 'axios'

// 定义用户信息类型
interface UserData {
  id: number
  accountName: string
  gender: string
  photoUrl: string
  phone: string
  email: string
  accountStatus: number | null
  createTime: string | null
  lastLoginTime: string | null
}

// 定义API响应类型
interface ApiResponse<T> {
  code: number
  msg: string | null
  data: T
}

defineOptions({
  name: 'UserProfile',
})

// 用户数据
const userInfo = reactive({
  accountName: '',
  gender: '',
  photoUrl: '',
  phone: '',
  email: '',
  accountStatus: null,
  createTime: null,
  lastLoginTime: null,
})

// 获取用户信息
const getUserInfo = async () => {
  try {
    // 从localStorage获取用户信息
    const userStr = localStorage.getItem('user')
    if (!userStr) {
      ElMessage.error('未找到用户信息，请重新登录')
      return
    }

    const userData = JSON.parse(userStr)
    const userId = userData.id

    console.log('获取用户信息，用户ID:', userId)

    const response = await request<ApiResponse<UserData>>({
      method: 'get',
      url: `/buyer/${userId}`,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
    })

    console.log('获取到的用户信息:', response)

    if (response.data && response.data.code === 1 && response.data.data) {
      // 更新用户信息
      const userData = response.data.data
      Object.assign(userInfo, {
        id: userData.id,
        accountName: userData.accountName || '',
        gender: userData.gender || '男',
        photoUrl:
          userData.photoUrl ||
          'https://hiram.oss-cn-beijing.aliyuncs.com/%E9%BB%98%E8%AE%A4%E5%A4%B4%E5%83%8F/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250302163653.jpg',
        phone: userData.phone || '',
        email: userData.email || '',
        accountStatus: userData.accountStatus,
        createTime: userData.createTime,
        lastLoginTime: userData.lastLoginTime,
      })
      console.log('用户信息已更新:', userInfo)
    } else {
      ElMessage.error('获取用户信息失败')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败，请重试')
  }
}

// 在组件挂载时获取用户信息
onMounted(() => {
  getUserInfo()
})

// 编辑状态
const isEditing = ref(false)
const formRef = ref<FormInstance>()
const submitting = ref(false)
const sendingCode = ref(false)
const countdown = ref(60)
const isCountingDown = ref(false)
const needEmailVerification = ref(false)

// 编辑表单
const editForm = reactive({
  accountName: '',
  gender: '',
  phone: '',
  email: '',
  verificationCode: '',
})

// 表单验证规则
const rules: FormRules = {
  accountName: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' },
  ],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
  verificationCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码长度应为6位', trigger: 'blur' },
  ],
}

// 开始编辑
const startEditing = () => {
  // 填充表单数据
  editForm.accountName = userInfo.accountName || ''
  editForm.gender = userInfo.gender || '男'
  editForm.phone = userInfo.phone || ''
  editForm.email = userInfo.email || ''
  editForm.verificationCode = ''

  console.log('开始编辑，表单数据:', editForm)
  isEditing.value = true
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
  needEmailVerification.value = false
}

// 倒计时文本
const countDownText = computed(() => {
  return isCountingDown.value ? `${countdown.value}秒后重试` : '发送验证码'
})

// 开始倒计时
const startCountdown = () => {
  isCountingDown.value = true
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
      isCountingDown.value = false
    }
  }, 1000)
}

// 发送验证码
const handleSendCode = async () => {
  if (!editForm.email) {
    ElMessage.warning('请先填写邮箱')
    return
  }

  // 验证邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(editForm.email)) {
    ElMessage.warning('请输入正确的邮箱格式')
    return
  }

  sendingCode.value = true
  try {
    const response = await request({
      method: 'post',
      url: '/buyer/register/sendCode',
      data: {
        address: editForm.email,
      },
    })

    if (response.data.code === 1) {
      startCountdown()
      needEmailVerification.value = true
      ElMessage.success(response.data.msg || '验证码已发送')
    } else {
      ElMessage.error(response.data.msg || '发送验证码失败')
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    ElMessage.error('发送验证码失败，请重试')
  } finally {
    sendingCode.value = false
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        // 检查是否需要邮箱验证
        if (editForm.email !== userInfo.email) {
          if (!editForm.verificationCode) {
            ElMessage.warning('更改邮箱需要验证码')
            return
          }
        }

        // 构建请求数据
        const requestData = {
          accountName: editForm.accountName,
          gender: editForm.gender,
          phone: editForm.phone,
          email: editForm.email,
          verificationCode: editForm.verificationCode,
        }

        console.log('提交的表单数据:', requestData)

        // 提交更新请求
        const response = await request<ApiResponse<null>>({
          method: 'put',
          url: '/buyer/update',
          data: requestData,
        })

        console.log('更新响应:', response)

        if (response.data.code === 1) {
          // 更新成功，更新本地数据
          Object.assign(userInfo, {
            accountName: editForm.accountName,
            gender: editForm.gender,
            phone: editForm.phone,
            email: editForm.email,
          })
          ElMessage.success(response.data.msg || '更新成功')
          isEditing.value = false
          needEmailVerification.value = false

          // 重新获取用户信息以确保数据同步
          getUserInfo()
        } else {
          ElMessage.error(response.data.msg || '更新失败')
        }
      } catch (error) {
        console.error('更新失败:', error)
        // 安全地获取错误消息
        let errMsg = '更新失败，请重试'
        if (error && typeof error === 'object' && 'response' in error) {
          const response = error.response
          if (response && typeof response === 'object' && 'data' in response) {
            const data = response.data
            if (data && typeof data === 'object' && 'msg' in data) {
              errMsg = data.msg as string
            }
          }
        }
        ElMessage.error(errMsg)
      } finally {
        submitting.value = false
      }
    }
  })
}

// 格式化手机号码（脱敏处理）
const formatPhone = (phone: string) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 格式化邮箱（脱敏处理）
const formatEmail = (email: string) => {
  if (!email) return ''
  const [username, domain] = email.split('@')
  const maskedUsername =
    username.length > 3
      ? username.slice(0, 3) + '*'.repeat(username.length - 3)
      : username.slice(0, 1) + '*'.repeat(username.length - 1)
  return `${maskedUsername}@${domain}`
}
</script>

<style scoped>
.info-card {
  @apply rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300;
}

/* 输入框样式 */
.custom-input :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(4px);
  border-radius: 1rem;
  height: 48px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.custom-input :deep(.el-input__wrapper:hover) {
  border-color: rgba(147, 51, 234, 0.3);
  background: rgba(255, 255, 255, 0.8);
}

.custom-input :deep(.el-input__wrapper.is-focus) {
  border-color: rgb(147, 51, 234);
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 0 4px rgba(147, 51, 234, 0.1);
}

.custom-input :deep(.el-input__inner) {
  height: 48px;
  color: #1f2937;
}

.custom-input :deep(.el-input__inner::placeholder) {
  color: #9ca3af;
}

/* 单选按钮样式 */
.custom-radio-group :deep(.el-radio) {
  margin-right: 20px;
  background: rgba(255, 255, 255, 0.6);
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.custom-radio-group :deep(.el-radio:hover) {
  background: rgba(255, 255, 255, 0.8);
}

.custom-radio-group :deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: #9333ea;
  border-color: #9333ea;
}

.custom-radio-group :deep(.el-radio__input.is-checked + .el-radio__label) {
  color: #9333ea;
}

/* 表单项悬浮效果 */
.form-item-hover {
  transition: transform 0.3s ease;
}

.form-item-hover:hover {
  transform: translateY(-2px);
}

/* 新的验证码按钮样式 */
.verification-button-new {
  position: relative;
  min-width: 140px;
  height: 48px;
  padding: 0 20px;
  color: white;
  font-weight: 500;
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
  border-radius: 0.5rem;
}

.verification-button-new:disabled {
  background: rgba(229, 231, 235, 0.8) !important;
  color: #9ca3af;
  cursor: not-allowed;
}

.verification-button-new:not(:disabled) {
  transform: translateY(0);
  transition: all 0.3s ease;
}

.verification-button-new:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(147, 51, 234, 0.35);
}

.verification-button-new:not(:disabled):active {
  transform: translateY(0);
}

.verification-button-new.sending {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.verification-button-new::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 0.5rem;
  padding: 2px;
  background: linear-gradient(to right, #9333ea, #6366f1);
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.verification-button-new:not(:disabled):hover::after {
  opacity: 1;
}

/* 添加发送中的动画效果 */
@keyframes sending-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(147, 51, 234, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(147, 51, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(147, 51, 234, 0);
  }
}

.verification-button-new:not(:disabled).sending {
  animation: sending-pulse 1.5s infinite;
}

/* 图标动画 */
.verification-button-new svg {
  transition: all 0.3s ease;
}

.verification-button-new:hover svg {
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.5));
}

/* 错误消息样式 */
:deep(.el-form-item__error) {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 8px;
  border-radius: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .info-card {
    @apply p-4;
  }
}
</style>
