<template>
  <div class="min-h-screen flex items-center justify-center relative overflow-hidden">
    <!-- 主要内容 -->
    <div class="w-full max-w-md relative z-10 p-6">
      <!-- 玻璃态卡片 -->
      <div
        class="backdrop-blur-lg bg-white/40 rounded-3xl shadow-2xl p-8 space-y-8 border border-white/20"
      >
        <!-- Logo和标题 -->
        <div class="text-center space-y-4">
          <div class="w-20 h-20 mx-auto">
            <img src="@/assets/logo1.jpg" alt="Logo" class="w-full h-full object-contain" />
          </div>
          <div>
            <h2
              class="text-3xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent"
            >
              {{ $t('forgotPassword.title') }}
            </h2>
            <p class="mt-2 text-gray-600">{{ $t('forgotPassword.subtitle') }}</p>
          </div>
        </div>

        <!-- 找回表单 -->
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- 用户名 -->
          <div class="space-y-2">
            <label for="userName" class="block text-sm font-medium text-gray-700">{{
              $t('forgotPassword.usernameLabel')
            }}</label>
            <div class="relative">
              <input
                v-model="form.userName"
                id="userName"
                type="text"
                required
                class="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ease-in-out"
                :class="{ 'border-red-500 focus:ring-red-500': errors.userName }"
                :placeholder="$t('forgotPassword.usernamePlaceholder')"
              />
              <div v-if="errors.userName" class="absolute -bottom-6 left-0 text-sm text-red-500">
                {{ errors.userName }}
              </div>
            </div>
          </div>

          <!-- 邮箱 -->
          <div class="space-y-2">
            <label for="address" class="block text-sm font-medium text-gray-700">{{
              $t('forgotPassword.emailLabel')
            }}</label>
            <div class="relative">
              <input
                v-model="form.address"
                id="address"
                type="email"
                required
                class="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ease-in-out"
                :class="{ 'border-red-500 focus:ring-red-500': errors.address }"
                :placeholder="$t('forgotPassword.emailPlaceholder')"
              />
              <div v-if="errors.address" class="absolute -bottom-6 left-0 text-sm text-red-500">
                {{ errors.address }}
              </div>
            </div>
          </div>

          <!-- 验证码 -->
          <div class="space-y-2">
            <label for="verificationCode" class="block text-sm font-medium text-gray-700">
              {{ $t('forgotPassword.verificationCodeLabel') }}
            </label>
            <div class="flex gap-4">
              <div class="relative flex-1">
                <input
                  v-model="form.verificationCode"
                  id="verificationCode"
                  type="text"
                  required
                  class="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ease-in-out"
                  :class="{
                    'border-red-500 focus:ring-red-500': errors.verificationCode,
                  }"
                  :placeholder="$t('forgotPassword.verificationCodePlaceholder')"
                />
                <div
                  v-if="errors.verificationCode"
                  class="absolute -bottom-6 left-0 text-sm text-red-500"
                >
                  {{ errors.verificationCode }}
                </div>
              </div>
              <button
                type="button"
                :disabled="isCountingDown || !form.address"
                @click.prevent="handleSendCode"
                class="verification-button-new group relative overflow-hidden flex items-center justify-center gap-2 min-w-[140px] h-[48px] px-4 text-white font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span class="relative z-10 flex items-center justify-center gap-2">
                  <span v-if="sendingCode" class="animate-spin">
                    <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none">
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      ></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  </span>
                  <span v-else class="transform transition-transform group-hover:scale-110">
                    <svg
                      class="w-5 h-5"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M2.5 12C2.5 7.52166 6.13388 3.875 10.5938 3.875C13.8612 3.875 16.7012 5.80875 18 8.5M21.5 12C21.5 16.4783 17.8661 20.125 13.4062 20.125C10.1388 20.125 7.29875 18.1912 6 15.5"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M19 8.5H18C17.4477 8.5 17 8.05228 17 7.5V6.5M5 15.5H6C6.55228 15.5 7 15.9477 7 16.5V17.5"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  {{
                    isCountingDown
                      ? $t('forgotPassword.resendCode', { seconds: countdown })
                      : $t('forgotPassword.sendCode')
                  }}
                </span>
                <div
                  class="absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 opacity-100 group-hover:opacity-90 transition-opacity duration-300"
                ></div>
                <div
                  class="absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 opacity-0 group-hover:opacity-100 blur-lg transition-opacity duration-300"
                ></div>
              </button>
            </div>
            <div class="mt-1 text-sm text-gray-600 bg-white/60 rounded-lg p-2">
              <el-icon class="align-middle mr-1"><InfoFilled /></el-icon>
              {{ $t('forgotPassword.codeTip') }}
            </div>
          </div>

          <!-- 提交按钮 -->
          <button
            type="submit"
            :disabled="loading"
            class="w-full py-3 px-4 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <div class="flex items-center justify-center">
              <span v-if="loading" class="w-5 h-5 mr-2 animate-spin">
                <svg viewBox="0 0 24 24" fill="none" class="w-5 h-5">
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              </span>
              <span>{{
                loading ? $t('forgotPassword.verifying') : $t('forgotPassword.submitButton')
              }}</span>
            </div>
          </button>

          <!-- 返回登录 -->
          <div class="text-center mt-6">
            <p class="text-sm text-gray-600">
              {{ $t('forgotPassword.rememberAccount') }}
              <router-link
                to="/login"
                class="text-purple-600 hover:text-purple-700 font-medium transition-colors"
              >
                {{ $t('forgotPassword.backToLogin') }}
              </router-link>
            </p>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'
import type { AxiosError } from 'axios'
import { InfoFilled } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import type {
  AccountVerifyRequest,
  SendCodeRequest,
  SendCodeResponse,
  AccountVerifyResponse,
  ApiResponse,
} from '@/types/account'

const { t } = useI18n()
const router = useRouter()
const loading = ref(false)
const sendingCode = ref(false)
const isCountingDown = ref(false)
const countdown = ref(60)

// 表单数据
const form = reactive({
  address: '',
  userName: '',
  verificationCode: '',
  userId: undefined as number | undefined,
})

// 表单错误信息
const errors = reactive({
  address: '',
  userName: '',
  verificationCode: '',
})

// 倒计时文本
const countDownText = computed(() => {
  return isCountingDown.value ? `${countdown.value}秒后重试` : '发送验证码'
})

// 开始倒计时
const startCountdown = () => {
  isCountingDown.value = true
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
      isCountingDown.value = false
    }
  }, 1000)
}

// 发送验证码
const handleSendCode = async () => {
  // 验证输入
  if (!form.address) {
    errors.address = '请输入邮箱地址'
    return
  }

  // 验证邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(form.address)) {
    errors.address = '请输入正确的邮箱格式'
    return
  }

  sendingCode.value = true
  try {
    // 构建请求数据
    const requestData = {
      address: form.address,
    }

    console.log('发送验证码请求数据:', requestData) // 添加日志

    const response = await request<ApiResponse<null>>({
      method: 'post',
      url: '/buyer/register/sendCode',
      data: requestData,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (response.data.code === 1) {
      startCountdown()
      ElMessage.success(response.data.msg || '验证码已发送')
    } else {
      ElMessage.error(response.data.msg || '发送验证码失败')
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    ElMessage.error('发送验证码失败，请重试')
  } finally {
    sendingCode.value = false
  }
}

// 验证表单
const validateForm = () => {
  let isValid = true
  errors.address = ''
  errors.userName = ''
  errors.verificationCode = ''

  if (!form.userName) {
    errors.userName = '请输入用户名'
    isValid = false
  }

  if (!form.address) {
    errors.address = '请输入邮箱地址'
    isValid = false
  } else {
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(form.address)) {
      errors.address = '请输入正确的邮箱格式'
      isValid = false
    }
  }

  if (!form.verificationCode) {
    errors.verificationCode = '请输入验证码'
    isValid = false
  } else if (form.verificationCode.length !== 6) {
    errors.verificationCode = '验证码长度应为6位'
    isValid = false
  }

  return isValid
}

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true
  try {
    // 构建请求数据，确保所有字段都有值
    const requestData = {
      userName: form.userName,
      address: form.address,
      verificationCode: form.verificationCode,
      userId: form.userId,
    }

    console.log('发送的请求数据:', requestData) // 添加日志

    const response = await request<ApiResponse<null>>({
      method: 'post',
      url: '/buyer/forgot',
      data: requestData,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (response.data.code === 1) {
      // 验证成功，跳转到修改密码页面
      ElMessage.success('身份验证成功，请重置密码')

      // 将用户名、邮箱和用户ID传递给重置密码页面
      router.push({
        path: '/reset-password',
        query: {
          userName: form.userName,
          address: form.address,
          userId: form.userId !== undefined ? String(form.userId) : undefined,
        },
      })
    } else {
      // 验证失败
      ElMessage.error(response.data.msg || '身份验证失败，请检查输入信息')
    }
  } catch (error) {
    console.error('身份验证失败:', error)

    // 安全地获取错误消息
    let errMsg = '身份验证失败，请重试'
    if (error && typeof error === 'object' && 'response' in error) {
      const response = error.response
      if (response && typeof response === 'object' && 'data' in response) {
        const data = response.data
        if (data && typeof data === 'object' && 'msg' in data) {
          errMsg = data.msg as string
        }
      }
    }

    ElMessage.error(errMsg)
  } finally {
    loading.value = false
  }
}

// 尝试获取当前用户ID
onMounted(() => {
  // 从localStorage或其他存储中获取用户ID
  const userInfo = localStorage.getItem('userInfo')
  if (userInfo) {
    try {
      const parsedUserInfo = JSON.parse(userInfo)
      if (parsedUserInfo && parsedUserInfo.id) {
        form.userId = parsedUserInfo.id
        console.log('已获取当前用户ID:', form.userId)
      }
    } catch (error) {
      console.error('解析用户信息失败:', error)
    }
  }
})
</script>

<style scoped>
/* 验证码按钮样式 */
.verification-button-new {
  position: relative;
  border: none;
  border-radius: 0.5rem;
  overflow: hidden;
  transform: translateY(0);
  will-change: transform;
}

.verification-button-new:not(:disabled) {
  box-shadow:
    0 4px 6px -1px rgba(147, 51, 234, 0.1),
    0 2px 4px -1px rgba(147, 51, 234, 0.06);
}

.verification-button-new:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow:
    0 10px 15px -3px rgba(147, 51, 234, 0.2),
    0 4px 6px -2px rgba(147, 51, 234, 0.1);
}

.verification-button-new:not(:disabled):active {
  transform: translateY(0);
}

.verification-button-new:disabled {
  background: rgba(229, 231, 235, 0.8);
  color: #9ca3af;
}

.verification-button-new.sending {
  animation: sending-pulse 2s infinite;
}

@keyframes sending-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(147, 51, 234, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(147, 51, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(147, 51, 234, 0);
  }
}

/* 添加发光效果 */
.verification-button-new::after {
  content: '';
  position: absolute;
  inset: -1px;
  background: linear-gradient(to right, #9333ea, #6366f1);
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  padding: 2px;
  border-radius: 0.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.verification-button-new:not(:disabled):hover::after {
  opacity: 1;
}
</style>
