/**
 * 账号找回和密码重置相关的实体类
 */

/**
 * 账号验证请求实体类
 * 用于验证用户身份
 */
export interface AccountVerifyRequest {
  /** 用户名 */
  userName: string
  /** 邮箱地址 */
  address: string
  /** 验证码 */
  verificationCode: string
  /** 用户ID（可选） */
  userId?: number
}

/**
 * 验证码发送请求实体类
 * 用于发送验证码
 */
export interface SendCodeRequest {
  /** 邮箱地址 */
  address: string
}

/**
 * 验证码发送响应实体类
 */
export interface SendCodeResponse {
  /** 响应代码 */
  code: number
  /** 响应消息 */
  msg: string | null
  /** 响应数据 */
  data: null
}

/**
 * 账号验证响应实体类
 */
export interface AccountVerifyResponse {
  /** 响应代码 */
  code: number
  /** 响应消息 */
  msg: string | null
  /** 响应数据 */
  data: {
    /** 验证令牌 */
    token: string
  }
}

/**
 * 密码重置请求实体类
 */
export interface ResetPasswordRequest {
  /** 用户名 */
  userName: string
  /** 邮箱地址 */
  address: string
  /** 验证令牌（可选） */
  token?: string
  /** 新密码 */
  newPassword: string
  /** 用户ID（可选） */
  userId?: number
}

/**
 * 密码重置响应实体类
 */
export interface ResetPasswordResponse {
  /** 响应代码 */
  code: number
  /** 响应消息 */
  msg: string | null
  /** 响应数据 */
  data: null
}

/**
 * 通用API响应实体类
 */
export interface ApiResponse<T> {
  /** 响应代码 */
  code: number
  /** 响应消息 */
  msg: string | null
  /** 响应数据 */
  data: T
}
