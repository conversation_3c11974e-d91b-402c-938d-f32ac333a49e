import request from '@/utils/request'
import type { ApiResponse, PageResult } from './types'
import type { AxiosResponse } from 'axios'

// 订单状态常量 - 与后端保持一致
export enum OrderStatus {
  PENDING_PAYMENT = 1, // 待付款
  PAID = 2, // 已付款
  PROCESSING = 3, // 处理中
  SHIPPED = 4, // 已发货
  COMPLETED = 5, // 已完成
  CANCELLED = 6, // 已取消
  REFUNDED = 7, // 已退款
}

// 配送方式常量
export enum ShippingMethod {
  SCHEDULED = 0, // 选择具体时间
  IMMEDIATE = 1, // 立即送出
}

// 支付方式常量 - 与后端保持一致
export enum PayMethod {
  WECHAT = 1, // 微信支付
  ALIPAY = 2, // 支付宝支付
  CREDIT_CARD = 3, // 信用卡支付
  CASH = 4, // 货到付款
}

// 订单项DTO - 符合后端接口文档
export interface OrderItemDTO {
  productId: number
  quantity: number
  unitPrice: number
}

// 订单提交DTO - 符合后端接口文档
export interface OrderSubmitDTO {
  addressId: number
  payMethod: number
  shippingMethodId: number
  orderRemark?: string
  orderItems: OrderItemDTO[]
  amount: number
  paymentTransactionId: string
  payTimeStamp: number
}

// 订单详情
export interface OrderDetail {
  id: number
  orderId: number
  productId: number
  productName: string
  productImage: string
  productSpec?: string
  quantity: number
  unitPrice: number
  discount: number
  subtotal: number
  createTime: string
  updateTime: string
}

// 地址视图对象
export interface AddressVO {
  id: number
  userId: number
  consignee: string
  phone: string
  sex: string
  provinceCode: string
  provinceName: string
  cityCode: string
  cityName: string
  districtCode: string
  districtName: string
  detail: string
  label: string
  isDefault: number
}

// 物流视图对象
export interface LogisticsVO {
  id: number
  logisticsNumber: string
  orderId: number
  logisticsCompany: string
  logisticsStatus: string
  logisticsStatusDesc: string
  shippingDate: string
  createTime: string
}

// 订单视图对象
export interface OrderVO {
  id: number
  number: string
  status: number
  statusDesc: string
  amount: number
  orderTime: string
  payTime: string
  payMethod: number
  payMethodDesc: string
  address: AddressVO
  orderRemark: string
  orderDetails: OrderDetail[]
  logistics: LogisticsVO
}

export const orderApi = {
  // 提交订单（已支付）- 符合后端接口文档
  submitOrder(data: OrderSubmitDTO) {
    return request.post<ApiResponse<OrderVO>>('/orders/submit', data)
  },

  // 根据ID查询订单
  getOrderById(id: number) {
    return request.get<ApiResponse<OrderVO>>(`/orders/${id}`)
  },

  // 根据订单号查询订单
  getOrderByNumber(number: string) {
    return request.get<ApiResponse<OrderVO>>(`/orders/number/${number}`)
  },

  // 取消订单
  cancelOrder(id: number, cancelReason?: string) {
    const params = cancelReason ? { cancelReason } : {}
    return request.put<ApiResponse<boolean>>(`/orders/${id}/cancel`, null, { params })
  },

  // 确认收货
  confirmOrder(id: number) {
    return request.put<ApiResponse<boolean>>(`/orders/${id}/confirm`)
  },

  // 查询当前用户的订单
  getUserOrders(status?: number) {
    const params = status ? { status } : {}
    return request.get<ApiResponse<OrderVO[]>>('/orders/user', { params })
  },

  // 分页查询订单
  getOrdersByPage(
    page: number = 1,
    pageSize: number = 10,
    userId?: number,
    status?: number,
    keyword?: string,
    startTime?: string,
    endTime?: string,
  ) {
    const params: Record<string, number | string | undefined> = { page, pageSize }

    // 添加可选参数
    if (userId !== undefined) params.userId = userId
    if (status !== undefined) params.status = status
    if (keyword) params.keyword = keyword
    if (startTime) params.startTime = startTime
    if (endTime) params.endTime = endTime

    return request.get<ApiResponse<PageResult<OrderVO>>>('/orders/page', { params })
  },
}

export const orderDetailApi = {
  // 根据订单ID查询订单详情
  getOrderDetails(orderId: number) {
    return request.get<ApiResponse<OrderDetail[]>>(`/orderDetail/order/${orderId}`)
  },
}

export const logisticsApi = {
  // 根据订单ID查询物流信息
  getLogisticsByOrderId(orderId: number) {
    return request.get<ApiResponse<LogisticsVO>>(`/logistics/order/${orderId}`)
  },

  // 更新物流状态
  updateLogisticsStatus(id: number, status: string) {
    return request.put<ApiResponse<boolean>>(`/logistics/${id}/status`, null, {
      params: { status },
    })
  },
}

// 用户物流跟踪API - 符合文档要求
export const userTrackingApi = {
  // 查询物流详情 - 根据物流单号查询跟踪详情
  getTrackingDetail(trackingNumber: string, carrierCode?: number) {
    return request.get<ApiResponse<TrackingDetailVO>>('/user/tracking/detail', {
      params: { trackingNumber, carrierCode },
    })
  },

  // 订单物流信息 - 根据订单ID查询物流信息
  getOrderTracking(orderId: number) {
    return request.get<ApiResponse<TrackingDetailVO[]>>(`/user/tracking/order/${orderId}`)
  },

  // 获取运输商列表 - 获取常用运输商列表
  getCarriers() {
    return request.get<ApiResponse<CarrierVO[]>>('/user/tracking/carriers')
  },
}

// 退款相关接口和类型定义
export interface RefundApplicationDTO {
  orderId: number // 订单ID，必填
  refundAmount: number // 申请退款金额，必填
  refundReason: string // 退款理由，必填
  refundType: number // 退款类型：1-仅退款，2-退货退款，必填
  refundMethod?: number // 退款方式：1-原路退回，2-余额退款，可选
  remark?: string // 备注信息，可选
}

export interface RefundApplicationVO {
  id: number // 退款申请ID
  refundNo: string // 退款申请单号
  orderId: number // 订单ID
  orderNumber: string // 订单号
  orderAmount: number // 订单金额
  buyerId: number // 买家ID
  buyerName: string // 申请人姓名
  buyerPhone?: string // 申请人手机号
  refundAmount: number // 申请退款金额
  refundReason: string // 退款理由
  refundType: number // 退款类型：1-仅退款，2-退货退款
  refundTypeDesc: string // 退款类型描述
  applicationStatus: number // 申请状态：1-待处理，2-已同意，3-已拒绝，4-已取消，5-退款中，6-退款成功，7-退款失败
  applicationStatusDesc: string // 申请状态描述
  needApproval: number // 是否需要审核：0-不需要，1-需要
  approvalStatus?: number // 审核状态：1-待审核，2-审核通过，3-审核拒绝
  approvalStatusDesc?: string // 审核状态描述
  approverId?: number // 审核人ID
  approverName?: string // 审核人姓名
  approvalTime?: string // 审核时间
  approvalRemark?: string // 审核备注
  refundMethod?: number // 退款方式：1-原路退回，2-余额退款
  refundMethodDesc?: string // 退款方式描述
  actualRefundAmount?: number // 实际退款金额
  refundTime?: string // 退款完成时间
  createTime: string // 创建时间
  updateTime: string // 更新时间
  orderInfo?: OrderVO // 订单信息
  wechatRefundRecord?: any // 微信退款记录
}

export interface RefundQueryDTO {
  refundNo?: string // 退款申请单号
  orderId?: number // 订单ID
  orderNumber?: string // 订单号
  buyerId?: number // 申请人ID
  applicationStatus?: number // 申请状态：1-待处理，2-已同意，3-已拒绝，4-已取消，5-退款中，6-退款成功，7-退款失败
  refundType?: number // 退款类型：1-仅退款，2-退货退款
  needApproval?: number // 是否需要审核：0-不需要，1-需要
  approvalStatus?: number // 审核状态：1-待审核，2-审核通过，3-审核拒绝
  beginTime?: string // 开始时间
  endTime?: string // 结束时间
  page?: number // 页码，默认1
  pageSize?: number // 每页大小，默认10
}

// 退款检查结果接口
export interface RefundCheckResult {
  canRefund: boolean // 是否可以退款
  reason: string // 不能退款的原因
  maxRefundAmount: number // 最大退款金额
  refundType?: number // 推荐退款类型
}

// 退款状态枚举
export enum RefundStatus {
  PENDING = 1, // 待处理
  APPROVED = 2, // 已同意
  REJECTED = 3, // 已拒绝
  CANCELLED = 4, // 已取消
  REFUNDING = 5, // 退款中
  SUCCESS = 6, // 退款成功
  FAILED = 7, // 退款失败
}

// 退款类型枚举
export enum RefundType {
  REFUND_ONLY = 1, // 仅退款
  RETURN_AND_REFUND = 2, // 退货退款
}

// 退款方式枚举
export enum RefundMethod {
  ORIGINAL_PAYMENT = 1, // 原路退回
  BALANCE = 2, // 余额退款
}

// 物流相关类型定义
export interface TrackingDetailVO {
  id: number // 主键ID
  trackingNumber: string // 物流单号
  carrierCode: number // 运输商代码
  carrierName: string // 运输商名称
  orderId: number // 关联订单ID
  orderNumber: string // 订单号
  originCountry: string // 发货国家代码
  destinationCountry: string // 目的地国家代码
  status: string // 物流主状态
  statusDesc: string // 状态描述
  subStatus: string // 物流子状态
  subStatusDesc: string // 子状态描述
  trackingStatus: string // 跟踪状态
  registerTime: string // 注册时间
  trackTime: string // 最后跟踪时间
  latestEventTime: string // 最新事件时间
  latestEventInfo: string // 最新事件信息
  pickupTime?: string // 揽收时间
  deliveryTime?: string // 签收时间
  events: TrackingEventVO[] // 物流事件列表
  createTime: string // 创建时间
  updateTime: string // 更新时间
}

export interface TrackingEventVO {
  time: string // 事件时间
  status: string // 事件状态
  location: string // 事件地点
  description: string // 事件描述
}

export interface CarrierVO {
  code: number // 运输商代码
  name: string // 运输商名称
  website?: string // 官方网站
  phone?: string // 客服电话
}

export interface TrackingQueryDTO {
  page?: number // 页码
  pageSize?: number // 每页大小
  trackingNumber?: string // 物流单号
  carrierCode?: number // 运输商代码
  orderId?: number // 订单ID
  status?: string // 物流状态
  trackingStatus?: string // 跟踪状态
  beginTime?: string // 开始时间
  endTime?: string // 结束时间
}

// 退款API接口
export const refundApi = {
  // 申请退款
  applyRefund(data: RefundApplicationDTO) {
    return request.post<ApiResponse<RefundApplicationVO>>('/user/refund/apply', data)
  },

  // 取消退款申请 (PUT方式)
  cancelRefundApplication(refundApplicationId: number) {
    return request.put<ApiResponse<string>>(`/user/refund/cancel/${refundApplicationId}`)
  },

  // 取消退款申请 (POST方式)
  cancelRefund(refundApplicationId: number) {
    return request.post<ApiResponse<boolean>>(`/user/refund/cancel/${refundApplicationId}`)
  },

  // 查询退款申请详情
  getRefundApplication(refundApplicationId: number) {
    return request.get<ApiResponse<RefundApplicationVO>>(`/user/refund/${refundApplicationId}`)
  },

  // 根据退款申请单号查询详情
  getRefundApplicationByNo(refundNo: string) {
    return request.get<ApiResponse<RefundApplicationVO>>(`/user/refund/no/${refundNo}`)
  },

  // 查询我的退款申请列表 - 使用/my接口避免路径冲突
  getMyRefundApplications(page: number = 1, pageSize: number = 10) {
    return request.get<ApiResponse<PageResult<RefundApplicationVO>>>('/user/refund/my', {
      params: { page, pageSize },
    })
  },

  // 分页查询退款申请
  pageQuery(queryDTO: RefundQueryDTO) {
    return request.get<ApiResponse<PageResult<RefundApplicationVO>>>('/user/refund/page', {
      params: queryDTO,
    })
  },

  // 查询退款状态
  queryRefundStatus(refundNo: string) {
    return request.get<ApiResponse<RefundApplicationVO>>(`/user/refund/status/${refundNo}`)
  },

  // 检查订单退款条件
  checkRefundEligibility(orderId: number) {
    return request.get<ApiResponse<RefundCheckResult>>(`/user/refund/check/${orderId}`)
  },
}
