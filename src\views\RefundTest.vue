<template>
  <div class="refund-test-page">
    <div class="container">
      <div class="header">
        <h1>退款功能测试页面</h1>
        <p>用于测试退款功能与后端接口的对接情况</p>
      </div>

      <div class="test-sections">
        <!-- API接口测试 -->
        <div class="test-section">
          <h2>API接口测试</h2>
          <div class="test-grid">
            <div class="test-item">
              <h3>检查退款条件</h3>
              <div class="test-form">
                <input
                  v-model="testOrderId"
                  type="number"
                  placeholder="输入订单ID"
                  class="test-input"
                />
                <button @click="testCheckRefundEligibility" class="test-btn">检查退款条件</button>
              </div>
              <div v-if="checkResult" class="test-result">
                <h4>检查结果：</h4>
                <pre>{{ JSON.stringify(checkResult, null, 2) }}</pre>
              </div>
            </div>

            <div class="test-item">
              <h3>申请退款</h3>
              <div class="test-form">
                <input
                  v-model="refundForm.orderId"
                  type="number"
                  placeholder="订单ID"
                  class="test-input"
                />
                <input
                  v-model="refundForm.refundAmount"
                  type="number"
                  step="0.01"
                  placeholder="退款金额"
                  class="test-input"
                />
                <input
                  v-model="refundForm.refundReason"
                  placeholder="退款原因"
                  class="test-input"
                />
                <select v-model="refundForm.refundType" class="test-input">
                  <option value="1">仅退款</option>
                  <option value="2">退货退款</option>
                </select>
                <select v-model="refundForm.refundMethod" class="test-input">
                  <option value="1">原路退回</option>
                  <option value="2">余额退款</option>
                </select>
                <button @click="testApplyRefund" class="test-btn">申请退款</button>
              </div>
              <div v-if="applyResult" class="test-result">
                <h4>申请结果：</h4>
                <pre>{{ JSON.stringify(applyResult, null, 2) }}</pre>
              </div>
            </div>

            <div class="test-item">
              <h3>查询我的退款申请</h3>
              <div class="test-form">
                <input v-model="queryPage" type="number" placeholder="页码" class="test-input" />
                <input
                  v-model="queryPageSize"
                  type="number"
                  placeholder="每页大小"
                  class="test-input"
                />
                <button @click="testGetMyRefunds" class="test-btn">查询我的退款</button>
              </div>
              <div v-if="myRefunds" class="test-result">
                <h4>查询结果：</h4>
                <pre>{{ JSON.stringify(myRefunds, null, 2) }}</pre>
              </div>
            </div>

            <div class="test-item">
              <h3>根据退款单号查询</h3>
              <div class="test-form">
                <input v-model="testRefundNo" placeholder="退款单号" class="test-input" />
                <button @click="testGetRefundByNo" class="test-btn">查询退款详情</button>
              </div>
              <div v-if="refundDetail" class="test-result">
                <h4>查询结果：</h4>
                <pre>{{ JSON.stringify(refundDetail, null, 2) }}</pre>
              </div>
            </div>

            <div class="test-item">
              <h3>物流查询测试</h3>
              <div class="test-form">
                <input v-model="testTrackingNumber" placeholder="物流单号" class="test-input" />
                <input
                  v-model="testTrackingOrderId"
                  type="number"
                  placeholder="订单ID"
                  class="test-input"
                />
                <button @click="testTrackingDetail" class="test-btn">查询物流详情</button>
                <button @click="testOrderTracking" class="test-btn">查询订单物流</button>
              </div>
              <div v-if="trackingResult" class="test-result">
                <h4>物流查询结果：</h4>
                <pre>{{ JSON.stringify(trackingResult, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据结构测试 -->
        <div class="test-section">
          <h2>数据结构测试</h2>
          <div class="test-grid">
            <div class="test-item">
              <h3>退款状态枚举</h3>
              <div class="enum-display">
                <div>PENDING (待处理): {{ RefundStatus.PENDING }}</div>
                <div>APPROVED (已同意): {{ RefundStatus.APPROVED }}</div>
                <div>REJECTED (已拒绝): {{ RefundStatus.REJECTED }}</div>
                <div>CANCELLED (已取消): {{ RefundStatus.CANCELLED }}</div>
                <div>REFUNDING (退款中): {{ RefundStatus.REFUNDING }}</div>
                <div>SUCCESS (退款成功): {{ RefundStatus.SUCCESS }}</div>
                <div>FAILED (退款失败): {{ RefundStatus.FAILED }}</div>
              </div>
            </div>

            <div class="test-item">
              <h3>退款类型枚举</h3>
              <div class="enum-display">
                <div>REFUND_ONLY (仅退款): {{ RefundType.REFUND_ONLY }}</div>
                <div>RETURN_AND_REFUND (退货退款): {{ RefundType.RETURN_AND_REFUND }}</div>
              </div>
            </div>

            <div class="test-item">
              <h3>退款方式枚举</h3>
              <div class="enum-display">
                <div>ORIGINAL_PAYMENT (原路退回): {{ RefundMethod.ORIGINAL_PAYMENT }}</div>
                <div>BALANCE (余额退款): {{ RefundMethod.BALANCE }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 测试日志 -->
        <div class="test-section">
          <h2>测试日志</h2>
          <div class="test-logs">
            <div v-for="(log, index) in testLogs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span :class="['log-level', `log-${log.level}`]">{{ log.level.toUpperCase() }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
          <button @click="clearLogs" class="test-btn">清空日志</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  refundApi,
  RefundStatus,
  RefundType,
  RefundMethod,
  userTrackingApi,
  type RefundApplicationDTO,
  type RefundCheckResult,
  type TrackingDetailVO,
} from '@/api/order'

defineOptions({
  name: 'RefundTest',
})

// 测试数据
const testOrderId = ref(1)
const testRefundNo = ref('')
const queryPage = ref(1)
const queryPageSize = ref(10)
const testTrackingNumber = ref('')
const testTrackingOrderId = ref<number | ''>('')

// 退款申请表单
const refundForm = reactive({
  orderId: 1,
  refundAmount: 99.99,
  refundReason: '测试退款原因',
  refundType: 1,
  refundMethod: 1,
})

// 测试结果
const checkResult = ref<RefundCheckResult | null>(null)
const applyResult = ref<any>(null)
const myRefunds = ref<any>(null)
const refundDetail = ref<any>(null)
const trackingResult = ref<TrackingDetailVO | TrackingDetailVO[] | null>(null)

// 测试日志
const testLogs = ref<Array<{ time: string; level: string; message: string }>>([])

// 添加日志
const addLog = (level: string, message: string) => {
  testLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    level,
    message,
  })
}

// 清空日志
const clearLogs = () => {
  testLogs.value = []
}

// 测试检查退款条件
const testCheckRefundEligibility = async () => {
  try {
    addLog('info', `开始检查订单 ${testOrderId.value} 的退款条件`)
    const response = await refundApi.checkRefundEligibility(testOrderId.value)

    if (response.data.code === 1) {
      checkResult.value = response.data.data
      addLog('success', '检查退款条件成功')
      ElMessage.success('检查退款条件成功')
    } else {
      addLog('error', `检查退款条件失败: ${response.data.msg}`)
      ElMessage.error(response.data.msg || '检查退款条件失败')
    }
  } catch (error) {
    addLog('error', `检查退款条件异常: ${error}`)
    ElMessage.error('检查退款条件异常')
    console.error('检查退款条件失败:', error)
  }
}

// 测试申请退款
const testApplyRefund = async () => {
  try {
    addLog('info', `开始申请退款，订单ID: ${refundForm.orderId}`)
    const refundData: RefundApplicationDTO = {
      orderId: refundForm.orderId,
      refundAmount: refundForm.refundAmount,
      refundReason: refundForm.refundReason,
      refundType: refundForm.refundType,
      refundMethod: refundForm.refundMethod,
    }

    const response = await refundApi.applyRefund(refundData)

    if (response.data.code === 1) {
      applyResult.value = response.data.data
      addLog('success', '申请退款成功')
      ElMessage.success('申请退款成功')
    } else {
      addLog('error', `申请退款失败: ${response.data.msg}`)
      ElMessage.error(response.data.msg || '申请退款失败')
    }
  } catch (error) {
    addLog('error', `申请退款异常: ${error}`)
    ElMessage.error('申请退款异常')
    console.error('申请退款失败:', error)
  }
}

// 测试查询我的退款申请
const testGetMyRefunds = async () => {
  try {
    addLog('info', `开始查询我的退款申请，页码: ${queryPage.value}`)
    const response = await refundApi.getMyRefundApplications(queryPage.value, queryPageSize.value)

    if (response.data.code === 1) {
      myRefunds.value = response.data.data
      addLog('success', '查询我的退款申请成功')
      ElMessage.success('查询我的退款申请成功')
    } else {
      addLog('error', `查询我的退款申请失败: ${response.data.msg}`)
      ElMessage.error(response.data.msg || '查询我的退款申请失败')
    }
  } catch (error) {
    addLog('error', `查询我的退款申请异常: ${error}`)
    ElMessage.error('查询我的退款申请异常')
    console.error('查询我的退款申请失败:', error)
  }
}

// 测试根据退款单号查询
const testGetRefundByNo = async () => {
  try {
    addLog('info', `开始根据退款单号查询: ${testRefundNo.value}`)
    const response = await refundApi.getRefundApplicationByNo(testRefundNo.value)

    if (response.data.code === 1) {
      refundDetail.value = response.data.data
      addLog('success', '根据退款单号查询成功')
      ElMessage.success('根据退款单号查询成功')
    } else {
      addLog('error', `根据退款单号查询失败: ${response.data.msg}`)
      ElMessage.error(response.data.msg || '根据退款单号查询失败')
    }
  } catch (error) {
    addLog('error', `根据退款单号查询异常: ${error}`)
    ElMessage.error('根据退款单号查询异常')
    console.error('根据退款单号查询失败:', error)
  }
}

// 测试物流详情查询
const testTrackingDetail = async () => {
  try {
    addLog('info', `开始查询物流详情: ${testTrackingNumber.value}`)
    const response = await userTrackingApi.getTrackingDetail(testTrackingNumber.value)

    if (response.data.code === 1) {
      trackingResult.value = response.data.data
      addLog('success', '查询物流详情成功')
      ElMessage.success('查询物流详情成功')
    } else {
      addLog('error', `查询物流详情失败: ${response.data.msg}`)
      ElMessage.error(response.data.msg || '查询物流详情失败')
    }
  } catch (error) {
    addLog('error', `查询物流详情异常: ${error}`)
    ElMessage.error('查询物流详情异常')
    console.error('查询物流详情失败:', error)
  }
}

// 测试订单物流查询
const testOrderTracking = async () => {
  try {
    addLog('info', `开始查询订单物流: ${testTrackingOrderId.value}`)
    const response = await userTrackingApi.getOrderTracking(Number(testTrackingOrderId.value))

    if (response.data.code === 1) {
      trackingResult.value = response.data.data
      addLog('success', '查询订单物流成功')
      ElMessage.success('查询订单物流成功')
    } else {
      addLog('error', `查询订单物流失败: ${response.data.msg}`)
      ElMessage.error(response.data.msg || '查询订单物流失败')
    }
  } catch (error) {
    addLog('error', `查询订单物流异常: ${error}`)
    ElMessage.error('查询订单物流异常')
    console.error('查询订单物流失败:', error)
  }
}
</script>

<style scoped>
.refund-test-page {
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.header h1 {
  font-size: 32px;
  margin-bottom: 10px;
}

.header p {
  font-size: 18px;
  opacity: 0.9;
}

.test-sections {
  display: grid;
  gap: 30px;
}

.test-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.test-section h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 24px;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.test-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.test-item h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 18px;
}

.test-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.test-input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.test-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.test-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.test-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.test-result {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
  border-left: 4px solid #667eea;
}

.test-result h4 {
  color: #333;
  margin-bottom: 10px;
  font-size: 16px;
}

.test-result pre {
  background: #2d3748;
  color: #e2e8f0;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.5;
}

.enum-display {
  display: grid;
  gap: 8px;
}

.enum-display div {
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  font-family: monospace;
  font-size: 14px;
}

.test-logs {
  max-height: 400px;
  overflow-y: auto;
  background: #2d3748;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
}

.log-item {
  display: flex;
  gap: 10px;
  margin-bottom: 8px;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-time {
  color: #a0aec0;
  min-width: 80px;
}

.log-level {
  min-width: 60px;
  font-weight: bold;
}

.log-info {
  color: #63b3ed;
}

.log-success {
  color: #68d391;
}

.log-error {
  color: #fc8181;
}

.log-message {
  color: #e2e8f0;
  flex: 1;
}

@media (max-width: 768px) {
  .test-grid {
    grid-template-columns: 1fr;
  }

  .container {
    padding: 0 10px;
  }

  .test-section {
    padding: 20px;
  }
}
</style>
