import request from '@/utils/request'

// 查询用户信息列表
export function listBuyers(query) {
  return request({
    url: '/system/buyers/list',
    method: 'get',
    params: query
  })
}

// 查询用户信息详细
export function getBuyers(id) {
  return request({
    url: '/buyer/' + id,
    method: 'get'
  })
}

// 新增用户信息
export function addBuyers(data) {
  return request({
    url: '/system/buyers',
    method: 'post',
    data: data
  })
}

// 修改用户信息
export function updateBuyers(data) {
  return request({
    url: '/system/buyers',
    method: 'put',
    data: data
  })
}

// 删除用户信息
export function delBuyers(id) {
  return request({
    url: '/system/buyers/' + id,
    method: 'delete'
  })
}
