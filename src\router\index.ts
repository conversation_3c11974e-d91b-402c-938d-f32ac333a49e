import { createRouter, createWebHistory } from 'vue-router'
import type { RouteLocationNormalized } from 'vue-router'
import HomePage from '@/components/HomePage.vue'
import { ElMessage } from 'element-plus'
import CreateStore from '@/views/CreateStore.vue'
import StoreManage from '@/views/StoreManage.vue'
import StoreSetup from '@/views/StoreSetup.vue'
import PrivacyPolicy from '@/views/PrivacyPolicy.vue'
import UpdateNotification from '@/views/UpdateNotification.vue'
import FeaturedProducts from '@/views/FeaturedProducts.vue'
import NewArrivals from '@/views/NewArrivals.vue'
import BestSellers from '@/views/BestSellers.vue'
import AllProducts from '@/views/AllProducts.vue'
import { i18n } from '@/i18n'

// 设置 token 过期时间（每天凌晨 0 点）
// const TOKEN_EXPIRY = 12 * 60 * 60 * 1000

// 清除登录信息
const clearLoginInfo = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('user')
  localStorage.removeItem('tokenTimestamp')
}

// 检查 token 是否过期
const isTokenExpired = () => {
  const tokenTimestamp = localStorage.getItem('tokenTimestamp')
  if (!tokenTimestamp) return true

  // 获取当前时间
  const now = new Date()

  // 获取 token 创建时间
  const tokenTime = new Date(parseInt(tokenTimestamp))

  // 检查是否已经过了今天的凌晨 0 点
  // 如果 token 创建于今天，则未过期；如果创建于之前的日期，则已过期
  return (
    now.getDate() !== tokenTime.getDate() ||
    now.getMonth() !== tokenTime.getMonth() ||
    now.getFullYear() !== tokenTime.getFullYear()
  )
}

const routes = [
  {
    path: '/',
    redirect: '/home',
  },
  {
    path: '/home',
    name: 'home',
    component: HomePage,
    meta: { requiresAuth: false },
  },
  {
    path: '/messageList',
    name: 'messageList',
    component: () => import('@/views/MessageList.vue'),
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/UserLogin.vue'),
  },
  {
    path: '/register',
    name: 'register',
    component: () => import('@/views/UserRegister.vue'),
  },
  {
    path: '/forgot-account',
    name: 'ForgotAccount',
    component: () => import('@/views/ForgotAccount.vue'),
    meta: {
      title: '找回密码',
    },
  },
  {
    path: '/forgot-username',
    name: 'ForgotUsername',
    component: () => import('@/views/ForgotUsername.vue'),
    meta: {
      title: '找回用户名',
    },
  },
  {
    path: '/reset-password',
    name: 'resetPassword',
    component: () => import('@/views/ResetPassword.vue'),
  },
  {
    path: '/seller-register',
    name: 'sellerRegister',
    component: () => import('@/views/SellerRegister.vue'),
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => import('@/views/UserDashboard.vue'),
    meta: { requiresAuth: true },
    redirect: '/dashboard/profile',
    children: [
      {
        path: 'profile',
        name: 'profile',
        component: () => import('@/views/Profile.vue'),
      },
      {
        path: 'orders',
        name: 'orders',
        component: () => import('@/views/Orders.vue'),
      },
      {
        path: 'security',
        name: 'security',
        component: () => import('@/views/Security.vue'),
      },
      {
        path: 'address',
        name: 'dashboardAddress',
        component: () => import('@/views/AddressManage.vue'),
      },
      {
        path: 'tracking',
        name: 'tracking',
        component: () => import('@/views/TrackingManage.vue'),
      },
      {
        path: 'refunds',
        name: 'refunds',
        component: () => import('@/views/RefundManage.vue'),
      },
    ],
  },
  {
    path: '/user',
    name: 'user',
    component: () => import('@/views/UserDashboard.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'profile',
        name: 'userProfile',
        component: () => import('@/views/UserProfile.vue'),
      },
      {
        path: 'address',
        name: 'addressManage',
        component: () => import('@/views/AddressManage.vue'),
      },
      {
        path: 'message',
        name: 'userMessage',
        component: () => import('@/views/UserMess.vue'),
      },
      {
        path: 'orders',
        name: 'userOrders',
        component: () => import('@/views/Orders.vue'),
        meta: { requiresAuth: true },
      },
    ],
  },
  {
    path: '/product/:id',
    name: 'ProductDetail',
    component: () => import('@/views/ProductDetail.vue'),
    meta: {
      requiresAuth: false,
      title: '商品详情',
    },
  },
  {
    path: '/store/setup',
    name: 'CreateStore',
    component: CreateStore,
    meta: { requiresAuth: true },
  },
  {
    path: '/store/create',
    name: 'createStore',
    component: CreateStore,
    meta: { requiresAuth: true },
  },
  {
    path: '/store/manage',
    name: 'storeManage',
    component: StoreManage,
    meta: { requiresAuth: true },
  },
  {
    path: '/order/create',
    name: 'createOrder',
    component: () => import('@/views/CreateOrder.vue'),
    meta: { requiresAuth: true },
    props: (route: RouteLocationNormalized) => ({
      products: route.query.products ? JSON.parse(route.query.products as string) : [],
    }),
  },
  // 添加订单详情路由，使用正确的路径
  {
    path: '/orders/:id',
    name: 'orderDetail',
    component: () => import('@/views/OrderDetail.vue'),
    meta: { requiresAuth: true },
    props: true,
  },
  {
    path: '/logistics/tracking/:id',
    name: 'logisticsTracking',
    component: () => import('@/views/LogisticsTracking.vue'),
    meta: { requiresAuth: true },
    props: true,
  },
  {
    path: '/privacy-policy',
    name: 'privacyPolicy',
    component: () => import('@/components/PrivacyPolicy.vue'),
    meta: { requiresAuth: false },
  },
  {
    path: '/shipping-policy',
    name: 'shippingPolicy',
    component: () => import('@/components/ShippingPolicy.vue'),
    meta: { requiresAuth: false },
  },
  {
    path: '/refund-policy',
    name: 'refundPolicy',
    component: () => import('@/components/RefundPolicy.vue'),
    meta: { requiresAuth: false },
  },
  {
    path: '/terms-of-service',
    name: 'termsOfService',
    component: () => import('@/components/TermsOfService.vue'),
    meta: { requiresAuth: false },
  },
  {
    path: '/update-notification',
    name: 'updateNotification',
    component: UpdateNotification,
    meta: { requiresAuth: false },
  },
  {
    path: '/18697791815',
    name: 'merchantReceive',
    component: () => import('@/components/MerchantReive.vue'),
    meta: { requiresAuth: false },
  },
  {
    path: '/manager-login',
    name: 'managerLogin',
    component: () => import('@/views/ManagerLogin.vue'),
    meta: { requiresAuth: false },
  },
  {
    path: '/nbs-selected',
    name: 'nbsSelected',
    component: () => import('@/views/NBSSelected.vue'),
    meta: { requiresAuth: false },
  },
  {
    path: '/featured-products',
    name: 'featuredProducts',
    component: () => import('@/views/FeaturedProducts.vue'),
    meta: { requiresAuth: false },
  },
  {
    path: '/new-arrivals',
    name: 'newArrivals',
    component: () => import('@/views/NewArrivals.vue'),
    meta: { requiresAuth: false },
  },
  {
    path: '/best-sellers',
    name: 'bestSellers',
    component: () => import('@/views/BestSellers.vue'),
    meta: { requiresAuth: false },
  },
  {
    path: '/all-products',
    name: 'allProducts',
    component: () => import('@/views/AllProducts.vue'),
    meta: { requiresAuth: false },
  },
  {
    path: '/order/:id/tracking',
    name: 'orderTracking',
    component: () => import('@/views/OrderTracking.vue'),
    meta: { requiresAuth: true },
  },
  {
    path: '/selected',
    name: 'SelectedProducts',
    component: () => import('@/views/SelectedProducts.vue'),
  },
  {
    path: '/newArrivals',
    name: 'NewArrivals',
    component: () => import('@/views/NewArrivals.vue'),
  },
  {
    path: '/bestSellers',
    name: 'BestSellers',
    component: () => import('@/views/BestSellers.vue'),
  },
  {
    path: '/allProducts',
    name: 'AllProducts',
    component: () => import('@/views/AllProducts.vue'),
  },
  {
    path: '/payment',
    name: 'payment',
    component: () => import('@/views/PaymentPage.vue'),
    meta: { requiresAuth: true },
    props: (route) => ({
      products: route.query.products ? JSON.parse(route.query.products as string) : [],
      orderInfo: route.query.orderInfo ? JSON.parse(route.query.orderInfo as string) : {},
    }),
  },
  // 添加临时订单详情页面路由
  {
    path: '/temp-order-detail',
    name: 'tempOrderDetail',
    component: () => import('@/views/TempOrderDetail.vue'),
    meta: { requiresAuth: true },
  },
  // 退款功能演示页面
  {
    path: '/refund-demo',
    name: 'refundDemo',
    component: () => import('@/views/RefundDemo.vue'),
    meta: { requiresAuth: false },
  },
  // 退款功能测试页面
  {
    path: '/refund-test',
    name: 'refundTest',
    component: () => import('@/views/RefundTest.vue'),
    meta: { requiresAuth: false },
  },
  // 用户物流查询页面
  {
    path: '/user-tracking',
    name: 'userTracking',
    component: () => import('@/views/UserTracking.vue'),
    meta: { requiresAuth: false },
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
})

router.beforeEach((to, from, next) => {
  // 获取当前存储的语言设置
  const savedLanguage = localStorage.getItem('language')

  // 如果存在语言设置，确保 i18n 实例使用该语言
  if (savedLanguage) {
    i18n.global.locale.value = savedLanguage
  }

  const token = localStorage.getItem('token')

  // 如果是前往主页或者不需要认证的页面，直接通过
  if (to.path === '/home' || to.meta.requiresAuth === false || to.path === '/') {
    next()
    return
  }

  // 检查 token 是否过期
  if (token && isTokenExpired()) {
    clearLoginInfo()
    if (to.meta.requiresAuth) {
      ElMessage.warning('登录已过期，请重新登录')
      next('/login')
      return
    }
    next()
    return
  }

  // 需要认证且未登录时跳转到登录页
  if (to.meta.requiresAuth && !token) {
    localStorage.setItem('redirectPath', to.fullPath)
    ElMessage.warning('请先登录')
    next('/login')
    return
  }

  // 已登录时访问登录/注册页跳转到主页
  if (token && (to.path === '/login' || to.path === '/register')) {
    next('/home')
    return
  }

  next()
})

export default router
