import { createI18n } from 'vue-i18n'
import en from './en'
import zh from './zh'
import zhLocale from '@/locales/zh'
import enLocale from '@/locales/en'

// 合并语言包
const messages = {
  zh: { ...zh, ...zhLocale },
  en: { ...en, ...enLocale }
}

export const i18n = createI18n({
  legacy: false, // 使用组合式API
  locale: localStorage.getItem('language') || 'zh', // 默认语言
  fallbackLocale: 'zh', // 回退语言
  messages,
  globalInjection: true,
})
