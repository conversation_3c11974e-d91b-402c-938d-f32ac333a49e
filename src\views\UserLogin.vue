<template>
  <div class="min-h-screen flex relative">
    <!-- Language Switcher -->
    <div class="absolute top-4 right-4 z-10">
      <button @click="toggleLanguage" class="text-purple-600 hover:text-purple-700 font-medium">
        {{ $t('header.language') }}
      </button>
    </div>

    <!-- Left Side with Animation -->
    <div class="hidden lg:flex lg:w-1/2 relative overflow-hidden bg-[#F8FAFC]">
      <!-- Animated Background Blobs -->
      <div
        class="absolute top-0 -left-4 w-72 h-72 bg-purple-300/50 rounded-full mix-blend-multiply filter blur-xl animate-blob"
      ></div>
      <div
        class="absolute top-0 -right-4 w-72 h-72 bg-orange-300/50 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"
      ></div>
      <div
        class="absolute -bottom-8 left-20 w-72 h-72 bg-blue-300/50 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"
      ></div>

      <!-- Illustration Container -->
      <div class="relative w-full h-full flex items-center justify-center p-12">
        <div class="relative w-full max-w-lg">
          <!-- Animated Elements -->
          <div class="absolute top-0 left-0 w-40 h-40 animate-float-slow">
            <div class="w-full h-full bg-white rounded-lg shadow-lg p-4">
              <div class="w-full h-2 bg-gray-200 rounded mb-2"></div>
              <div class="w-3/4 h-2 bg-gray-200 rounded mb-2"></div>
              <div class="w-1/2 h-2 bg-gray-200 rounded"></div>
            </div>
          </div>

          <div class="absolute top-20 right-0 animate-float">
            <div class="w-32 h-32 bg-white rounded-full shadow-lg flex items-center justify-center">
              <div class="w-20 h-20 border-4 border-purple-500 rounded-full relative">
                <div class="absolute -right-1 -top-1 w-6 h-6 bg-purple-500 rounded-full"></div>
              </div>
            </div>
          </div>

          <div class="absolute bottom-0 left-1/4 animate-float-fast">
            <div
              class="w-24 h-24 bg-white rounded-lg shadow-lg transform rotate-45 flex items-center justify-center"
            >
              <div class="transform -rotate-45">
                <div class="w-12 h-12 border-t-2 border-l-2 border-purple-500"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Side Login Form -->
    <div class="w-full lg:w-1/2 flex items-center justify-center p-8">
      <div class="w-full max-w-md space-y-8">
        <div class="text-center">
          <h2 class="text-3xl font-bold text-gray-900">{{ $t('auth.login') }}</h2>
          <p class="mt-2 text-sm text-gray-600">{{ $t('welcomeBack') }}</p>
        </div>

        <form @submit.prevent="handleSubmit" class="mt-8 space-y-6">
          <!-- Account Input -->
          <div class="space-y-2">
            <label for="account" class="block text-sm font-medium text-gray-700">
              {{ $t('auth.accountOrEmail') }}
            </label>
            <div class="relative">
              <input
                v-model="form.accountName"
                id="account"
                type="text"
                required
                class="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ease-in-out"
                :class="{ 'border-red-500 focus:ring-red-500': errors.accountName }"
                :placeholder="$t('auth.accountOrEmailPlaceholder')"
              />
              <div v-if="errors.accountName" class="absolute -bottom-6 left-0 text-sm text-red-500">
                {{ errors.accountName }}
              </div>
            </div>
          </div>

          <!-- Password Input -->
          <div class="space-y-2">
            <label for="password" class="block text-sm font-medium text-gray-700">
              {{ $t('auth.password') }}
            </label>
            <div class="relative">
              <input
                v-model="form.password"
                id="password"
                :type="showPassword ? 'text' : 'password'"
                required
                class="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ease-in-out"
                :class="{ 'border-red-500 focus:ring-red-500': errors.password }"
                :placeholder="$t('auth.password')"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <EyeIcon v-if="showPassword" class="h-5 w-5" />
                <EyeOffIcon v-else class="h-5 w-5" />
              </button>
              <div v-if="errors.password" class="absolute -bottom-6 left-0 text-sm text-red-500">
                {{ errors.password }}
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <button
            type="submit"
            :disabled="loading"
            class="w-full py-3 px-4 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <div class="flex items-center justify-center">
              <LoaderIcon v-if="loading" class="w-5 h-5 mr-2 animate-spin" />
              <span>{{ loading ? $t('auth.loggingIn') : $t('auth.login') }}</span>
            </div>
          </button>

          <!-- Terms -->
          <div class="text-center text-sm text-gray-500">
            <p>
              {{ $t('auth.terms.agreement') }}
              <a
                @click="handleTermsClick('privacy')"
                class="text-purple-600 hover:text-purple-500 transition-colors duration-200"
              >
                {{ $t('auth.terms.userAgreement') }}
              </a>
              {{ $t('auth.terms.and') }}
              <a
                @click="handleTermsClick('policy')"
                class="text-purple-600 hover:text-purple-500 transition-colors duration-200"
              >
                {{ $t('auth.terms.privacyPolicy') }}
              </a>
            </p>
          </div>

          <!-- Register Link -->
          <div class="text-center text-sm">
            <span class="text-gray-600">{{ $t('auth.noAccount') }}</span>
            <router-link
              to="/register"
              class="text-purple-600 hover:text-purple-700 font-medium transition-colors ml-1"
            >
              {{ $t('auth.userRegister') }}
            </router-link>
            <span class="text-gray-600 mx-2">|</span>
            <a
              href="http://shop.sharewharf.com//"
              target="_blank"
              class="text-purple-600 hover:text-purple-700 font-medium transition-colors"
            >
              {{ $t('auth.sellerRegister') }}
            </a>
          </div>

          <!-- 添加找回用户名入口 -->
          <div class="text-center text-sm mt-2">
            <span class="text-gray-600">{{ $t('auth.forgotUsernameTip') }}</span>
            <router-link
              to="/forgot-username"
              class="text-purple-600 hover:text-purple-700 font-medium transition-colors ml-1"
            >
              {{ $t('auth.forgotUsername') }}
            </router-link>
          </div>

          <!-- 添加找回密码入口 -->
          <div class="text-center text-sm mt-2">
            <span class="text-gray-600">{{ $t('auth.forgotPasswordTip') }}</span>
            <router-link
              to="/forgot-account"
              class="text-purple-600 hover:text-purple-700 font-medium transition-colors ml-1"
            >
              {{ $t('auth.forgotPassword') }}
            </router-link>
          </div>

          <!-- Update Notification Link -->
          <div class="text-center text-sm mt-4">
            <span class="text-gray-600"></span>
            <router-link
              to="/update-notification"
              class="text-purple-600 hover:text-purple-700 font-medium transition-colors"
            >
              {{ $t('header.userGuide') }}
            </router-link>
          </div>

          <!-- 添加开发通知弹窗 -->
          <DevelopmentNotice />
        </form>

        <!-- 成功提示弹窗 -->
        <el-dialog v-model="showSuccessModal" title="提交成功" width="400px">
          <div class="text-center">
            <i class="el-icon-success text-green-500 text-5xl mb-4"></i>
            <h2 class="text-2xl font-bold mb-2">已提交申请!</h2>
            <p class="text-gray-600">管理员将于24内进行审核并以短信形式进行通知</p>
          </div>
          <template #footer>
            <el-button type="primary" @click="showSuccessModal = false">用户登录</el-button>
          </template>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { EyeIcon, EyeOffIcon, LoaderIcon, UserRoundIcon } from 'lucide-vue-next'
import type { FormInstance, FormRules } from 'element-plus'
import { buyerApi } from '@/api/buyer'
import type { BuyerLoginDTO, BuyerRegisterDTO } from '@/api/buyer'
import { AxiosError, type AxiosResponse } from 'axios'
import DevelopmentNotice from '@/components/DevelopmentNotice.vue'
import { useI18n } from 'vue-i18n'

const router = useRouter()
const route = useRoute()
const showPassword = ref(false)
const loading = ref(false)
let tokenTimer: number | null = null

const form = reactive<BuyerLoginDTO>({
  accountName: '',
  password: '',
})

// 表单错误信息
const errors = reactive({
  accountName: '',
  password: '',
})

const { t, locale } = useI18n()

const showSuccessModal = ref(false)

const toggleLanguage = () => {
  const newLocale = locale.value === 'zh' ? 'en' : 'zh'
  locale.value = newLocale
  localStorage.setItem('language', newLocale) // 保存用户选择的语言
}

const validateForm = () => {
  let isValid = true
  errors.accountName = ''
  errors.password = ''

  if (!form.accountName || !form.accountName.trim()) {
    errors.accountName = '请输入账号或邮箱'
    isValid = false
  }

  if (!form.password || !form.password.trim()) {
    errors.password = '请输入密码'
    isValid = false
  } else if (form.password.length < 6) {
    errors.password = '密码长度至少为6位'
    isValid = false
  }

  return isValid
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return // 如果表单验证失败，直接返回
  }

  loading.value = true
  try {
    // 判断输入是邮箱还是用户名
    const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.accountName)

    const response = await buyerApi.login({
      accountName: form.accountName,
      password: form.password,
      loginType: isEmail ? 'email' : 'accountName', // 添加登录类型参数
    })

    console.log('Login response:', response) // 添加日志，查看响应结构

    // 处理后端返回的数据结构
    // Axios响应对象中，实际数据在response.data中
    const responseData = response.data

    if (responseData.code === 1) {
      // 登录成功
      const userData = responseData.data

      localStorage.setItem('token', userData.token)
      localStorage.setItem('user', JSON.stringify(userData))
      localStorage.setItem('tokenTimestamp', Date.now().toString())

      // 使用统一的方法处理登录成功
      handleLoginSuccess(userData.token, {
        id: userData.id,
        accountName: userData.accountName || form.accountName,
        lastLoginTime: new Date().toLocaleString(),
      })

      ElMessage.success(responseData.msg || '登录成功！')
      await router.push('/home')
    } else {
      // 登录失败，显示后端返回的错误信息
      ElMessage.error(responseData.msg || '登录失败，请检查账号密码')
    }
  } catch (error: unknown) {
    if (error instanceof AxiosError) {
      console.error('Login error details:', error)
      const errorMsg = error.response?.data?.msg
      ElMessage.error(errorMsg || '登录失败，请稍后重试')
    } else {
      console.error('Unexpected error:', error) // 添加更详细的错误日志
      ElMessage.error('网络错误，请检查网络连接')
    }
  } finally {
    loading.value = false
  }
}

interface UserInfo {
  id: number
  accountName: string
  lastLoginTime: string
}

const handleLoginSuccess = (token: string, userInfo: UserInfo) => {
  localStorage.setItem('token', token)
  localStorage.setItem(
    'user',
    JSON.stringify({
      id: userInfo.id,
      accountName: userInfo.accountName,
      lastLoginTime: userInfo.lastLoginTime,
    }),
  )
  localStorage.setItem('tokenTimestamp', Date.now().toString())
}

// 组件挂载时启动定时器
onMounted(() => {
  const token = localStorage.getItem('token')
  if (token) {
    // startTokenTimer()
  }

  if (route.query.showSuccess === 'true') {
    showSuccessModal.value = true
  }

  // 添加这些调试代码
  console.log('Current locale:', locale.value)
  console.log('adminLoginTip in current locale:', t('auth.adminLoginTip'))
  console.log('adminLogin in current locale:', t('auth.adminLogin'))
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (tokenTimer) {
    clearInterval(tokenTimer)
    tokenTimer = null
  }
})

// 处理协议点击
const handleTermsClick = (type: 'privacy' | 'policy') => {
  router.push({
    path: '/privacy-policy',
    query: { type }, // 可以通过 query 参数区分是用户协议还是隐私政策
  })
}
</script>

<style scoped>
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }

  33% {
    transform: translate(30px, -50px) scale(1.1);
  }

  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }

  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-20px);
  }

  100% {
    transform: translateY(0px);
  }
}

@keyframes float-slow {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }

  100% {
    transform: translateY(0px);
  }
}

@keyframes float-fast {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-15px);
  }

  100% {
    transform: translateY(0px);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 8s ease-in-out infinite;
}

.animate-float-fast {
  animation: float-fast 4s ease-in-out infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}
</style>
