<template>
  <div class="min-h-screen flex items-center justify-center relative overflow-hidden">
    <!-- 主要内容 -->
    <div class="w-full max-w-md relative z-10 p-6">
      <!-- 玻璃态卡片 -->
      <div
        class="backdrop-blur-lg bg-white/40 rounded-3xl shadow-2xl p-8 space-y-8 border border-white/20"
      >
        <!-- Logo和标题 -->
        <div class="text-center space-y-4">
          <div class="w-20 h-20 mx-auto">
            <img src="@/assets/logo1.jpg" alt="Logo" class="w-full h-full object-contain" />
          </div>
          <div>
            <h2
              class="text-3xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent"
            >
              {{ $t('forgotUsername.title') }}
            </h2>
            <p class="mt-2 text-gray-600">{{ $t('forgotUsername.subtitle') }}</p>
          </div>
        </div>

        <!-- 找回表单 -->
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- 邮箱地址 -->
          <div class="space-y-2">
            <label for="email" class="block text-sm font-medium text-gray-700">{{ $t('forgotUsername.emailLabel') }}</label>
            <div class="relative">
              <input
                v-model="form.email"
                id="email"
                type="email"
                required
                class="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ease-in-out"
                :class="{ 'border-red-500 focus:ring-red-500': errors.email }"
                :placeholder="$t('forgotUsername.emailPlaceholder')"
              />
              <div v-if="errors.email" class="absolute -bottom-6 left-0 text-sm text-red-500">
                {{ errors.email }}
              </div>
            </div>
          </div>

          <!-- 验证码 -->
          <div class="space-y-2">
            <label for="verificationCode" class="block text-sm font-medium text-gray-700">
              {{ $t('forgotUsername.verificationCodeLabel') }}
            </label>
            <div class="flex gap-4">
              <div class="relative flex-1">
                <input
                  v-model="form.verificationCode"
                  id="verificationCode"
                  type="text"
                  required
                  class="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ease-in-out"
                  :class="{
                    'border-red-500 focus:ring-red-500': errors.verificationCode,
                  }"
                  :placeholder="$t('forgotUsername.verificationCodePlaceholder')"
                />
                <div
                  v-if="errors.verificationCode"
                  class="absolute -bottom-6 left-0 text-sm text-red-500"
                >
                  {{ errors.verificationCode }}
                </div>
              </div>
              <button
                type="button"
                :disabled="isCountingDown || !form.email"
                @click.prevent="handleSendCode"
                class="verification-button-new group relative overflow-hidden flex items-center justify-center gap-2 min-w-[140px] h-[48px] px-4 text-white font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span class="relative z-10 flex items-center justify-center gap-2">
                  <span v-if="sendingCode" class="animate-spin">
                    <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none">
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      ></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  </span>
                  <span v-else class="transform transition-transform group-hover:scale-110">
                    <svg
                      class="w-5 h-5"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M2.5 12C2.5 7.52166 6.13388 3.875 10.5938 3.875C13.8612 3.875 16.7012 5.80875 18 8.5M21.5 12C21.5 16.4783 17.8661 20.125 13.4062 20.125C10.1388 20.125 7.29875 18.1912 6 15.5"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M19 8.5H18C17.4477 8.5 17 8.05228 17 7.5V6.5M5 15.5H6C6.55228 15.5 7 15.9477 7 16.5V17.5"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  {{ isCountingDown ? $t('forgotUsername.resendCode', { seconds: countdown }) : $t('forgotUsername.sendCode') }}
                </span>
                <div
                  class="absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 opacity-100 group-hover:opacity-90 transition-opacity duration-300"
                ></div>
                <div
                  class="absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 opacity-0 group-hover:opacity-100 blur-lg transition-opacity duration-300"
                ></div>
              </button>
            </div>
            <div class="mt-1 text-sm text-gray-600 bg-white/60 rounded-lg p-2">
              <el-icon class="align-middle mr-1"><InfoFilled /></el-icon>
              {{ $t('forgotUsername.codeTip') }}
            </div>
          </div>

          <!-- 提交按钮 -->
          <button
            type="submit"
            :disabled="loading"
            class="w-full py-3 px-4 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <div class="flex items-center justify-center">
              <span v-if="loading" class="w-5 h-5 mr-2 animate-spin">
                <svg viewBox="0 0 24 24" fill="none" class="w-5 h-5">
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              </span>
              <span>{{ loading ? $t('forgotUsername.verifying') : $t('forgotUsername.submitButton') }}</span>
            </div>
          </button>

          <!-- 返回登录 -->
          <div class="text-center mt-6">
            <p class="text-sm text-gray-600">
              {{ $t('forgotUsername.rememberUsername') }}
              <router-link
                to="/login"
                class="text-purple-600 hover:text-purple-700 font-medium transition-colors"
              >
                {{ $t('forgotUsername.backToLogin') }}
              </router-link>
            </p>
          </div>
        </form>

        <!-- 用户名显示弹窗 -->
        <el-dialog
          v-model="showUsernameModal"
          :show-close="false"
          width="460px"
          :modal="true"
          :close-on-click-modal="false"
          class="username-dialog"
          align-center
        >
          <div class="success-container">
            <!-- 成功图标动画 -->
            <div class="success-animation mb-8">
              <div class="success-circle">
                <div class="success-icon">
                  <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                    <circle class="checkmark-circle" cx="26" cy="26" r="25" fill="none" />
                    <path class="checkmark-check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                  </svg>
                </div>
              </div>
            </div>

            <!-- 标题 -->
            <h2 class="text-3xl font-bold text-gray-800 mb-4">
              {{ usernameUpdated ? '操作成功!' : '验证成功!' }}
            </h2>

            <!-- 用户名信息 -->
            <div v-if="recoveredUsername || usernameUpdated" class="bg-gray-50 p-6 rounded-xl mb-6">
              <p class="text-gray-600 mb-2">您的用户名是:</p>
              <p class="text-2xl font-bold text-purple-600 mb-2">{{ recoveredUsername }}</p>
              <p v-if="recoveredEmail" class="text-gray-600 mb-2 mt-4">您的邮箱是:</p>
              <p v-if="recoveredEmail" class="text-xl font-medium text-purple-600">
                {{ recoveredEmail }}
              </p>
              <p v-if="usernameUpdated" class="mt-4 text-green-600 font-medium">
                {{ recoveredUsername ? '用户名修改成功!' : '用户名设置成功!' }}
              </p>
            </div>

            <!-- 修改用户名表单 - 只在未更新用户名时显示 -->
            <form v-if="!usernameUpdated" @submit.prevent="updateUsername" class="mb-6 space-y-4">
              <div class="space-y-2">
                <label for="newUsername" class="block text-sm font-medium text-gray-700">
                  {{ recoveredUsername ? '新用户名' : '设置用户名' }}
                </label>
                <div class="relative">
                  <input
                    v-model="newUsername"
                    id="newUsername"
                    type="text"
                    required
                    class="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ease-in-out"
                    :class="{ 'border-red-500 focus:ring-red-500': usernameError }"
                    :placeholder="recoveredUsername ? '请输入新的用户名' : '请设置您的用户名'"
                  />
                  <div v-if="usernameError" class="mt-1 text-sm text-red-500">
                    {{ usernameError }}
                  </div>
                </div>
              </div>
              <div class="flex gap-3">
                <button
                  type="button"
                  @click="cancelUpdateUsername"
                  class="flex-1 py-3 px-4 bg-gray-100 text-gray-700 hover:bg-gray-200 rounded-lg transition-all duration-200"
                >
                  取消
                </button>
                <button
                  type="submit"
                  :disabled="updatingUsername"
                  class="flex-1 py-3 px-4 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all duration-200 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <div class="flex items-center justify-center">
                    <span v-if="updatingUsername" class="w-5 h-5 mr-2 animate-spin">
                      <svg viewBox="0 0 24 24" fill="none" class="w-5 h-5">
                        <circle
                          class="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          stroke-width="4"
                        ></circle>
                        <path
                          class="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                    </span>
                    <span>{{
                      updatingUsername ? '提交中...' : recoveredUsername ? '确认修改' : '确认设置'
                    }}</span>
                  </div>
                </button>
              </div>
            </form>

            <!-- 登录按钮 -->
            <button
              @click="goToLogin"
              class="w-full max-w-sm mx-auto py-4 px-6 bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-lg font-medium rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
            >
              <span class="flex items-center justify-center">
                <svg
                  class="w-6 h-6 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
                  ></path>
                </svg>
                立即登录
              </span>
            </button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'
import type { AxiosError } from 'axios'
import { InfoFilled } from '@element-plus/icons-vue'
import type { ApiResponse } from '@/types/account'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter()
const loading = ref(false)
const sendingCode = ref(false)
const isCountingDown = ref(false)
const countdown = ref(60)
const showUsernameModal = ref(false)
const recoveredUsername = ref('')
const recoveredEmail = ref('')

// 修改用户名相关状态
const isUpdatingUsername = ref(false)
const updatingUsername = ref(false)
const newUsername = ref('')
const usernameError = ref('')
const usernameUpdated = ref(false)

// 表单数据
const form = reactive({
  email: '',
  verificationCode: '',
})

// 表单错误信息
const errors = reactive({
  email: '',
  verificationCode: '',
})

// 倒计时文本
const countDownText = computed(() => {
  return isCountingDown.value ? `${countdown.value}秒后重试` : '发送验证码'
})

// 开始倒计时
const startCountdown = () => {
  isCountingDown.value = true
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
      isCountingDown.value = false
    }
  }, 1000)
}

// 发送验证码
const handleSendCode = async () => {
  // 验证输入
  if (!form.email) {
    errors.email = '请输入邮箱地址'
    return
  }

  // 验证邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(form.email)) {
    errors.email = '请输入正确的邮箱格式'
    return
  }

  sendingCode.value = true
  try {
    // 构建请求数据
    const requestData = {
      address: form.email,
    }

    console.log('发送验证码请求数据:', requestData)

    const response = await request<ApiResponse<null>>({
      method: 'post',
      url: '/buyer/register/sendCode',
      data: requestData,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (response.data.code === 1) {
      startCountdown()
      ElMessage.success(response.data.msg || '验证码已发送')
    } else {
      ElMessage.error(response.data.msg || '发送验证码失败')
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    ElMessage.error('发送验证码失败，请重试')
  } finally {
    sendingCode.value = false
  }
}

// 验证表单
const validateForm = () => {
  let isValid = true
  errors.email = ''
  errors.verificationCode = ''

  if (!form.email) {
    errors.email = '请输入邮箱地址'
    isValid = false
  } else {
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(form.email)) {
      errors.email = '请输入正确的邮箱格式'
      isValid = false
    }
  }

  if (!form.verificationCode) {
    errors.verificationCode = '请输入验证码'
    isValid = false
  } else if (form.verificationCode.length !== 6) {
    errors.verificationCode = '验证码长度应为6位'
    isValid = false
  }

  return isValid
}

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true
  try {
    // 构建请求数据，与 AccountVerifyRequest 保持一致
    const requestData = {
      address: form.email,
      verificationCode: form.verificationCode,
      // userName 字段不传
    }

    console.log('发送的请求数据:', requestData)

    const response = await request<ApiResponse<{ username: string; email: string } | null>>({
      method: 'post',
      url: '/buyer/forgotUsername',
      data: requestData,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (response.data.code === 1) {
      // 验证成功，显示修改用户名表单
      // 如果后端返回了用户名信息，则显示
      if (response.data.data) {
        recoveredUsername.value = response.data.data.username
        recoveredEmail.value = response.data.data.email
      } else {
        // 如果后端没有返回用户名信息，则使用邮箱作为默认值
        recoveredEmail.value = form.email
      }

      // 直接显示修改用户名表单
      showUsernameModal.value = true
      isUpdatingUsername.value = true
    } else {
      // 验证失败
      ElMessage.error(response.data.msg || '身份验证失败，请检查输入信息')
    }
  } catch (error) {
    console.error('身份验证失败:', error)

    // 安全地获取错误消息
    let errMsg = '身份验证失败，请重试'
    if (error && typeof error === 'object' && 'response' in error) {
      const response = error.response
      if (response && typeof response === 'object' && 'data' in response) {
        const data = response.data
        if (data && typeof data === 'object' && 'msg' in data) {
          errMsg = data.msg as string
        }
      }
    }

    ElMessage.error(errMsg)
  } finally {
    loading.value = false
  }
}

// 跳转到登录页面
const goToLogin = () => {
  router.push('/login')
}

// 显示修改用户名表单
const showUpdateUsernameForm = () => {
  isUpdatingUsername.value = true
  newUsername.value = recoveredUsername.value
}

// 取消修改用户名
const cancelUpdateUsername = () => {
  isUpdatingUsername.value = false
  usernameError.value = ''
}

// 提交修改用户名
const updateUsername = async () => {
  // 验证新用户名
  if (!newUsername.value) {
    usernameError.value = '请输入用户名'
    return
  }

  if (newUsername.value.length < 3) {
    usernameError.value = '用户名长度至少为3个字符'
    return
  }

  // 如果有现有用户名且新用户名与现有用户名相同
  if (recoveredUsername.value && newUsername.value === recoveredUsername.value) {
    usernameError.value = '新用户名不能与当前用户名相同'
    return
  }

  updatingUsername.value = true
  try {
    // 构建请求数据，按照后端需要的字段格式
    const requestData = {
      accountName: newUsername.value,
      email: recoveredEmail.value || form.email,
    }

    console.log('修改用户名请求数据:', requestData)

    const response = await request<ApiResponse<null>>({
      method: 'put',
      url: '/buyer/changeUsername',
      data: requestData,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (response.data.code === 1) {
      ElMessage.success(recoveredUsername.value ? '用户名修改成功' : '用户名设置成功')
      recoveredUsername.value = newUsername.value
      isUpdatingUsername.value = false
      usernameUpdated.value = true // 标记用户名已更新
    } else {
      usernameError.value =
        response.data.msg ||
        (recoveredUsername.value ? '修改用户名失败，请重试' : '设置用户名失败，请重试')
    }
  } catch (error) {
    console.error(recoveredUsername.value ? '修改用户名失败:' : '设置用户名失败:', error)

    // 安全地获取错误消息
    let errMsg = recoveredUsername.value ? '修改用户名失败，请重试' : '设置用户名失败，请重试'
    if (error && typeof error === 'object' && 'response' in error) {
      const response = error.response
      if (response && typeof response === 'object' && 'data' in response) {
        const data = response.data
        if (data && typeof data === 'object' && 'msg' in data) {
          errMsg = data.msg as string
        }
      }
    }

    usernameError.value = errMsg
  } finally {
    updatingUsername.value = false
  }
}
</script>

<style scoped>
/* 验证码按钮样式 */
.verification-button-new {
  position: relative;
  border: none;
  border-radius: 0.5rem;
  overflow: hidden;
  transform: translateY(0);
  will-change: transform;
}

.verification-button-new:not(:disabled) {
  box-shadow:
    0 4px 6px -1px rgba(147, 51, 234, 0.1),
    0 2px 4px -1px rgba(147, 51, 234, 0.06);
}

.verification-button-new:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow:
    0 10px 15px -3px rgba(147, 51, 234, 0.2),
    0 4px 6px -2px rgba(147, 51, 234, 0.1);
}

.verification-button-new:not(:disabled):active {
  transform: translateY(0);
}

.verification-button-new:disabled {
  background: rgba(229, 231, 235, 0.8);
  color: #9ca3af;
}

.verification-button-new.sending {
  animation: sending-pulse 2s infinite;
}

@keyframes sending-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(147, 51, 234, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(147, 51, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(147, 51, 234, 0);
  }
}

/* 添加发光效果 */
.verification-button-new::after {
  content: '';
  position: absolute;
  inset: -1px;
  background: linear-gradient(to right, #9333ea, #6366f1);
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  padding: 2px;
  border-radius: 0.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.verification-button-new:not(:disabled):hover::after {
  opacity: 1;
}

/* 成功动画样式 */
.success-animation {
  position: relative;
  width: 100px;
  height: 100px;
  margin: 0 auto;
}

.checkmark {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: block;
  stroke-width: 3;
  stroke: #7c3aed;
  stroke-miterlimit: 10;
  margin: 0 auto;
  box-shadow: 0 0 30px rgba(124, 58, 237, 0.2);
  animation: scale 0.5s ease-in-out 0.3s forwards;
}

.checkmark-circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 3;
  stroke-miterlimit: 10;
  stroke: #7c3aed;
  fill: none;
  animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

.checkmark-check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  stroke-width: 3;
  animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
}

@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes scale {
  0%,
  100% {
    transform: none;
  }
  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}

/* 用户名弹窗样式 */
.username-dialog :deep(.el-dialog) {
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.98));
  backdrop-filter: blur(20px);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.5);
  border: none;
  padding: 0;
}

.username-dialog :deep(.el-dialog__header) {
  display: none;
}

.username-dialog :deep(.el-dialog__body) {
  padding: 0;
  margin: 0;
}

.success-container {
  padding: 3rem 2rem;
  text-align: center;
}
</style>
