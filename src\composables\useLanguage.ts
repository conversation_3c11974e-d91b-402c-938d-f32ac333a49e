import { ref } from 'vue'
import zhLocale from '../locales/zh'
import enLocale from '../locales/en'

type LocaleMessages = typeof zhLocale

export function useLanguage() {
  const currentLang = ref(localStorage.getItem('language') || 'zh')
  const messages: Record<string, LocaleMessages> = {
    zh: zhLocale,
    en: enLocale,
  }

  const getMessage = (key: string): string => {
    try {
      const keys = key.split('.')
      let result = messages[currentLang.value]
      for (const k of keys) {
        if (result) {
          result = result[k]
        }
      }
      return result || key
    } catch (error) {
      console.error('Error getting message:', error)
      return key
    }
  }

  const toggleLanguage = () => {
    currentLang.value = currentLang.value === 'zh' ? 'en' : 'zh'
    localStorage.setItem('language', currentLang.value)
  }

  return {
    currentLang,
    toggleLanguage,
    getMessage,
  }
}
