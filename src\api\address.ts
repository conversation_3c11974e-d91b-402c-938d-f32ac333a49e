import request from '@/utils/request'

// 地址接口类型定义
export interface USAddress {
  id?: number
  userId?: number
  name: string
  street: string
  city: string
  state: string
  zipCode: string
  phoneNumber: string
  addressDetail: string
  Default?: number // 是否为默认地址 1-是 0-否
}

// 地址API
export const addressApi = {
  // 获取地址列表
  getAddressList: (userId: number) => {
    return request({
      url: `/user/address/list?userId=${userId}`,
      method: 'get',
    })
  },

  // 获取默认地址
  getDefaultAddress: () => {
    return request({
      url: '/user/address/default',
      method: 'get',
    })
  },

  // 添加地址
  addAddress: (address: USAddress) => {
    return request({
      url: '/user/address/create',
      method: 'post',
      data: address,
    })
  },

  // 更新地址
  updateAddress: (address: USAddress) => {
    return request({
      url: `/user/address/update/${address.id}`,
      method: 'put',
      data: address,
    })
  },

  // 删除地址
  deleteAddress: (id: number) => {
    return request({
      url: `/user/address/delete/${id}`,
      method: 'delete',
    })
  },

  // 设置默认地址
  setDefaultAddress: (id: number) => {
    return request({
      url: `/user/address/default/${id}`,
      method: 'put',
    })
  },
}

export default addressApi
