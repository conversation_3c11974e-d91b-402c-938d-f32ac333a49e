<template>
  <form @submit.prevent="handleSubmit">
    <!-- 邮箱输入部分 -->
    <div>
      <label>
        邮箱
        <input
          id="email"
          type="text"
          v-model="email"
          @change="resetEmailError"
          @blur="validateEmailOnBlur"
          placeholder="您的邮箱@example.com"
        />
      </label>
      <div v-if="emailError" class="error-message">{{ emailError }}</div>
    </div>

    <!-- 支付部分 -->
    <h4>支付信息</h4>
    <div id="payment-element"></div>

    <!-- 提交按钮 -->
    <button :disabled="isLoading" id="submit">
      <span v-if="isLoading" class="spinner">加载中...</span>
      <span v-else>立即支付 {{ formattedAmount }}</span>
    </button>

    <!-- 支付结果消息 -->
    <div v-if="message" class="payment-message">{{ message }}</div>
  </form>
</template>

<script lang="ts">
import { loadStripe } from '@stripe/stripe-js';
import {
  StripeElements,
  StripeElement,
  useStripe,
  useElements
} from '@stripe/stripe-vue';

export default {
  name: 'StripePaymentForm',

  setup() {
    const stripe = useStripe();
    const elements = useElements();

    return {
      stripe,
      elements
    };
  },

  data() {
    return {
      email: '',
      emailError: null,
      message: null,
      isLoading: false,
      checkoutTotal: '¥0.00' // 默认金额
    };
  },

  computed: {
    // 格式化金额显示
    formattedAmount() {
      return this.checkoutTotal;
    }
  },

  mounted() {
    // 初始化Stripe元素
    this.initStripeElements();
  },

  methods: {
    // 初始化支付元素
    async initStripeElements() {
      const stripePromise = loadStripe('你的_stripe_publishable_key');
      const stripe = await stripePromise;

      const elements = stripe.elements();
      const paymentElement = elements.create('payment');
      paymentElement.mount('#payment-element');
    },

    // 重置邮箱错误
    resetEmailError() {
      this.emailError = null;
    },

    // 失去焦点时验证邮箱
    async validateEmailOnBlur() {
      if (!this.email) return;

      try {
        const { isValid, message } = await this.validateEmail(this.email);
        if (!isValid) {
          this.emailError = message;
        }
      } catch (error) {
        console.error('邮箱验证出错:', error);
      }
    },

    // 验证邮箱有效性
    async validateEmail(email) {
      // 这里应该是调用Stripe API验证邮箱
      // 模拟验证逻辑
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const isValid = emailRegex.test(email);

      return {
        isValid,
        message: isValid ? null : '请输入有效的邮箱地址'
      };
    },

    // 提交支付表单
    async handleSubmit() {
      this.isLoading = true;

      // 先验证邮箱
      const { isValid, message } = await this.validateEmail(this.email);
      if (!isValid) {
        this.emailError = message;
        this.message = message;
        this.isLoading = false;
        return;
      }

      try {
        // 确认支付
        const { error } = await this.stripe.confirmPayment({
          elements: this.elements,
          confirmParams: {
            return_url: 'https://your-website.com/return',
          },
        });

        if (error) {
          this.message = error.message;
        }
      } catch (error) {
        console.error('支付出错:', error);
        this.message = '支付过程中出现错误，请重试';
      } finally {
        this.isLoading = false;
      }
    }
  }
}
</script>

<style scoped>
.error-message {
  color: red;
  font-size: 0.8em;
  margin-top: 0.25em;
}

.payment-message {
  margin-top: 1em;
  padding: 0.75em;
  border-radius: 4px;
}

#submit {
  margin-top: 1em;
  padding: 0.75em 1.5em;
  background-color: #006aff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

#submit:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.spinner {
  display: inline-block;
  width: 1em;
  height: 1em;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>