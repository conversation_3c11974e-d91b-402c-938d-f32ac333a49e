<template>
  <div class="refund-page">
    <div class="background-circle bg-circle-1"></div>
    <div class="background-circle bg-circle-2"></div>
    <div class="background-circle bg-circle-3"></div>

    <!-- 退款管理页面 -->
    <div class="container">
      <div class="header">
        <h1>退款管理中心</h1>
        <p>查看和管理您的退款申请记录</p>
      </div>

      <div class="card refund-tabs">
        <div
          v-for="(tab, index) in tabs"
          :key="index"
          :class="['tab-item', { active: activeTab === index }]"
          @click="activeTab = index"
        >
          {{ tab.name }}
          <span class="count">{{ tab.count }}</span>
        </div>
      </div>

      <div class="card search-section">
        <div class="search-input">
          <input type="text" v-model="searchQuery" placeholder="搜索订单号/退款单号" />
          <button class="search-btn">
            <i class="icon-search"></i>
          </button>
        </div>
        <div class="filter-options">
          <div class="date-filter">
            <span>日期：</span>
            <select v-model="dateFilter">
              <option value="all">全部时间</option>
              <option value="today">今天</option>
              <option value="week">本周</option>
              <option value="month">本月</option>
            </select>
          </div>
          <div class="status-filter">
            <span>状态：</span>
            <select v-model="statusFilter">
              <option value="all">全部状态</option>
              <option :value="RefundStatus.PENDING">待处理</option>
              <option :value="RefundStatus.APPROVED">已同意</option>
              <option :value="RefundStatus.PROCESSING">退款中</option>
              <option :value="RefundStatus.COMPLETED">退款成功</option>
              <option :value="RefundStatus.FAILED">退款失败</option>
              <option :value="RefundStatus.REJECTED">已拒绝</option>
              <option :value="RefundStatus.CANCELLED">已取消</option>
            </select>
          </div>
        </div>
      </div>

      <div class="card refund-list">
        <div v-if="loading" class="loading-state">
          <div class="spinner"></div>
          <p>正在加载退款记录...</p>
        </div>

        <div v-else-if="filteredRefunds.length === 0" class="empty-state">
          <div class="empty-icon">💰</div>
          <p>暂无退款记录</p>
        </div>

        <div v-else v-for="refund in filteredRefunds" :key="refund.id" class="refund-item">
          <div class="refund-header">
            <div class="refund-id">退款单号：{{ refund.refundNo }}</div>
            <div class="refund-date">{{ formatDate(refund.createTime) }}</div>
            <div :class="['refund-status', `status-${refund.applicationStatus}`]">
              {{ refund.applicationStatusDesc || getStatusText(refund.applicationStatus) }}
            </div>
          </div>

          <div class="refund-info">
            <div class="info-row">
              <span class="label">原订单号：</span>
              <span class="value">{{ refund.orderNumber }}</span>
            </div>
            <div class="info-row">
              <span class="label">退款金额：</span>
              <span class="value amount">¥{{ refund.refundAmount.toFixed(2) }}</span>
            </div>
            <div class="info-row">
              <span class="label">退款原因：</span>
              <span class="value">{{ refund.refundReason }}</span>
            </div>
            <div v-if="refund.refundTime" class="info-row">
              <span class="label">退款完成时间：</span>
              <span class="value">{{ formatDate(refund.refundTime) }}</span>
            </div>
            <div v-if="refund.approvalTime" class="info-row">
              <span class="label">审核时间：</span>
              <span class="value">{{ formatDate(refund.approvalTime) }}</span>
            </div>
          </div>

          <div class="refund-actions">
            <button class="action-btn detail-btn" @click="viewRefundDetail(refund)">
              查看详情
            </button>
            <button
              v-if="refund.applicationStatus === RefundStatus.REFUNDING"
              class="action-btn query-btn"
              @click="queryRefundStatus(refund.refundNo)"
            >
              查询状态
            </button>
            <button
              v-if="canCancelRefund(refund)"
              class="action-btn cancel-btn"
              @click="cancelRefund(refund)"
            >
              取消申请
            </button>
          </div>
        </div>
      </div>

      <div class="pagination">
        <button class="page-btn prev" :disabled="currentPage === 1" @click="currentPage--">
          上一页
        </button>
        <div class="page-numbers">
          <span
            v-for="page in totalPages"
            :key="page"
            :class="['page-number', { active: currentPage === page }]"
            @click="currentPage = page"
          >
            {{ page }}
          </span>
        </div>
        <button class="page-btn next" :disabled="currentPage === totalPages" @click="currentPage++">
          下一页
        </button>
        <span class="pagination-info">共 {{ total }} 条，{{ totalPages }} 页</span>
      </div>
    </div>

    <!-- 退款详情弹窗 -->
    <div v-if="showDetailModal" class="modal-overlay" @click="closeDetailModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>退款详情</h3>
          <button class="close-btn" @click="closeDetailModal">×</button>
        </div>
        <div class="modal-body">
          <div v-if="selectedRefund" class="detail-content">
            <div class="detail-section">
              <h4>基本信息</h4>
              <div class="detail-grid">
                <div class="detail-item">
                  <span class="detail-label">退款单号：</span>
                  <span class="detail-value">{{ selectedRefund.refundNo }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">退款申请ID：</span>
                  <span class="detail-value">{{ selectedRefund.id }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">原订单号：</span>
                  <span class="detail-value">{{ selectedRefund.orderNumber }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">退款状态：</span>
                  <span class="detail-value">{{
                    selectedRefund.applicationStatusDesc ||
                    getStatusText(selectedRefund.applicationStatus)
                  }}</span>
                </div>
              </div>
            </div>

            <div class="detail-section">
              <h4>退款信息</h4>
              <div class="detail-grid">
                <div class="detail-item">
                  <span class="detail-label">退款金额：</span>
                  <span class="detail-value amount"
                    >¥{{ selectedRefund.refundAmount.toFixed(2) }}</span
                  >
                </div>
                <div class="detail-item">
                  <span class="detail-label">退款原因：</span>
                  <span class="detail-value">{{ selectedRefund.refundReason }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">退款类型：</span>
                  <span class="detail-value">{{
                    selectedRefund.refundTypeDesc ||
                    (selectedRefund.refundType === 1 ? '仅退款' : '退货退款')
                  }}</span>
                </div>
                <div v-if="selectedRefund.remark" class="detail-item">
                  <span class="detail-label">备注：</span>
                  <span class="detail-value">{{ selectedRefund.remark }}</span>
                </div>
              </div>
            </div>

            <div class="detail-section">
              <h4>时间信息</h4>
              <div class="detail-grid">
                <div class="detail-item">
                  <span class="detail-label">申请时间：</span>
                  <span class="detail-value">{{ formatDate(selectedRefund.createTime) }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">更新时间：</span>
                  <span class="detail-value">{{ formatDate(selectedRefund.updateTime) }}</span>
                </div>
                <div v-if="selectedRefund.approvalTime" class="detail-item">
                  <span class="detail-label">审核时间：</span>
                  <span class="detail-value">{{ formatDate(selectedRefund.approvalTime) }}</span>
                </div>
                <div v-if="selectedRefund.refundTime" class="detail-item">
                  <span class="detail-label">退款完成时间：</span>
                  <span class="detail-value">{{ formatDate(selectedRefund.refundTime) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { refundApi, type RefundApplicationVO, RefundStatus } from '@/api/order'

defineOptions({
  name: 'RefundManage',
})

// 状态变量
const loading = ref(false)
const refunds = ref<RefundApplicationVO[]>([])
const activeTab = ref(0)
const searchQuery = ref('')
const dateFilter = ref('all')
const statusFilter = ref('all')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const showDetailModal = ref(false)
const selectedRefund = ref<RefundApplicationVO | null>(null)

// 标签页数据
const tabs = [
  { name: '全部退款', count: 0 },
  { name: '待处理', count: 0, status: RefundStatus.PENDING },
  { name: '已同意', count: 0, status: RefundStatus.APPROVED },
  { name: '已拒绝', count: 0, status: RefundStatus.REJECTED },
  { name: '已取消', count: 0, status: RefundStatus.CANCELLED },
  { name: '退款中', count: 0, status: RefundStatus.REFUNDING },
  { name: '退款成功', count: 0, status: RefundStatus.SUCCESS },
  { name: '退款失败', count: 0, status: RefundStatus.FAILED },
]

// 获取退款状态文本
const getStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    [RefundStatus.PENDING]: '待处理',
    [RefundStatus.APPROVED]: '已同意',
    [RefundStatus.REJECTED]: '已拒绝',
    [RefundStatus.CANCELLED]: '已取消',
    [RefundStatus.REFUNDING]: '退款中',
    [RefundStatus.SUCCESS]: '退款成功',
    [RefundStatus.FAILED]: '退款失败',
  }
  return statusMap[status] || '未知状态'
}

// 格式化日期
const formatDate = (dateString: string): string => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 过滤退款记录
const filteredRefunds = computed(() => {
  let result = refunds.value

  // 根据标签页过滤
  if (activeTab.value > 0 && tabs[activeTab.value].status !== undefined) {
    result = result.filter((refund) => refund.applicationStatus === tabs[activeTab.value].status)
  }

  // 根据状态过滤
  if (statusFilter.value !== 'all') {
    const statusValue = parseInt(statusFilter.value)
    result = result.filter((refund) => refund.applicationStatus === statusValue)
  }

  // 根据搜索关键词过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(
      (refund) =>
        refund.refundNo.toLowerCase().includes(query) ||
        refund.orderNumber.toLowerCase().includes(query),
    )
  }

  // 根据日期过滤
  if (dateFilter.value !== 'all') {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    result = result.filter((refund) => {
      const refundDate = new Date(refund.createTime)
      if (dateFilter.value === 'today') {
        return refundDate >= today
      } else if (dateFilter.value === 'week') {
        const weekStart = new Date(today)
        weekStart.setDate(today.getDate() - today.getDay())
        return refundDate >= weekStart
      } else if (dateFilter.value === 'month') {
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
        return refundDate >= monthStart
      }
      return true
    })
  }

  return result
})

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(filteredRefunds.value.length / pageSize.value)
})

// 查看退款详情
const viewRefundDetail = (refund: RefundApplicationVO) => {
  selectedRefund.value = refund
  showDetailModal.value = true
}

// 关闭详情弹窗
const closeDetailModal = () => {
  showDetailModal.value = false
  selectedRefund.value = null
}

// 查询退款状态
const queryRefundStatus = async (refundNo: string) => {
  try {
    const response = await refundApi.queryRefundStatus(refundNo)
    if (response.data.code === 1) {
      // 更新本地数据
      const index = refunds.value.findIndex((r) => r.refundNo === refundNo)
      if (index !== -1) {
        refunds.value[index] = response.data.data
      }
      ElMessage.success('退款状态已更新')
    } else {
      ElMessage.error(response.data.msg || '查询退款状态失败')
    }
  } catch (error) {
    console.error('查询退款状态失败:', error)
    ElMessage.error('查询退款状态失败，请稍后重试')
  }
}

// 判断是否可以取消退款
const canCancelRefund = (refund: RefundApplicationVO): boolean => {
  // 只有待处理和已同意状态的退款申请可以取消
  return (
    refund.applicationStatus === RefundStatus.PENDING ||
    refund.applicationStatus === RefundStatus.APPROVED
  )
}

// 取消退款申请
const cancelRefund = async (refund: RefundApplicationVO) => {
  try {
    const response = await refundApi.cancelRefundApplication(refund.id)
    if (response.data.code === 1) {
      ElMessage.success('退款申请已取消')
      // 刷新退款列表
      fetchRefunds()
    } else {
      ElMessage.error(response.data.msg || '取消退款申请失败')
    }
  } catch (error) {
    console.error('取消退款申请失败:', error)
    ElMessage.error('取消退款申请失败，请稍后重试')
  }
}

// 获取退款记录列表
const fetchRefunds = async () => {
  loading.value = true
  try {
    // 直接调用我的退款申请列表API
    const response = await refundApi.getMyRefundApplications(currentPage.value, pageSize.value)

    if (response.data.code === 1) {
      const pageResult = response.data.data
      refunds.value = pageResult.records || []
      total.value = pageResult.total || 0
    } else {
      refunds.value = []
      total.value = 0
      ElMessage.error(response.data.msg || '获取退款记录失败')
    }

    // 更新标签页计数
    updateTabCounts()
  } catch (error) {
    console.error('获取退款记录失败:', error)
    ElMessage.error('获取退款记录失败，请稍后重试')
    refunds.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 更新标签页计数
const updateTabCounts = () => {
  tabs[0].count = refunds.value.length
  tabs[1].count = refunds.value.filter((r) => r.applicationStatus === RefundStatus.PENDING).length
  tabs[2].count = refunds.value.filter((r) => r.applicationStatus === RefundStatus.APPROVED).length
  tabs[3].count = refunds.value.filter((r) => r.applicationStatus === RefundStatus.REJECTED).length
  tabs[4].count = refunds.value.filter((r) => r.applicationStatus === RefundStatus.CANCELLED).length
  tabs[5].count = refunds.value.filter((r) => r.applicationStatus === RefundStatus.REFUNDING).length
  tabs[6].count = refunds.value.filter((r) => r.applicationStatus === RefundStatus.SUCCESS).length
  tabs[7].count = refunds.value.filter((r) => r.applicationStatus === RefundStatus.FAILED).length
}

// 监听标签页变化
watch(activeTab, (newTab) => {
  currentPage.value = 1
  if (newTab === 0) {
    statusFilter.value = 'all'
  } else if (newTab > 0 && newTab < tabs.length && tabs[newTab].status !== undefined) {
    statusFilter.value = tabs[newTab].status.toString()
  }
})

// 页面加载时获取数据
onMounted(() => {
  fetchRefunds()
})
</script>

<style scoped>
.refund-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
  background: transparent;
}

.background-circle {
  display: none;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #8a4af3;
  font-size: 28px;
  margin-bottom: 8px;
}

.header p {
  color: #666;
  font-size: 16px;
}

.card {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.refund-tabs {
  display: flex;
  padding: 0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 15px 0;
  cursor: pointer;
  color: #666;
  position: relative;
  transition: all 0.3s;
}

.tab-item:hover {
  color: #8a4af3;
}

.tab-item.active {
  color: #8a4af3;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background-color: #8a4af3;
  border-radius: 3px;
}

.count {
  display: inline-block;
  background-color: #f0eaff;
  color: #8a4af3;
  border-radius: 10px;
  padding: 2px 8px;
  font-size: 12px;
  margin-left: 5px;
}

.search-section {
  padding: 15px;
}

.search-input {
  display: flex;
  margin-bottom: 15px;
}

.search-input input {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 6px 0 0 6px;
  padding: 10px 15px;
  font-size: 14px;
  outline: none;
}

.search-input input:focus {
  border-color: #8a4af3;
}

.search-btn {
  background-color: #8a4af3;
  color: white;
  border: none;
  border-radius: 0 6px 6px 0;
  padding: 0 20px;
  cursor: pointer;
}

.icon-search::before {
  content: '🔍';
}

.filter-options {
  display: flex;
  gap: 20px;
}

.date-filter,
.status-filter {
  display: flex;
  align-items: center;
}

.date-filter span,
.status-filter span {
  color: #666;
  margin-right: 8px;
}

.date-filter select,
.status-filter select {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 8px 10px;
  font-size: 14px;
  outline: none;
  background-color: white;
}

.refund-list {
  padding: 0;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(138, 74, 243, 0.2);
  border-top-color: #8a4af3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.empty-state {
  padding: 60px 0;
  text-align: center;
  color: #999;
}

.empty-icon {
  font-size: 40px;
  margin-bottom: 10px;
}

.refund-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px;
}

.refund-item:last-child {
  border-bottom: none;
}

.refund-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 14px;
}

.refund-id {
  font-weight: 600;
}

.refund-date {
  color: #999;
}

.refund-status {
  font-weight: 600;
}

.status-success {
  color: #4caf50;
}

.status-processing {
  color: #2196f3;
}

.status-refundclose {
  color: #f44336;
}

.status-abnormal {
  color: #ff9800;
}

.refund-info {
  margin-bottom: 15px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
}

.label {
  color: #666;
  min-width: 100px;
}

.value {
  color: #333;
}

.value.amount {
  color: #ff6b6b;
  font-weight: 600;
}

.refund-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 8px 15px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  border: none;
  transition: all 0.3s;
}

.detail-btn {
  background-color: white;
  color: #666;
  border: 1px solid #e0e0e0;
}

.detail-btn:hover {
  background-color: #f9f9f9;
}

.query-btn {
  background-color: #f0eaff;
  color: #8a4af3;
}

.query-btn:hover {
  background-color: #e5daff;
}

.cancel-btn {
  background-color: #fff3e0;
  color: #f57c00;
}

.cancel-btn:hover {
  background-color: #ffe0b2;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
}

.page-btn {
  padding: 8px 15px;
  border: 1px solid #e0e0e0;
  background-color: white;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.prev {
  border-radius: 6px 0 0 6px;
}

.next {
  border-radius: 0 6px 6px 0;
}

.page-numbers {
  display: flex;
}

.page-number {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e0e0;
  border-left: none;
  cursor: pointer;
  transition: all 0.3s;
}

.page-number.active {
  background-color: #8a4af3;
  color: white;
  border-color: #8a4af3;
}

.pagination-info {
  margin-left: 15px;
  color: #666;
  font-size: 14px;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  color: #333;
  margin-bottom: 10px;
  font-size: 16px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.detail-item {
  display: flex;
  padding: 8px 0;
}

.detail-label {
  color: #666;
  min-width: 120px;
}

.detail-value {
  color: #333;
  font-weight: 500;
}

.detail-value.amount {
  color: #ff6b6b;
  font-weight: 600;
}

@media (max-width: 768px) {
  .filter-options {
    flex-direction: column;
    gap: 10px;
  }

  .refund-header {
    flex-direction: column;
    gap: 5px;
  }

  .refund-actions {
    flex-wrap: wrap;
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }
}
</style>
