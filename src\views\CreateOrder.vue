<template>
  <div class="max-w-4xl mx-auto p-6">
    <div class="bg-white rounded-lg shadow p-6">
      <h2 class="text-2xl font-bold mb-6">创建订单</h2>

      <!-- 配送方式 -->
      <div class="mb-8">
        <h3 class="text-lg font-medium mb-4">配送方式</h3>
        <el-radio-group v-model="form.shippingMethodId" class="w-full">
          <el-radio :label="ShippingMethod.IMMEDIATE" border class="w-full mb-2">
            立即送出
          </el-radio>
          <el-radio :label="ShippingMethod.SCHEDULED" border class="w-full">
            预约时间
            <el-time-picker
              v-if="form.shippingMethodId === ShippingMethod.SCHEDULED"
              v-model="deliveryTime"
              format="HH:mm"
              placeholder="选择配送时间"
              class="ml-4"
            />
          </el-radio>
        </el-radio-group>
      </div>

      <!-- 支付方式 -->
      <div class="mb-8">
        <h3 class="text-lg font-medium mb-4">支付方式</h3>
        <el-radio-group v-model="form.payMethod" class="w-full">
          <el-radio :label="PayMethod.WECHAT" border class="w-full mb-2">
            <div class="flex items-center">
              <img src="/icons/wechat-pay.svg" alt="微信支付" class="w-6 h-6 mr-2" />
              微信支付
            </div>
          </el-radio>
          <el-radio :label="PayMethod.ALIPAY" border class="w-full">
            <div class="flex items-center">
              <img src="/icons/alipay.svg" alt="支付宝" class="w-6 h-6 mr-2" />
              支付宝
            </div>
          </el-radio>
        </el-radio-group>
      </div>

      <!-- 订单商品 -->
      <div class="mb-8">
        <h3 class="text-lg font-medium mb-4">订单商品</h3>
        <div class="border rounded-lg">
          <div class="p-4" v-for="item in orderItems" :key="item.id">
            <div class="flex items-center">
              <img :src="item.image" :alt="item.name" class="w-16 h-16 object-cover rounded" />
              <div class="ml-4 flex-1">
                <h4 class="font-medium">{{ item.name }}</h4>
                <div class="flex justify-between items-center mt-2">
                  <p class="text-red-600">¥{{ item.price }}</p>
                  <el-input-number
                    v-model="item.quantity"
                    :min="1"
                    :max="99"
                    @change="calculateTotal"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 订单备注 -->
      <div class="mb-8">
        <h3 class="text-lg font-medium mb-4">订单备注</h3>
        <el-input
          v-model="form.orderRemark"
          type="textarea"
          rows="3"
          placeholder="请输入订单备注信息"
        />
      </div>

      <!-- 订单金额 -->
      <div class="border-t pt-6">
        <div class="flex justify-between items-center text-lg">
          <span>订单总额：</span>
          <span class="text-red-600 font-bold">¥{{ totalAmount }}</span>
        </div>
        <el-button type="primary" class="w-full mt-4" :loading="loading" @click="submitOrder">
          提交订单
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { orderApi, ShippingMethod, PayMethod, OrderStatus } from '@/api/order'
import type { OrdersDTO } from '@/api/order';

const router = useRouter()
const loading = ref(false)
const showAddressDialog = ref(false)
const deliveryTime = ref(null)
const totalAmount = ref(0)

const props = defineProps<{
  products?: {
    id: number
    name: string
    price: number
    quantity: number
    image: string
  }[]
}>()

const orderItems = ref(props.products || [])

const form = reactive<OrdersDTO>({
  buyerId: JSON.parse(localStorage.getItem('user') || '{}').id,
  addressId: 0,
  shippingMethodId: ShippingMethod.IMMEDIATE,
  amount: 0,
  payMethod: PayMethod.WECHAT,
  orderRemark: '',
  orderTime: new Date(),
  checkoutTime: undefined,
  status: OrderStatus.PENDING_PAYMENT,
})

// 计算总金额
const calculateTotal = () => {
  totalAmount.value = orderItems.value.reduce((sum, item) => {
    return sum + item.price * item.quantity
  }, 0)
  form.amount = totalAmount.value
}

// 提交订单
const submitOrder = async () => {
  if (form.shippingMethodId === ShippingMethod.SCHEDULED && !deliveryTime.value) {
    ElMessage.warning('请选择配送时间')
    return
  }

  loading.value = true
  try {
    const response = await orderApi.createOrder({
      ...form,
      orderTime: form.orderTime.toISOString(),
      checkoutTime: form.checkoutTime?.toISOString(),
      amount: Number(totalAmount.value.toFixed(2)),
    })

    if (response.code === 1) {
      ElMessage.success('订单创建成功')
      router.push(`/order/detail/${response.data.id}`)
    } else {
      ElMessage.error(response.msg || '创建订单失败')
    }
  } catch (error) {
    console.error('Create order error:', error)
    ElMessage.error('创建订单失败，请重试')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  calculateTotal()
})
</script>
