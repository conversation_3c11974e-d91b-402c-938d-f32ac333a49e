import axios from 'axios'
import type { AxiosInstance, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router'

const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API || import.meta.env.VITE_BASE_API,
  timeout: 15000, // 设置为15秒超时
  headers: {
    'Content-Type': 'application/json',
  },
})

request.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `${token}`
    }
    if (config.method === 'post' && config.data) {
      if (
        config.url?.includes('/system/login') &&
        (!config.data.username || !config.data.username.trim())
      ) {
        return Promise.reject(new Error('用户名不能为空'))
      }
    }
    console.log('Full request config:', {
      url: config.url,
      method: config.method,
      baseURL: config.baseURL,
      headers: config.headers,
      data: config.data,
    })
    return config
  },
  (error) => {
    console.error('Request error:', error)
    return Promise.reject(error)
  },
)

request.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response.data
    console.log('Response Data:', {
      status: response.status,
      statusText: response.statusText,
      data: res,
      headers: response.headers,
    })
    if (response.status === 401) {
      return Promise.reject(new Error('Unauthorized'))
    }
    if (res) {
      return res
    }
    return Promise.reject(new Error('Empty response'))
  },
  (error) => {
    console.error('Response Error:', error)
    return Promise.reject(error)
  },
)

export default request
