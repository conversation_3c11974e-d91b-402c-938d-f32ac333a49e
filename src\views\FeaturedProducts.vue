<template>
  <!-- 淡色主题，保持相同布局 -->
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 text-gray-800">
    <!-- 侧边导航栏 - 保持相同布局 -->
    <div class="fixed top-0 left-0 h-full w-16 md:w-64 bg-white shadow-md flex flex-col z-20">
      <!-- Logo区域 -->
      <div class="p-4 md:p-6 flex justify-center md:justify-start items-center">
        <div class="flex md:hidden">
          <span class="text-lg font-bold text-purple-600">SW</span>
        </div>
        <div class="hidden md:block">
          <span class="text-xl font-bold text-purple-600">ShareWharf</span>
        </div>
      </div>

      <!-- 导航链接 - 保持相同布局 -->
      <div class="flex-1 flex flex-col py-8 space-y-3">
        <a
          href="/"
          class="p-4 flex justify-center md:justify-start items-center text-gray-500 hover:text-purple-600"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
            />
          </svg>
          <span class="hidden md:block ml-3">首页</span>
        </a>

        <a
          href="#"
          class="p-4 flex justify-center md:justify-start items-center bg-purple-50 text-purple-600 border-l-4 border-purple-600"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
            />
          </svg>
          <span class="hidden md:block ml-3">商品列表</span>
        </a>
      </div>

      <!-- 底部图标 -->
      <div class="p-4 flex justify-center md:justify-start">
        <button @click="goToHome" class="text-gray-500 hover:text-purple-600">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
            />
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- 主内容区域 - 保持相同布局 -->
    <div class="ml-16 md:ml-64 p-6 transition-all duration-300">
      <!-- 顶部栏 -->
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold">
          <span class="text-purple-600">精选</span>
          <span class="text-gray-800">好物</span>
        </h1>

        <div class="flex items-center space-x-4">
          <!-- 搜索框 -->
          <div class="relative">
            <input
              type="text"
              v-model="searchQuery"
              placeholder="搜索商品..."
              class="w-48 lg:w-64 bg-white border border-gray-200 rounded-lg py-2 px-4 text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>

          <!-- 购物车图标 -->
          <div class="relative">
            <button class="p-2 rounded-full bg-white shadow-sm hover:shadow-md transition-all">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-gray-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                />
              </svg>
              <span
                v-if="cartCount > 0"
                class="absolute -top-1 -right-1 bg-purple-600 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full"
              >
                {{ cartCount }}
              </span>
            </button>
          </div>
        </div>
      </div>

      <!-- 筛选标签 -->
      <div class="flex flex-wrap gap-2 mb-6">
        <span
          class="px-4 py-2 rounded-full text-sm bg-white text-purple-600 border border-purple-200 shadow-sm"
        >
          <span class="mr-2">✨</span> 精选商品
        </span>
        <span
          class="px-4 py-2 rounded-full text-sm bg-white text-blue-600 border border-blue-200 shadow-sm"
        >
          <span class="mr-2">🎯</span> 品质保证
        </span>
        <span
          class="px-4 py-2 rounded-full text-sm bg-white text-green-600 border border-green-200 shadow-sm"
        >
          <span class="mr-2">🌟</span> 严选优品
        </span>
      </div>

      <!-- 排序控件 -->
      <div class="mb-8 flex justify-end">
        <div class="relative">
          <select
            v-model="sortOption"
            class="appearance-none px-4 py-2 bg-white border border-gray-200 rounded-lg text-gray-700 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent shadow-sm"
          >
            <option value="newest">最新上架</option>
            <option value="priceAsc">价格从低到高</option>
            <option value="priceDesc">价格从高到低</option>
          </select>
          <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg
              class="h-4 w-4 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </div>
      </div>

      <!-- 商品网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <div
          v-for="product in filteredProducts"
          :key="product.id"
          class="group bg-white rounded-xl shadow-sm hover:shadow-md overflow-hidden flex flex-col hover:transform hover:scale-105 transition-all duration-300"
          @click="viewProduct(product.id)"
        >
          <!-- 商品图片 -->
          <div class="relative aspect-[4/3] overflow-hidden">
            <!-- 悬停叠加层 -->
            <div
              class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 flex flex-col justify-end"
            >
              <div class="p-4 flex justify-between items-center">
                <button
                  class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-lg transition-colors"
                >
                  查看详情
                </button>
                <button
                  @click.stop="toggleFavorite(product.id)"
                  class="p-2 bg-white/90 rounded-full hover:bg-purple-100 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    :class="{
                      'text-red-500 fill-red-500': product.isFavorite,
                      'text-gray-600': !product.isFavorite,
                    }"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <!-- 商品标签 -->
            <div
              v-if="product.tag"
              class="absolute top-4 left-4 bg-purple-600 text-white px-4 py-1 text-xs font-medium rounded-full z-10 shadow-sm"
            >
              {{ product.tag }}
            </div>

            <!-- 商品图片 -->
            <img
              :src="product.pic || '/placeholder.svg'"
              :alt="product.name"
              class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
              :class="{ 'scale-95': isImageClicked && clickedImageId === product.id }"
              @click.stop="handleImageClick(product.id)"
            />
          </div>

          <!-- 商品信息 -->
          <div class="p-4 flex-grow flex flex-col">
            <h3 class="text-lg font-semibold text-gray-800 mb-2 truncate">{{ product.name }}</h3>
            <p class="text-sm text-gray-500 mb-4 line-clamp-2 flex-grow">
              {{ product.description }}
            </p>
            <div class="flex items-center justify-between">
              <span class="text-xl font-bold text-purple-600">¥{{ product.price.toFixed(2) }}</span>
              <span class="text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-full"
                >已售 {{ product.sales || 0 }}</span
              >
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="mt-12 flex justify-center">
        <div class="inline-flex bg-white rounded-lg shadow-sm overflow-hidden">
          <button
            v-for="page in totalPages"
            :key="page"
            :class="[
              'w-10 h-10 flex items-center justify-center text-sm font-medium transition-colors',
              currentPage === page
                ? 'bg-purple-600 text-white'
                : 'text-gray-700 hover:bg-purple-50',
            ]"
            @click="currentPage = page"
          >
            {{ page }}
          </button>
        </div>
      </div>

      <!-- 页脚 -->
      <footer class="mt-16 py-6 border-t border-gray-200 text-center text-gray-500 text-sm">
        <div class="mb-2">精选好物 · 品质保证 · 正品直供</div>
        <div>© 2025 ShareWharf商城. 保留所有权利</div>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import request from '@/utils/request'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

// 初始化数据
const router = useRouter()
const cartCount = ref(0)
const searchQuery = ref('')
const sortOption = ref('popular')
const currentPage = ref(1)
const pageSize = 12

// 图片点击状态
const isImageClicked = ref(false)
const clickedImageId = ref<number | null>(null)

const products = ref([])

// 获取全部商品列表
const getProducts = async () => {
  try {
    // 使用环境变量获取API基础URL
    const apiBaseUrl = import.meta.env.VITE_BASE_API

    // 直接使用分类ID为0获取全部商品
    const res = await request.get(`${apiBaseUrl}/products/category/0`, {
      params: {
        page: currentPage.value,
        pageSize: pageSize,
        sortBy: getSortBy(),
        sortOrder: getSortOrder(),
      },
    })
    if (res.data.code === 1) {
      products.value = Array.isArray(res.data.data)
        ? res.data.data.map((item) => ({
            ...item,
            isFavorite: false,
          }))
        : []
    } else {
      console.error('获取商品列表失败')
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
  }
}

// 获取排序字段
const getSortBy = () => {
  switch (sortOption.value) {
    case 'newest':
      return 'id'
    case 'priceAsc':
    case 'priceDesc':
      return 'price'
    case 'popular':
    default:
      return 'sales'
  }
}

// 获取排序方式
const getSortOrder = () => {
  switch (sortOption.value) {
    case 'priceAsc':
      return 'asc'
    case 'priceDesc':
    case 'newest':
    case 'popular':
    default:
      return 'desc'
  }
}

// 根据筛选条件过滤产品
const filteredProducts = computed(() => {
  let result = products.value

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(
      (p) => p.name.toLowerCase().includes(query) || p.description.toLowerCase().includes(query),
    )
  }

  return result
})

// 分页数据
const totalPages = computed(() => {
  return Math.ceil(filteredProducts.value.length / pageSize)
})

// 监听分页和排序变化，重新获取数据
watch([currentPage, sortOption], () => {
  getProducts()
})

// 图片点击效果
const handleImageClick = (productId) => {
  clickedImageId.value = productId
  isImageClicked.value = true

  // 300ms 后重置状态
  setTimeout(() => {
    isImageClicked.value = false
    clickedImageId.value = null
  }, 300)
}

// 方法
const viewProduct = (productId) => {
  router.push(`/product/${productId}`)
}

// 返回首页方法
const goToHome = () => {
  router.push('/')
}

const toggleFavorite = async (productId) => {
  const token = localStorage.getItem('token')
  if (!token) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  const product = products.value.find((p) => p.id === productId)
  if (product) {
    try {
      const res = await request.post('/favorites/toggle', { productId })
      if (res.data.code === 1) {
        product.isFavorite = !product.isFavorite
        ElMessage.success(product.isFavorite ? '收藏成功' : '取消收藏成功')
      } else {
        ElMessage.error('操作失败')
      }
    } catch (error) {
      console.error('收藏操作失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 初始化数据
onMounted(async () => {
  await getProducts()
})
</script>
