<template>
  <div class="min-h-screen flex items-center justify-center relative overflow-hidden">
    <!-- 主要内容 -->
    <div class="w-full max-w-md relative z-10 p-6">
      <!-- 玻璃态卡片 -->
      <div
        class="backdrop-blur-lg bg-white/40 rounded-3xl shadow-2xl p-8 space-y-8 border border-white/20"
      >
        <!-- Logo和标题 -->
        <div class="text-center space-y-4">
          <div class="relative w-20 h-20 mx-auto">
            <div
              class="absolute inset-0 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-2xl shadow-lg transform rotate-45"
            ></div>
            <div
              class="relative w-full h-full flex items-center justify-center text-white font-bold text-2xl -rotate-45"
            >
              SW
            </div>
          </div>
          <div>
            <h2
              class="text-3xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent"
            >
              {{ $t('register.title') }}
            </h2>
            <p class="mt-2 text-gray-600">{{ $t('register.subtitle') }}</p>
          </div>
        </div>

        <el-form ref="formRef" :model="form" :rules="rules" class="space-y-5">
          <!-- 账号名输入框 -->
          <el-form-item prop="accountName" class="form-item-hover mb-4">
            <el-input
              v-model="form.accountName"
              :placeholder="$t('register.accountNamePlaceholder')"
              prefix-icon="User"
              class="custom-input"
            />
          </el-form-item>

          <!-- 密码输入框 -->
          <el-form-item prop="password" class="form-item-hover mb-4">
            <el-input
              v-model="form.password"
              type="password"
              :placeholder="$t('register.passwordPlaceholder')"
              prefix-icon="Lock"
              show-password
              class="custom-input"
              @input="checkPasswordStrength"
            />
            <div class="mt-2 text-sm bg-white/60 rounded-lg p-3">
              <div class="flex items-center justify-between mb-2">
                <span class="text-gray-600">{{ $t('register.passwordStrength') }}:</span>
                <span :class="strengthColorClass">{{
                  $t(`register.strength.${strengthLevel}`)
                }}</span>
              </div>
              <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                <div
                  class="h-full transition-all duration-300"
                  :class="strengthBarColorClass"
                  :style="{ width: `${(passwordStrength / 4) * 100}%` }"
                ></div>
              </div>
              <ul class="mt-2 text-gray-500 space-y-1 text-xs">
                <li :class="{ 'text-green-600': hasMinLength }">
                  ✓ {{ $t('register.passwordRules.minLength') }}
                </li>
                <li :class="{ 'text-green-600': hasUpperCase }">
                  ✓ {{ $t('register.passwordRules.upperCase') }}
                </li>
                <li :class="{ 'text-green-600': hasLowerCase }">
                  ✓ {{ $t('register.passwordRules.lowerCase') }}
                </li>
                <li :class="{ 'text-green-600': hasNumber }">
                  ✓ {{ $t('register.passwordRules.number') }}
                </li>
              </ul>
            </div>
          </el-form-item>

          <!-- 确认密码输入框 -->
          <el-form-item prop="confirmPassword" class="form-item-hover mb-4">
            <el-input
              v-model="form.confirmPassword"
              type="password"
              :placeholder="$t('register.confirmPasswordPlaceholder')"
              prefix-icon="Lock"
              show-password
              class="custom-input"
            />
          </el-form-item>

          <!-- 性别选择 -->
          <el-form-item prop="gender" class="flex justify-center mb-4">
            <el-radio-group v-model="form.gender" class="custom-radio-group">
              <el-radio label="男" class="custom-radio">{{ $t('register.gender.male') }}</el-radio>
              <el-radio label="女" class="custom-radio">{{
                $t('register.gender.female')
              }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 手机号输入框 -->
          <el-form-item prop="phone" class="form-item-hover mb-4">
            <el-input
              v-model="form.phone"
              :placeholder="$t('register.phonePlaceholder')"
              prefix-icon="Phone"
              class="custom-input"
            />
          </el-form-item>

          <!-- 邮箱输入框 -->
          <el-form-item prop="email" class="form-item-hover mb-4">
            <el-input
              v-model="form.email"
              :placeholder="$t('register.emailPlaceholder')"
              prefix-icon="Message"
              class="custom-input"
            />
            <div class="mt-1 text-sm text-gray-600 bg-white/60 rounded-lg p-2">
              <el-icon class="align-middle mr-1"><InfoFilled /></el-icon>
              {{ $t('register.emailTip') }}
            </div>
          </el-form-item>

          <!-- 验证码输入框 -->
          <el-form-item prop="verificationCode" class="form-item-hover mb-4">
            <div class="flex gap-4">
              <el-input
                v-model="form.verificationCode"
                :placeholder="$t('register.verificationCodePlaceholder')"
                prefix-icon="Key"
                class="custom-input flex-1"
              />
              <button
                type="button"
                :disabled="isCountingDown || !form.email"
                @click="handleSendCode"
                class="verification-button min-w-[120px] h-[40px] px-4 rounded-lg text-sm"
              >
                {{
                  isCountingDown
                    ? $t('register.resendCode', { seconds: countdown })
                    : $t('register.sendCode')
                }}
              </button>
            </div>
            <div
              class="mt-1 text-sm bg-white/60 rounded-lg p-2"
              :class="sendingCode ? 'text-purple-600' : 'text-gray-600'"
            >
              <el-icon class="align-middle mr-1">
                <component :is="sendingCode ? 'Loading' : 'Message'" />
              </el-icon>
              {{ sendingCode ? $t('register.sendingCode') : $t('register.codeTip') }}
            </div>
            <div
              class="mt-1 text-sm text-amber-600 bg-amber-50/80 rounded-lg p-2 border border-amber-200"
            >
              <el-icon class="align-middle mr-1"><WarningFilled /></el-icon>
              {{ $t('register.checkSpamFolder') }}
            </div>
          </el-form-item>

          <!-- 邀请码输入框 -->
          <el-form-item prop="inviteCode" class="form-item-hover mb-4">
            <el-input
              v-model="form.inviteCode"
              :placeholder="$t('register.inviteCodePlaceholder')"
              prefix-icon="Ticket"
              class="custom-input"
            />
            <div class="mt-1 text-sm text-gray-600 bg-white/60 rounded-lg p-2">
              <el-icon class="align-middle mr-1"><InfoFilled /></el-icon>
              {{ $t('register.inviteCodeTip') }}
            </div>
          </el-form-item>

          <!-- 注册按钮 -->
          <el-form-item>
            <button
              type="button"
              :disabled="loading"
              @click="handleRegister"
              class="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
            >
              <div class="flex items-center justify-center">
                <LoaderIcon v-if="loading" class="w-5 h-5 mr-2 animate-spin" />
                <span>{{
                  loading ? $t('register.registering') : $t('register.registerButton')
                }}</span>
              </div>
            </button>
          </el-form-item>

          <!-- 登录链接 -->
          <div class="text-center mt-6">
            <p class="text-sm text-gray-600">
              {{ $t('register.haveAccount') }}
              <router-link to="/login" class="text-purple-600 hover:text-purple-500 font-medium">
                {{ $t('register.loginNow') }}
              </router-link>
            </p>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { buyerApi } from '@/api/buyer'
import type { AxiosError } from 'axios'
import request from '@/utils/request'
import { LoaderIcon } from 'lucide-vue-next'
import { InfoFilled, Loading, Message, Ticket, WarningFilled } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

interface ErrorResponse {
  code: number
  msg: string
}

interface RegisterForm {
  accountName: string
  password: string
  confirmPassword: string
  gender: string
  phone: string
  email: string
  verificationCode: string
  inviteCode: string
}

interface RegisterResponse {
  code: number
  msg: string
  data: null
}

interface ApiResponse<T> {
  code: number
  msg: string
  data: T
}

const router = useRouter()
const formRef = ref<FormInstance>()
const loading = ref(false)

const form = reactive<RegisterForm>({
  accountName: '',
  password: '',
  confirmPassword: '',
  gender: '',
  phone: '',
  email: '',
  verificationCode: '',
  inviteCode: '',
})

// 密码强度相关的响应式变量
const passwordStrength = ref(0)
const strengthText = ref('弱')
const strengthColorClass = ref('text-red-500')
const strengthBarColorClass = ref('bg-red-500')

// 密码验证状态
const hasMinLength = ref(false)
const hasUpperCase = ref(false)
const hasLowerCase = ref(false)
const hasNumber = ref(false)

// 检查密码强度
const checkPasswordStrength = () => {
  const password = form.password
  let strength = 0

  // 检查长度
  hasMinLength.value = password.length >= 8
  if (hasMinLength.value) strength++

  // 检查是否包含大写字母
  hasUpperCase.value = /[A-Z]/.test(password)
  if (hasUpperCase.value) strength++

  // 检查是否包含小写字母
  hasLowerCase.value = /[a-z]/.test(password)
  if (hasLowerCase.value) strength++

  // 检查是否包含数字
  hasNumber.value = /\d/.test(password)
  if (hasNumber.value) strength++

  // 更新密码强度
  passwordStrength.value = strength

  // 更新强度文本和颜色
  switch (strength) {
    case 0:
    case 1:
      strengthText.value = '弱'
      strengthColorClass.value = 'text-red-500'
      strengthBarColorClass.value = 'bg-red-500'
      break
    case 2:
    case 3:
      strengthText.value = '中'
      strengthColorClass.value = 'text-yellow-500'
      strengthBarColorClass.value = 'bg-yellow-500'
      break
    case 4:
      strengthText.value = '强'
      strengthColorClass.value = 'text-green-500'
      strengthBarColorClass.value = 'bg-green-500'
      break
  }
}

const countdown = ref(60)
const isCountingDown = ref(false)
const sendingCode = ref(false)
const countDownText = computed(() => {
  return isCountingDown.value ? `${countdown.value}秒后重试` : '发送验证码'
})

const captchaUrl = ref('')

// 开始倒计时
const startCountdown = () => {
  isCountingDown.value = true
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
      isCountingDown.value = false
    }
  }, 1000)
}

const rules = reactive<FormRules>({
  accountName: [
    { required: true, message: '请输入账号名', trigger: 'blur' },
    { min: 3, max: 20, message: '账号长度在3到20个字符之间', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 8, message: '密码长度至少为8个字符', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!/[A-Z]/.test(value)) {
          callback(new Error('密码必须包含至少一个大写字母'))
        } else if (!/[a-z]/.test(value)) {
          callback(new Error('密码必须包含至少一个小写字母'))
        } else if (!/\d/.test(value)) {
          callback(new Error('密码必须包含至少一个数字'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== form.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: ['blur', 'change'],
    },
  ],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
  verificationCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码长度应为6位', trigger: 'blur' },
  ],
  inviteCode: [{ max: 20, message: '邀请码长度不能超过20个字符', trigger: 'blur' }],
})

const handleRegister = async () => {
  if (!formRef.value) {
    ElMessage.warning('表单验证失败')
    return
  }

  try {
    await formRef.value.validate(async (valid) => {
      if (valid) {
        loading.value = true
        try {
          const registerData = {
            accountName: form.accountName,
            password: form.password,
            gender: form.gender,
            phone: form.phone,
            email: form.email,
            verificationCode: form.verificationCode,
            inviteCode: form.inviteCode,
          }

          const { data } = await buyerApi.register(registerData)
          const { code, msg } = data

          if (code === 1) {
            ElMessage.success(msg || '注册成功')
            router.push('/login')
          } else {
            ElMessage.error(msg || '注册失败，请重试')
          }
        } catch (error) {
          console.error('Register error:', error)
          const axiosError = error as AxiosError<ApiResponse<null>>
          ElMessage.error(axiosError.response?.data?.msg || '注册失败，请重试')
        } finally {
          loading.value = false
        }
      }
    })
  } catch (error) {
    console.error('Form validation error:', error)
    ElMessage.error('表单验证失败，请检查输入')
  }
}

// 发送验证码
const handleSendCode = async () => {
  if (!form.email) {
    ElMessage.warning('请先填写邮箱')
    return
  }

  // 验证邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(form.email)) {
    ElMessage.warning('请输入正确的邮箱格式')
    return
  }

  sendingCode.value = true
  try {
    const response = await request({
      method: 'post',
      url: '/buyer/register/sendCode',
      data: {
        address: form.email,
      },
    })

    if (response.data.code === 1) {
      startCountdown()
      ElMessage.success(response.data.msg || '验证码已发送')
    } else {
      ElMessage.error(response.data.msg || '发送验证码失败')
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    ElMessage.error('发送验证码失败，请重试')
  } finally {
    sendingCode.value = false
  }
}

// 密码强度相关
const strengthLevel = computed(() => {
  switch (passwordStrength.value) {
    case 0:
    case 1:
      return 'weak'
    case 2:
    case 3:
      return 'medium'
    case 4:
      return 'strong'
    default:
      return 'weak'
  }
})
</script>

<style scoped>
/* 输入框样式 */
.custom-input :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(4px);
  border-radius: 1rem;
  height: 48px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.custom-input :deep(.el-input__wrapper:hover) {
  border-color: rgba(147, 51, 234, 0.3);
  background: rgba(255, 255, 255, 0.8);
}

.custom-input :deep(.el-input__wrapper.is-focus) {
  border-color: rgb(147, 51, 234);
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 0 4px rgba(147, 51, 234, 0.1);
}

.custom-input :deep(.el-input__inner) {
  height: 48px;
  color: #1f2937;
}

.custom-input :deep(.el-input__inner::placeholder) {
  color: #9ca3af;
}

.custom-input :deep(.el-input__prefix-icon) {
  color: #9333ea;
  font-size: 1.2em;
}

/* 单选按钮样式 */
.custom-radio-group :deep(.el-radio) {
  margin-right: 20px;
  background: rgba(255, 255, 255, 0.6);
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.custom-radio-group :deep(.el-radio:hover) {
  background: rgba(255, 255, 255, 0.8);
}

.custom-radio-group :deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: #9333ea;
  border-color: #9333ea;
}

.custom-radio-group :deep(.el-radio__input.is-checked + .el-radio__label) {
  color: #9333ea;
}

/* 表单项悬浮效果 */
.form-item-hover {
  transition: transform 0.3s ease;
}

.form-item-hover:hover {
  transform: translateY(-2px);
}

/* 验证码按钮样式 */
.verification-button {
  background: linear-gradient(135deg, #9333ea 0%, #6929c4 100%);
  color: white;
  border: none;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.verification-button:disabled {
  background: rgba(229, 231, 235, 0.8) !important;
  color: #9ca3af;
  cursor: not-allowed;
}

.verification-button:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(147, 51, 234, 0.35);
}

/* 错误消息样式 */
:deep(.el-form-item__error) {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 8px;
  border-radius: 4px;
}
</style>
