<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo-container">
          <div class="logo-circle"></div>
          <div class="logo-square"></div>
          <div class="logo-triangle"></div>
        </div>
        <h1>管理员登录</h1>
        <p class="subtitle">欢迎回来！请输入您的账号信息</p>
      </div>

      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group" :class="{ 'error': accountNameError }">
          <label for="accountName">账号</label>
          <div class="input-container">
            <input
              id="accountName"
              v-model="accountName"
              type="text"
              placeholder="请输入管理员账号"
              @focus="accountNameError = ''"
              :disabled="isLoading"
            />
            <div class="icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
            </div>
          </div>
          <span class="error-message" v-if="accountNameError">{{ accountNameError }}</span>
        </div>

        <div class="form-group" :class="{ 'error': passwordError }">
          <label for="password">密码</label>
          <div class="input-container">
            <input
              id="password"
              v-model="password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="请输入密码"
              @focus="passwordError = ''"
              :disabled="isLoading"
            />
            <div class="icon password-toggle" @click="togglePassword">
              <svg v-if="!showPassword" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle></svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path><line x1="1" y1="1" x2="23" y2="23"></line></svg>
            </div>
          </div>
          <span class="error-message" v-if="passwordError">{{ passwordError }}</span>
        </div>

        <div class="options-row">
          <label class="remember-me">
            <input type="checkbox" v-model="rememberMe" :disabled="isLoading" />
            <span class="checkmark"></span>
            记住我
          </label>
          <a href="#" class="forgot-password" @click.prevent="forgotPassword">忘记密码？</a>
        </div>

        <button type="submit" class="login-button" :disabled="isLoading">
          <span v-if="!isLoading">登录</span>
          <div v-else class="spinner"></div>
        </button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

// 表单数据
const accountName = ref('')
const password = ref('')
const rememberMe = ref(false)
const showPassword = ref(false)
const isLoading = ref(false)

// 错误信息
const accountNameError = ref('')
const passwordError = ref('')

const router = useRouter()

// 切换密码显示
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

// 登录处理
const handleLogin = async () => {
  // 表单验证
  if (!accountName.value.trim()) {
    accountNameError.value = '请输入管理员账号'
    return
  }

  if (!password.value) {
    passwordError.value = '请输入密码'
    return
  }

  try {
    isLoading.value = true

    // 调用后端登录接口，使用request工具而不是直接使用axios
    const response = await request.post('/manager/login', {
      accountName: accountName.value,
      password: password.value
    })

    // 由于使用了request工具，response已经是response.data
    const { code, msg, data } = response
    console.log(response)
    if (response.data.code === 1 && data) {
      // 登录成功，保存管理员token和信息
      localStorage.setItem('adminToken', data.token || 'admin-token')
      localStorage.setItem('adminInfo', JSON.stringify({
        accountName: accountName.value,
        role: 'admin',
        ...data
      }))

      ElMessage.success(response.data.msg || '登录成功')

      // 跳转到商户接收页面
      router.push('/***********')
    } else {
      // 登录失败，显示错误信息
      ElMessage.error(response.data.msg || '账号或密码错误')
    }
  } catch (error) {
    console.error('登录错误:', error)
    // 获取错误信息并根据错误类型提供更具体的提示
    let errorMsg = '登录失败，请稍后再试'

    if (error.response) {
      // 服务器响应了请求，但状态码不是2xx
      if (error.response.status === 500) {
        errorMsg = '服务器内部错误，请联系管理员'
      } else if (error.response.data && error.response.data.msg) {
        errorMsg = error.response.data.msg
      }
    } else if (error.request) {
      // 请求已发送但没有收到响应
      errorMsg = '无法连接到服务器，请检查网络连接'
    } else if (error.message) {
      // 请求设置触发的错误
      errorMsg = error.message
    }

    ElMessage.error(errorMsg)
  } finally {
    isLoading.value = false
  }
}

// 忘记密码
const forgotPassword = () => {
  ElMessage.info('请联系系统管理员重置密码')
}
</script>

<style scoped>
/* 基础样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.login-card {
  width: 100%;
  max-width: 450px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  padding: 40px;
  position: relative;
  z-index: 10;
  overflow: hidden;
}

/* 头部样式 */
.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
  height: 60px;
}

.logo-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  position: absolute;
  z-index: 1;
}

.logo-square {
  width: 35px;
  height: 35px;
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  position: absolute;
  transform: rotate(45deg);
  left: calc(50% - 15px);
  z-index: 2;
}

.logo-triangle {
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 35px solid #a8edea;
  position: absolute;
  z-index: 3;
}

.login-header h1 {
  font-size: 28px;
  color: #333;
  margin-bottom: 10px;
}

.subtitle {
  color: #666;
  font-size: 16px;
}

/* 表单样式 */
.login-form {
  margin-top: 30px;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 500;
}

.input-container {
  position: relative;
}

.input-container input {
  width: 100%;
  padding: 12px 40px 12px 15px;
  border: 2px solid #e1e1e1;
  border-radius: 10px;
  font-size: 16px;
  transition: all 0.3s;
}

.input-container input:focus {
  border-color: #4facfe;
  outline: none;
  box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

.input-container .icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  cursor: pointer;
}

.password-toggle:hover {
  color: #4facfe;
}

.error-message {
  color: #ff4d4f;
  font-size: 14px;
  margin-top: 5px;
}

.options-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  cursor: pointer;
}

.forgot-password {
  color: #4facfe;
  text-decoration: none;
  font-size: 14px;
}

.forgot-password:hover {
  text-decoration: underline;
}

.login-button {
  width: 100%;
  padding: 12px;
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  border: none;
  border-radius: 10px;
  color: white;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.login-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 7px 14px rgba(79, 172, 254, 0.2);
}

.login-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-card {
    padding: 30px 20px;
  }

  .login-header h1 {
    font-size: 24px;
  }

  .options-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}
</style>
