<template>
  <!-- 页面容器 -->
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 py-8">
    <!-- 消息提示框 -->
    <!-- <transition name="slide">
      <div
        v-if="showNewMessageAlert"
        class="new-message-alert"
        :style="{
          background: `linear-gradient(to right, ${themeColorLight}, ${themeColorLight}00)`,
          'border-left': `4px solid ${themeColor}`,
        }"
      >
        <span>您收到了一条新消息！</span>
        <button
          class="close-alert-btn"
          @click="closeNewMessageAlert"
          :style="{ color: themeColor }"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
    </transition> -->
    <!-- 装饰性背景元素 - 模糊的圆形背景 -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none z-0">
      <div
        class="absolute top-20 left-10 w-64 h-64 bg-purple-200 rounded-full opacity-20 blur-3xl"
      ></div>
      <div
        class="absolute bottom-20 right-10 w-80 h-80 bg-blue-200 rounded-full opacity-20 blur-3xl"
      ></div>
    </div>

    <!-- 头部导航栏 -->
<header class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8 relative z-10">
  <div class="flex items-center justify-between">
        <!-- 返回按钮 -->
        <button
          class="flex items-center gap-2 px-4 py-2 text-purple-600 hover:text-purple-700 bg-white/80 backdrop-blur-sm rounded-lg shadow-sm transition-all duration-200 hover:shadow-md group"
          @click="goBack"
        >
          <span class="text-lg transition-transform group-hover:-translate-x-1">←</span>
          <span class="font-medium">返回</span>
        </button>

        <!-- 标题区域 -->
        <div class="text-center">
          <h1
            class="text-3xl md:text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-2"
          >
            站内信
          </h1>
          <div class="text-sm text-gray-500 tracking-wider uppercase">系统与商家消息通知</div>
        </div>
<div class="flex items-center text-white bg-amber-500/90 dark:bg-amber-600/80 px-3 py-1.5 rounded-lg border border-amber-400 dark:border-amber-500 mr-[100px] tip">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      </svg>
      <span class="text-sm">可刷新以便获取最新消息</span>
    </div>

        <!-- 占位元素，保持布局平衡 -->
        <div class="w-[100px]"></div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-6 relative overflow-hidden">
        <!-- 背景装饰元素 -->
        <div
          class="absolute -top-24 -right-24 w-48 h-48 bg-purple-100 rounded-full opacity-50"
        ></div>
        <div
          class="absolute -bottom-24 -left-24 w-48 h-48 bg-blue-100 rounded-full opacity-50"
        ></div>

        <div class="message-container-wrapper" :style="{ height: containerHeight }">
          <!-- 消息容器 -->
          <div class="message-container">
            <div class="content-wrapper">
              <!-- 左侧消息分类导航 -->
              <div class="sidebar">
                <div
                  v-for="tab in tabs"
                  :key="tab.type"
                  class="tab-item"
                  :class="{
                    active: activeTab === tab.type,
                    'has-unread': tab.unread > 0,
                  }"
                  @click="switchTab(tab.type)"
                >
                  <div class="tab-icon">
                    <component :is="tab.icon" />
                  </div>
                  <div class="tab-content">
                    <span class="tab-name">{{ tab.name }}</span>
                    <!-- 未读消息数量标记 -->
                    <span class="unread-badge" v-if="tab.unread > 0">{{ tab.unread }}</span>
                  </div>
                </div>
              </div>

              <!-- 右侧消息内容区域 -->
              <div class="message-content">
                <div class="batch-actions">
                  <!-- 新增全选按钮 -->
                  <button class="select-all-btn" @click="toggleSelectAll">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <polyline points="9 11 12 14 22 4"></polyline>
                      <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                    </svg>
                    {{ isAllSelected ? '取消全选' : '全选' }}
                  </button>
                  <button class="batch-delete-btn" @click="handleBatchDelete">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <polyline points="3 6 5 6 21 6"></polyline>
                      <path
                        d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"
                      ></path>
                      <line x1="10" y1="11" x2="10" y2="17"></line>
                      <line x1="14" y1="11" x2="14" y2="17"></line>
                    </svg>
                    批量删除
                  </button>
                  <button class="batch-mark-read-btn" @click="handleBatchMarkAsRead">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    标记选中为已读
                  </button>
                  <button class="mark-all-read-btn" @click="handleMarkAllAsRead">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    标记全部消息为已读
                  </button>
                </div>

                <!-- 加载状态 -->
                <div v-if="loading" class="loading-spinner">
                  <div class="spinner"></div>
                  <p>加载中...</p>
                </div>

                <!-- 空状态 -->
                <div v-else-if="filteredMessages.length === 0" class="empty-message">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="120"
                    height="120"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    opacity="0.6"
                  >
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                    <line x1="9" y1="10" x2="15" y2="10" />
                    <line x1="12" y1="7" x2="12" y2="13" />
                  </svg>
                  <p>暂无{{ activeTab }}</p>
                </div>

                <!-- 消息列表 -->
                <div v-else class="message-list">
                  <!-- 单条消息项 -->
                  <div
                    v-for="message in filteredMessages"
                    :key="message.msgID"
                    class="message-item"
                    :class="{ unread: !message.isRead }"
                  >
                    <input
                      class="message-checkbox"
                      type="checkbox"
                      :id="message.msgID"
                      :checked="selectedMessages.includes(message.msgID)"
                      @change="handelCheckboxChange(message)"
                    />
                    <label :for="message.msgID"></label>
                    <!-- 消息右上角红点 -->
                    <div class="unread-badge" v-if="!message.isRead"></div>
                    <!-- 消息主要内容 -->
                    <div class="message-main">
                      <div class="message-header">
                        <h3 class="message-title">{{ message.title }}</h3>
                        <span class="message-time">{{ message.sendTime }}</span>
                      </div>
                      <p class="message-preview">{{ message.content }}</p>
                      <span class="merchant-name">{{ message.senderName }}</span>
                      <!-- <span class="unread-badge" v-if="tab.unread > 0">{{ tab.unread }}</span> -->
                    </div>

                    <!-- 消息操作按钮 -->
                    <div class="message-actions">
                      <!-- 查看按钮 -->
                      <button class="action-btn view-btn" @click.stop="showDetail(message)">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                          <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                        查看
                      </button>

                      <!-- 删除按钮 -->
                      <button class="action-btn delete-btn" @click.stop="confirmDelete(message)">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <polyline points="3 6 5 6 21 6"></polyline>
                          <path
                            d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"
                          ></path>
                          <line x1="10" y1="11" x2="10" y2="17"></line>
                          <line x1="14" y1="11" x2="14" y2="17"></line>
                        </svg>
                        删除
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <footer
      class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 text-center text-gray-500 text-sm relative z-10"
    >
      <div class="mb-2">站内信系统 · 及时通知 · 重要提醒</div>
      <div>© 2025 商城. 保留所有权利</div>
    </footer>

    <!-- 消息详情弹窗 -->
    <div v-if="showModal" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3 class="modal-title">{{ currentMessage?.title }}</h3>
          <!-- 关闭按钮 -->
          <button class="modal-close" @click="closeModal">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="modal-body">
          <!-- 消息元信息 -->
          <div class="message-meta">
            <span class="message-type" :class="currentMessage?.type">
              {{ currentMessage?.type }}
            </span>
            <span class="message-date">{{ currentMessage?.sendTime }}</span>
            <span v-if="currentMessage?.senderName" class="message-merchant">
              来自：{{ currentMessage?.senderName }}
            </span>
          </div>
          <!-- 消息内容 -->
          <div class="message-detail">
            {{ currentMessage?.content }}
          </div>
        </div>
        <div class="modal-footer">
          <button class="modal-button" @click="closeModal">关闭</button>
        </div>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteConfirm" class="confirm-overlay">
      <div class="confirm-container">
        <div class="confirm-header">
          <h3>确认删除</h3>
        </div>
        <div class="confirm-body">
          <p>确定要删除这条消息吗？删除后无法恢复。</p>
        </div>
        <div class="confirm-footer">
          <button class="confirm-button cancel" @click="cancelDelete">取消</button>
          <button class="confirm-button confirm" @click="handelDeleteMessage">确定</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 导入Vue相关功能
import {
  batchDeleteMessages,
  batchMarkAsRead,
  deleteMessage,
  getMessageDetail,
  getMessageList,
  getUnreadCount,
  markAllAsRead,
  markAsRead,
} from '@/api/messageList'
import { ElMessage } from 'element-plus'
import { throttle } from 'lodash-es'
import { Bell, Store } from 'lucide-vue-next'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
// 获取路由实例
const router = useRouter()

// 消息类型
type MessageType = '系统公告' | '用户消息'

// 消息接口
interface Message {
  msgID: string
  msgType: string
  title: string
  content: string
  sendTime: Date | string
  isRead: boolean
  type: MessageType
  senderName?: string
}

// 标签页接口
interface Tab {
  type: MessageType
  name: string
  icon: string
  unread: number
}

// 当前激活的标签页
const activeTab = ref<MessageType>('用户消息')
// 加载状态
const loading = ref(false)
// 所有消息列表
const messageList = ref<Message[]>([])
// 当前页面消息列表总消息数量
const currentMessageListLength = ref(0)

// 总消息数量
const totalCount = ref(0)
// 是否显示详情弹窗
const showModal = ref(false)
// 当前查看的消息
const currentMessage = ref<Message>()
// 是否显示删除确认弹窗
const showDeleteConfirm = ref(false)
// 待删除的消息ID
const messageToDelete = ref<number | null>(null)
const unreadCount = ref(0)
// 默认轮询时间间隔
const pollingTime = ref(5 * 1000)
// 长时间未操作轮询时间间隔
const longPollingTime = ref(10 * 1000)
// 获取用户信息
const userInfo = JSON.parse(localStorage.getItem('user') as string)
const showNewMessageAlert = ref(false)
// 记录用户没有操作的时间
const stopOperationTime = ref(0)
// 记录用户开始操作的时间
const startOperationTime = ref(0)
const pollingTimer = ref()
const selectedMessages = ref<string[]>([]) // 存储选中的消息ID
// 标签页数据
const tabs = ref<Tab[]>([
  {
    type: '用户消息',
    name: '用户消息',
    icon: Store,
    unread: 0,
  },
  {
    type: '系统公告',
    name: '系统公告',
    icon: Bell,
    unread: 0,
  },
])



const themeColor = 'rgb(107 70 193)'
const themeColorLight = 'rgb(107 70 193 / 0.1)'

const containerHeight = computed(() => {
  // 基础高度（操作栏等固定元素）
  const baseHeight = 200 // 约12.5rem

  // 每条消息固定高度为7rem (112px)
  const itemHeight = 112

  // 计算消息列表总高度
  const messagesHeight = filteredMessages.value.length * itemHeight

  console.log('动态高度：', messagesHeight)
  console.log('消息数量：', filteredMessages.value.length, '条')
  // 总高度 = 基础高度 + 消息列表高度
  // 确保最小高度足够显示操作栏和空状态
  return `${Math.max(baseHeight + messagesHeight, 500)}px`
})
// 计算属性：判断是否已全选
const isAllSelected = computed(() => {
  return (
    selectedMessages.value.length === filteredMessages.value.length &&
    filteredMessages.value.length > 0
  )
})

// 全选/取消全选方法
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedMessages.value = []
  } else {
    selectedMessages.value = filteredMessages.value.map((msg) => msg.msgID)
  }
}
// 获取未读消息数量
const loadUnreadCount = async () => {
  try {
    const res = await getUnreadCount({ type: 'user', id: userInfo.id })
    if (res.data.code === 1) {
      unreadCount.value = res.data || 0
      // ElMessage.success(`获取未读消息数量成功,`)
      console.log('当前未读消息数量：', unreadCount.value)
      // 计算每个标签页的未读消息数量
      tabs.value.forEach((tab) => {
        tab.unread = messageList.value.filter(
          (message) => message.msgType === tab.type && !message.isRead,
        ).length
      })
    }
  } catch (error) {
    console.error('获取未读消息数量失败:', error)
  }
}

const initMessageList = async (enableLoading: boolean = false) => {

    const res = await getMessageList({ type: 'user', id: userInfo.id })
    if (res.data.code === 1) {
      // 获取消息列表
      messageList.value = res.data.data.list || []
      currentMessageListLength.value = res.data.data.length
      console.log('消息列表返回数据：', res)
      // ElMessage.success('获取消息列表成功')
      console.log('当前消息列表：', messageList.value)
      loadUnreadCount()
      selectedMessages.value = []
    } else {
      ElMessage.error('获取消息列表失败')
    }
}

// 多选框选中项发生变化时触发
const handelCheckboxChange = (message: Message) => {
  const index = selectedMessages.value.indexOf(message.msgID)
  if (index > -1) {
    selectedMessages.value.splice(index, 1)
  } else {
    selectedMessages.value.push(message.msgID)
  }
  console.log('选中了', selectedMessages.value)
}
// 计算当前标签页过滤后的消息
const filteredMessages = computed(() => {
  // console.log("消息列表",messageList.value);
  // console.log("当前标签",activeTab.value);
  const msg = messageList.value.filter((msg) => msg.msgType === activeTab.value)
  console.log('当前标签消息', msg)
  return msg
})

const goBack = () => {
  router.go(-1)
}

const switchTab = (type: MessageType) => {
  console.log('当前标签：', activeTab.value)
  // 切换标签页时重置选择的消息
  selectedMessages.value = []
  activeTab.value = type
}
// 开始轮询
const startPolling = () => {
  if (pollingTimer.value) clearTimeout(pollingTimer.value)
  // recordUserActivity()
  if ((stopOperationTime.value - startOperationTime.value) / 1000 >= 600) {
    pollingTime.value = longPollingTime.value

  }
  pollingTimer.value = setTimeout(() => {
    stopOperationTime.value = Date.now()
    initMessageList()
    startPolling()
  }, pollingTime.value)
}

const resetStopOperationTime = throttle(() => {
  startOperationTime.value = Date.now()
  if (pollingTime.value == longPollingTime.value) {
    pollingTime.value = 5 * 1000
    console.log('重置轮询时间')
    clearTimeout(pollingTimer.value)
    initMessageList()
    startPolling()
  }
}, 2000)

const closeNewMessageAlert = () => {
  showNewMessageAlert.value = false
}

const showDetail = async (message: Message) => {
  try {
    const res = await getMessageDetail(message.msgID, { type: 'user', id: userInfo.id })
    console.log('当前消息', message)
    markMessageAsRead(message)
    // console.log('返回值：',res.data.data);
    currentMessage.value = res.data.data
    // console.log('当前选择的消息',res.data.data);
    showModal.value = true
  } catch (error) {
    ElMessage.error('获取消息详情失败')
  }
}

const markMessageAsRead = async (message: Message) => {
  try {
    await markAsRead(message.msgID, { type: 'user', id: userInfo.id })
    console.log('标记消息已读成功')
    initMessageList()
  } catch (error) {
    ElMessage.error('标记消息已读失败')
  }
}

const handleBatchMarkAsRead = async () => {
  try {
    await batchMarkAsRead({ msgIDs: selectedMessages.value, type: 'user', id: userInfo.id })
    ElMessage.success('批量标记已读成功')
    initMessageList()
  } catch (error) {
    ElMessage.error('批量标记已读失败')
  }
}
// 标记全部消息为已读
const handleMarkAllAsRead = async () => {
  try {
    await markAllAsRead({ type: 'user', id: userInfo.id })
    ElMessage.success('标记全部消息已读成功')
    initMessageList()
  } catch (error) {
    ElMessage.error('标记全部消息已读失败')
  }
}
const closeModal = () => {
  showModal.value = false
  currentMessage.value = {
    msgID: '0',
    msgType: '',
    title: '',
    content: '',
    sendTime: '',
    isRead: false,
    type: '用户消息',
    senderName: '',
  }
}
// 打开确认删除消息弹窗
const confirmDelete = async (message: Message) => {
  currentMessage.value = message
  showDeleteConfirm.value = true
}

const cancelDelete = () => {
  showDeleteConfirm.value = false
  messageToDelete.value = null
}

const handelDeleteMessage = async () => {
  console.log(currentMessage.value)
  if (!currentMessage.value) {
    ElMessage.error('当前没有选中要删除的消息')
    return
  }
  try {
    loading.value = true
    await deleteMessage(currentMessage.value.msgID, { type: 'user', id: userInfo.id })
    loading.value = false
    // 更新未读计数
    updateUnreadCounts()
    // 关闭确认弹窗
    showDeleteConfirm.value = false
    messageToDelete.value = null
    initMessageList()
  } catch (error) {
    loading.value = false
    ElMessage.error('删除消息失败')
  }
}
// 批量删除
const handleBatchDelete = async () => {
  if (!selectedMessages.value.length) {
    ElMessage.error('请选择要删除的消息')
    return
  }
  try {
    loading.value = true
    await batchDeleteMessages({
      msgIDs: selectedMessages.value,
      type: 'user',
      id: userInfo.id,
    })
    loading.value = false
    // // 更新未读计数
    // updateUnreadCounts()
    // // 关闭确认弹窗
    // showDeleteConfirm.value = false
    // messageToDelete.value = null
    initMessageList()
  } catch (error) {
    loading.value = false
    ElMessage.error('删除消息失败')
  }
}
// 更新未读消息数量
const updateUnreadCounts = () => {
  tabs.value.forEach((tab) => {
    tab.unread = messageList.value.filter((msg) => msg.msgType === tab.type && !msg.isRead).length
  })
}

watch(
  currentMessageListLength,
  (newValue, oldValue) => {
    showNewMessageAlert.value = true
  },
  { immediate: false },
)

// 组件挂载时获取消息数据
onMounted(() => {
  // 初始化消息列表
  initMessageList()
  startPolling()
  startOperationTime.value=Date.now()
  window.addEventListener('mousedown',resetStopOperationTime)
  window.addEventListener('mousemove',resetStopOperationTime)
  window.addEventListener('keydown',resetStopOperationTime)
})
onUnmounted(() => {
  window.removeEventListener('mousedown',resetStopOperationTime)
  window.removeEventListener('mousemove',resetStopOperationTime)
  window.removeEventListener('keydown',resetStopOperationTime)
  if(pollingTimer.value){
    clearTimeout(pollingTimer.value)
  }
})
</script>
<style scoped>
.message-container-wrapper {
  overflow: hidden; /* 确保内容不会溢出 */
  will-change: height; /* 性能优化 */
  transition: height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}
.message-container {
  display: flex;
  flex-direction: column;
  height: auto; /* 内容高度自动扩展 */
}
/* 滑动动画 */
.slide-enter-active {
  transition: all 0.3s ease-out;
}
.slide-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-enter-from,
.slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

/* 提示框样式 */
.new-message-alert {
  position: fixed;
  top: 30px;
  right: 30px;
  display: flex;
  align-items: center;
  padding: 14px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  backdrop-filter: blur(10px);
}
.new-message-alert span {
  font-size: 1.2rem;
  font-weight: 500;
  background: linear-gradient(#9333ea, #2563eb);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  display: inline-block;
}
.close-alert-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  transition: opacity 0.2s;
}
.close-alert-btn:hover {
  opacity: 1;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
}

.title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #212529;
}

.unread-count {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #6c757d;
}

.count-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 22px;
  height: 22px;
  margin-right: 6px;
  padding: 0 4px;
  background-color: #ff4d4f;
  color: white;
  border-radius: 11px;
  font-size: 12px;
  font-weight: 500;
}

.content-wrapper {
  display: flex;
  min-height: 500px; /* 最小高度保证 */
  overflow: hidden;
}

.sidebar {
  width: 220px;
  background-color: white;
  border-right: 1px solid #e9ecef;
  padding: 12px 0;
  overflow-y: auto;
  border-radius: 12px 0 0 12px;
}

.tab-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  margin: 0 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-item:hover {
  background-color: #f1f3f5;
}

.tab-item.active {
  background-color: v-bind('themeColorLight');
}

.tab-item.has-unread {
  position: relative;
}

.tab-item.has-unread::after {
  content: '';
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background-color: v-bind('themeColor');
  border-radius: 50%;
}

.tab-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 10px;
  color: #495057;
}

.tab-item.active .tab-icon {
  color: v-bind('themeColor');
}

.tab-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tab-name {
  font-size: 14px;
  font-weight: 500;
  color: #212529;
}

.tab-item.active .tab-name {
  color: v-bind('themeColor');
  font-weight: 500;
}
/* 
.unread-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 15px;
  height: 20px;
  padding: 0 4px;
  background-color: #ff4d4f;
  color: white;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 400;
} */

.message-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background-color: white;
  border-radius: 0 12px 12px 0;
}
/* 更新批量操作区域样式 */
.batch-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
  gap: 10px; /* 按钮间距 */
}
/* 全选按钮样式 */
.select-all-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: rgb(107 70 193 / 0.1);
  color: v-bind('themeColor');
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 10px;
}

.select-all-btn:hover {
  background-color: rgb(107 70 193 / 0.2);
}

.select-all-btn svg {
  width: 16px;
  height: 16px;
  stroke: currentColor;
}
.batch-delete-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: #fee2e2;
  color: #dc2626;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}
/* 批量标记为已读按钮样式 */
.batch-mark-read-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: rgb(107 70 193 / 0.1);
  color: v-bind('themeColor');
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.batch-mark-read-btn:hover:not(:disabled) {
  background-color: rgb(107 70 193 / 0.2);
}

.batch-mark-read-btn:disabled {
  background-color: #e9ecef;
  color: #adb5bd;
  cursor: not-allowed;
  opacity: 0.7;
}
.tip {
  position: absolute;
  right: -70px;
}
.batch-mark-read-btn svg {
  width: 16px;
  height: 16px;
  stroke: currentColor;
}
/* 标记全部为已读按钮样式 */
.mark-all-read-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: rgb(107 70 193 / 0.1);
  color: v-bind('themeColor');
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 10px; /* 与批量删除按钮保持间距 */
}

.mark-all-read-btn:hover {
  background-color: rgb(107 70 193 / 0.2);
}

.mark-all-read-btn:disabled {
  background-color: #e9ecef;
  color: #adb5bd;
  cursor: not-allowed;
}

.mark-all-read-btn svg {
  width: 16px;
  height: 16px;
  stroke: v-bind('themeColor');
}
.batch-delete-btn:hover {
  background-color: #fecaca;
}

.batch-delete-btn:disabled {
  background-color: #e9ecef;
  color: #adb5bd;
  cursor: not-allowed;
}

.batch-delete-btn svg {
  width: 16px;
  height: 16px;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: v-bind('themeColor');
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top-color: v-bind('themeColor');
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6c757d;
}

.empty-message svg {
  width: 100px;
  height: 100px;
  margin-bottom: 16px;
  color: #adb5bd;
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.message-item {
  position: relative;
  display: flex;
  height: 5.2rem;
  justify-content: space-between;
  padding: 14px 14px 14px 40px; /* 左侧增加内边距 */
  border-radius: 8px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}
.message-checkbox {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

/* 自定义多选框样式 */
.message-checkbox + label {
  position: relative;
  cursor: pointer;
  padding-left: 28px;
  margin-right: 15px;
  align-self: center;
}

/* 未选中状态 */
.message-checkbox + label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  background: white;
  transition: all 0.2s ease;
}

/* 悬停状态 */
.message-checkbox:hover + label:before {
  border-color: v-bind('themeColor');
}

/* 选中状态 */
.message-checkbox:checked + label:before {
  background-color: v-bind('themeColor');
  border-color: v-bind('themeColor');
}

/* 选中后的勾号 */
.message-checkbox:checked + label:after {
  content: '';
  position: absolute;
  left: 5px;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
}
.message-item.unread .message-checkbox + label:before {
  border-color: v-bind('themeColor');
}
.unread-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #ff4d4f;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 0 0 2px white;
}

/* 如果未读数量较多（>9），显示为9+ */
.unread-badge::after {
  content: '';
  display: none;
}

.message-item:hover {
  background-color: #f1f3f5;
  border-color: #dee2e6;
}

.message-item.unread {
  background-color: v-bind('themeColorLight');
  border-color: rgb(107 70 193 / 0.3);
}

.message-main {
  flex: 1;
  margin-left: 10px;
  cursor: pointer;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.message-title {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: #212529;
}

.message-item.unread .message-title {
  color: v-bind('themeColor');
}

.message-time {
  font-size: 12px;
  color: #6c757d;
}

.message-preview {
  margin: 6px 0;
  font-size: 14px;
  color: #495057;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-footer {
  margin-top: 8px;
}

.merchant-name {
  display: inline-block;
  padding: 3px 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  font-size: 12px;
  color: #495057;
}

.message-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
  margin-left: 12px;
  padding-left: 12px;
  border-left: 1px solid #e9ecef;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 13px;
  transition: all 0.2s;
}

.action-btn svg {
  width: 14px;
  height: 14px;
}

.view-btn {
  background-color: #e9ecef;
  color: #495057;
}

.view-btn:hover {
  background-color: #dee2e6;
}

.delete-btn {
  background-color: #fee2e2;
  color: #dc2626;
}

.delete-btn:hover {
  background-color: #fecaca;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  width: 90%;
  max-width: 600px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #212529;
}

.modal-close {
  background: none;
  border: none;
  cursor: pointer;
  color: #6c757d;
  padding: 4px;
  transition: all 0.2s;
}

.modal-close:hover {
  color: #495057;
  transform: rotate(90deg);
}

.modal-body {
  padding: 20px;
}

.message-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #6c757d;
}

.message-type {
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.message-type.merchant {
  background-color: #e0e7ff;
  color: #4f46e5;
}

.message-type.system {
  background-color: #d1fae5;
  color: #059669;
}

.message-content {
  line-height: 1.6;
  font-size: 15px;
  color: #495057;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
}

.modal-button {
  padding: 8px 16px;
  background-color: v-bind('themeColor');
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.modal-button:hover {
  opacity: 0.9;
}

/* 删除确认弹窗 */
.confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.confirm-container {
  width: 90%;
  max-width: 400px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  animation: modalFadeIn 0.3s ease-out;
}

.confirm-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
}

.confirm-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #212529;
}

.confirm-body {
  padding: 20px;
  font-size: 15px;
  color: #495057;
}

.confirm-footer {
  padding: 16px 20px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.confirm-button {
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.confirm-button.cancel {
  background-color: #e9ecef;
  color: #495057;
}

.confirm-button.cancel:hover {
  background-color: #dee2e6;
}

.confirm-button.confirm {
  background-color: #ef4444;
  color: white;
}

.confirm-button.confirm:hover {
  background-color: #dc2626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-wrapper {
    flex-direction: column;
    height: auto;
  }

  .sidebar {
    width: 100%;
    display: flex;
    padding: 8px 0;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
    overflow-x: auto;
    overflow-y: hidden;
  }

  .message-content {
    border-radius: 0 0 12px 12px;
  }

  .tab-item {
    flex: 1;
    min-width: 120px;
    flex-direction: column;
    padding: 8px;
    margin: 0 4px;
    text-align: center;
  }

  .tab-icon {
    margin-right: 0;
    margin-bottom: 6px;
  }

  .tab-content {
    flex-direction: column;
  }

  .unread-badge {
    margin-top: 4px;
  }

  .tab-item.has-unread::after {
    right: 8px;
    top: 8px;
    transform: none;
  }

  .message-item {
    flex-direction: column;
  }

  .message-actions {
    flex-direction: row;
    margin-left: 0;
    padding-left: 0;
    border-left: none;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e9ecef;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 12px 16px;
  }

  .title {
    font-size: 16px;
  }

  .count-badge {
    min-width: 20px;
    height: 20px;
    font-size: 11px;
  }

  .message-item {
    padding: 12px;
  }

  .message-title {
    font-size: 14px;
  }

  .message-preview {
    font-size: 13px;
  }

  .modal-container,
  .confirm-container {
    width: 95%;
  }
  /* 滑动动画 */
  .slide-fade-enter-active {
    transition: all 0.3s ease-out;
  }
  .slide-fade-leave-active {
    transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
  }
  .slide-fade-enter-from,
  .slide-fade-leave-to {
    transform: translateX(100%);
    opacity: 0;
  }
}
</style>
