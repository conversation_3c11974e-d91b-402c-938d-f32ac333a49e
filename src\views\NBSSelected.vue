<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 py-8">
    <!-- 头部导航 -->
    <header class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
      <div class="flex items-center justify-between">
        <button
          class="flex items-center gap-2 px-4 py-2 text-purple-600 hover:text-purple-700 bg-white/80 backdrop-blur-sm rounded-lg shadow-sm transition-all duration-200 hover:shadow-md"
          @click="goToHome"
        >
          <span class="text-lg">←</span>
          <span class="font-medium">返回首页</span>
        </button>
        <h1 class="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
          ShareWharf商城精选商品
        </h1>
        <div class="w-[100px]"></div> <!-- 用于平衡布局的占位div -->
      </div>
    </header>

    <!-- 排序选项和商品列表容器 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-6 mb-8">
        <div class="mb-8 text-center">
          <h2 class="text-xl font-semibold text-gray-800 mb-2">精选好物</h2>
          <p class="text-gray-600 text-sm">为您精心挑选的优质商品</p>
          <div class="w-20 h-1 bg-gradient-to-r from-purple-500 to-blue-500 mx-auto mt-4 rounded-full"></div>
        </div>

        <div class="flex justify-between items-center mb-6">
          <div class="flex gap-2">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
              <span class="mr-1">✨</span> 精选商品
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
              <span class="mr-1">🎯</span> 品质保证
            </span>
          </div>
          <select
            v-model="sortOption"
            class="px-4 py-2 bg-white border border-gray-200 rounded-lg text-gray-700 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
          >
            <option value="newest">按最新排序</option>
            <option value="priceAsc">价格从低到高</option>
            <option value="priceDesc">价格从高到低</option>
          </select>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <div
            v-for="product in filteredProducts"
            :key="product.id"
            class="group bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden flex flex-col"
            @click="viewProduct(product.id)"
          >
            <div class="relative aspect-[4/3] overflow-hidden">
              <img
                :src="product.pic || '/placeholder.svg'"
                :alt="product.name"
                class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
              />
              <div
                v-if="product.tag"
                class="absolute top-3 left-3 bg-purple-600 text-white px-3 py-1 text-xs rounded-full shadow-lg"
              >
                {{ product.tag }}
              </div>
              <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div class="absolute bottom-3 left-3 right-3 flex justify-between items-center">
                  <span class="text-white font-medium text-sm">查看详情</span>
                  <button
                    @click.stop="toggleFavorite(product.id)"
                    class="p-2 bg-white/90 backdrop-blur-sm rounded-full hover:bg-purple-600 hover:text-white transition-colors duration-200"
                  >
                    <i class="icon-heart" :class="{ 'text-red-500': product.isFavorite }">❤</i>
                  </button>
                </div>
              </div>
            </div>
            <div class="p-4 flex-grow flex flex-col">
              <h3 class="text-lg font-medium text-gray-800 mb-2 line-clamp-1">{{ product.name }}</h3>
              <p class="text-sm text-gray-500 mb-3 line-clamp-2 flex-grow">{{ product.description }}</p>
              <div class="flex items-center justify-between">
                <span class="text-lg font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">¥{{ product.price.toFixed(2) }}</span>
                <span class="text-xs text-gray-500">已售 {{ product.sales || 0 }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination">
          <button
            v-for="page in totalPages"
            :key="page"
            :class="['page-btn', currentPage === page ? 'active' : '']"
            @click="currentPage = page"
          >
            {{ page }}
          </button>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <button
          v-for="page in totalPages"
          :key="page"
          :class="['page-btn', currentPage === page ? 'active' : '']"
          @click="currentPage = page"
        >
          {{ page }}
        </button>
      </div>
    </div>

    <!-- 底部 -->
    <footer class="footer">
      <div class="copyright">© 2025 商城. 保留所有权利</div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import request from '@/utils/request'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

// 初始化数据
const router = useRouter()
const cartCount = ref(0)
const searchQuery = ref('')
const sortOption = ref('popular')
const currentPage = ref(1)
const pageSize = 12

// 图片点击状态
const isImageClicked = ref(false)
const clickedImageId = ref<number | null>(null)

const products = ref([])

// 获取全部商品列表
const getProducts = async () => {
  try {
    // 使用环境变量获取API基础URL
    const apiBaseUrl = import.meta.env.VITE_BASE_API

    // 直接使用分类ID为0获取全部商品
    const res = await request.get(`${apiBaseUrl}/products/category/0`, {
      params: {
        page: currentPage.value,
        pageSize: pageSize,
        sortBy: getSortBy(),
        sortOrder: getSortOrder(),
      },
    })
    if (res.data.code === 1) {
      products.value = Array.isArray(res.data.data)
        ? res.data.data.map((item) => ({
            ...item,
            isFavorite: false,
          }))
        : []
    } else {
      console.error('获取商品列表失败')
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
  }
}

// 获取排序字段
const getSortBy = () => {
  switch (sortOption.value) {
    case 'newest':
      return 'id'
    case 'priceAsc':
    case 'priceDesc':
      return 'price'
    case 'popular':
    default:
      return 'sales'
  }
}

// 获取排序方式
const getSortOrder = () => {
  switch (sortOption.value) {
    case 'priceAsc':
      return 'asc'
    case 'priceDesc':
    case 'newest':
    case 'popular':
    default:
      return 'desc'
  }
}

// 根据筛选条件过滤产品
const filteredProducts = computed(() => {
  let result = products.value

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(
      (p) => p.name.toLowerCase().includes(query) || p.description.toLowerCase().includes(query),
    )
  }

  return result
})

// 分页数据
const totalPages = computed(() => {
  return Math.ceil(filteredProducts.value.length / pageSize)
})

// 监听分页和排序变化，重新获取数据
watch([currentPage, sortOption], () => {
  getProducts()
})

// 图片点击效果
const handleImageClick = (productId) => {
  clickedImageId.value = productId
  isImageClicked.value = true

  // 300ms 后重置状态
  setTimeout(() => {
    isImageClicked.value = false
    clickedImageId.value = null
  }, 300)
}

// 方法
const viewProduct = (productId) => {
  router.push(`/product/${productId}`)
}

// 返回首页方法
const goToHome = () => {
  router.push('/')
}

const toggleFavorite = async (productId) => {
  const token = localStorage.getItem('token')
  if (!token) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  const product = products.value.find((p) => p.id === productId)
  if (product) {
    try {
      const res = await request.post('/favorites/toggle', { productId })
      if (res.data.code === 1) {
        product.isFavorite = !product.isFavorite
        ElMessage.success(product.isFavorite ? '收藏成功' : '取消收藏成功')
      } else {
        ElMessage.error('操作失败')
      }
    } catch (error) {
      console.error('收藏操作失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 初始化数据
onMounted(async () => {
  await getProducts()
})
</script>

<style scoped>
.products-container {
  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f8f9fa;
  color: #333;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: #f0e6ff;
  color: #8a2be2;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.back-btn:hover {
  background-color: #e6d9ff;
}

.icon-back {
  font-size: 16px;
}

.logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
}

.header-title {
  font-size: 24px;
  font-weight: 600;
  color: #8a2be2;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 8px;
  padding: 8px 16px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.search-box input {
  background: none;
  border: none;
  outline: none;
  width: 200px;
  font-size: 14px;
}

.search-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.user-actions {
  display: flex;
  gap: 15px;
}

.cart-btn,
.user-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 20px;
  position: relative;
}

.cart-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #8a2be2;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filters {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 20px;
  background-color: white;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.sort-options select {
  padding: 6px 12px;
  border-radius: 6px;
  border: 1px solid #ddd;
  font-size: 14px;
  outline: none;
}

.products-wrapper {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  padding: 20px;
}

.section-title {
  font-size: 20px;
  margin-bottom: 20px;
  color: #333;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.product-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  transition:
    transform 0.2s,
    box-shadow 0.2s;
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-image {
  height: 200px;
  position: relative;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.quick-action {
  position: absolute;
  bottom: 10px;
  right: 10px;
  display: flex;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.2s;
}

.product-card:hover .quick-action {
  opacity: 1;
}

.add-to-cart-btn,
.favorite-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  background-color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.add-to-cart-btn {
  background-color: #8a2be2;
  color: white;
  font-size: 18px;
}

.icon-heart {
  color: #999;
  font-size: 16px;
}

.icon-heart.favorited {
  color: #ff4757;
}

.product-info {
  padding: 15px;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.product-price {
  font-size: 18px;
  font-weight: 700;
  color: #8a2be2;
}

.product-sales {
  font-size: 12px;
  color: #999;
}

/* 添加操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
}

.view-details-btn,
.add-cart-btn {
  flex: 1;
  padding: 8px 0;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.view-details-btn {
  background-color: #f0e6ff;
  color: #8a2be2;
  border: 1px solid #d9c2ff;
}

.view-details-btn:hover {
  background-color: #e6d9ff;
}

.add-cart-btn {
  background-color: #8a2be2;
  color: white;
}

.add-cart-btn:hover {
  background-color: #7928ca;
}

.pagination {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 30px;
}

.page-btn {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.page-btn:hover {
  border-color: #8a2be2;
  color: #8a2be2;
}

.page-btn.active {
  background-color: #8a2be2;
  color: white;
  border-color: #8a2be2;
}

.footer {
  margin-top: 40px;
  padding: 20px 0;
  text-align: center;
  color: #666;
  font-size: 14px;
}
</style>
