import request from '@/utils/request'
import type { ApiResponse } from './types'

interface CategoryVO {
  id: number
  name: string
  parentId: number
  level: number
  sort: number
  status: 0 | 1
  createTime: string
}

export const categoryApi = {
  // 获取分类列表
  getCategoryList() {
    return request.get<ApiResponse<CategoryVO[]>>('/category/list')
  },

  // 获取分类详情
  getCategoryDetail(id: number) {
    return request.get<ApiResponse<CategoryVO>>(`/category/${id}`)
  }
}

export type {
  CategoryVO
}
