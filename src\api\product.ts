import request from '@/utils/request'
import type { ApiResponse } from './types'
import type { ProductPageQueryDTO, ProductVO, PageResult } from './types'
import { ElMessage } from 'element-plus'

// 产品错误码枚举
export enum ProductErrorCode {
  PRODUCT_NOT_EXIST = 3001,
  PRODUCT_NAME_EXIST = 3002,
  NO_PERMISSION = 3003,
}

// 产品状态枚举
export enum ProductStatus {
  DISABLED = 0,
  ENABLED = 1,
}

// 处理产品相关错误
const handleProductError = (code: number): string => {
  switch (code) {
    case ProductErrorCode.PRODUCT_NOT_EXIST:
      return '产品不存在'
    case ProductErrorCode.PRODUCT_NAME_EXIST:
      return '产品名称已存在'
    case ProductErrorCode.NO_PERMISSION:
      return '没有操作权限'
    default:
      return '未知错误'
  }
}

export interface ProductDetailVO {
  id: number
  name: string
  description: string
  price: number
  image: string
  images?: string[]
  specifications?: string[]
  storeName: string
  // ... 其他商品详情字段
}

// 产品API封装
export const productApi = {
  // 获取产品分页列表
  async getProductPage(params: ProductPageQueryDTO): Promise<ApiResponse<PageResult<ProductVO>>> {
    try {
      const response = await request.get<ApiResponse<PageResult<ProductVO>>>('/products/page', {
        params,
      })
      if (response.data.code !== 1) {
        ElMessage.error(handleProductError(response.data.code))
      }
      return response.data
    } catch (error) {
      console.error('Get product page error:', error)
      throw error
    }
  },

  // 根据类别获取产品列表
  async getProductsByCategory(categoryId: number): Promise<ApiResponse<ProductVO[]>> {
    try {
      const response = await request.get<ApiResponse<ProductVO[]>>(`/products/category/${categoryId}`)
      if (response.data.code !== 1) {
        ElMessage.error(handleProductError(response.data.code))
      }
      return response.data
    } catch (error) {
      console.error('Get products by category error:', error)
      throw error
    }
  },

  // 获取产品详情
  async getProductDetail(id: number): Promise<ApiResponse<ProductDetailVO>> {
    return request.get(`/product/${id}`)
  },

  // 辅助方法：检查产品是否存在
  async checkProductExists(id: number): Promise<boolean> {
    try {
      const response = await this.getProductDetail(id)
      return response.code === 1
    } catch {
      return false
    }
  },
}
