import { productApi, ProductStatus } from '@/api/product'
import type { ProductVO } from '@/api/types'

// 获取产品列表
const getProducts = async () => {
  try {
    const response = await productApi.getProductPage({
      page: currentPage.value,
      pageSize: pageSize.value,
      productName: searchForm.productName,
      categoryId: searchForm.categoryId,
      productStatus: ProductStatus.ENABLED,
    })

    if (response.code === 1) {
      products.value = response.data.records
      total.value = response.data.total
    }
  } catch (error) {
    console.error('Get products error:', error)
    ElMessage.error('获取产品列表失败')
  }
}

// 获取产品详情
const getProductDetail = async (id: number) => {
  try {
    const response = await productApi.getProductDetail(id)
    if (response.code === 1) {
      productDetail.value = response.data
    }
  } catch (error) {
    console.error('Get product detail error:', error)
    ElMessage.error('获取产品详情失败')
  }
}

// 根据类别获取产品
const getProductsByCategory = async (categoryId: number) => {
  try {
    const response = await productApi.getProductsByCategory(categoryId)
    if (response.code === 1) {
      categoryProducts.value = response.data
    }
  } catch (error) {
    console.error('Get products by category error:', error)
    ElMessage.error('获取分类产品失败')
  }
}
