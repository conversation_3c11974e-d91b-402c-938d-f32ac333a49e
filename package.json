{"name": "hiram_vue3_12.12", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\"", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@stripe/stripe-js": "^7.3.1", "axios": "^1.7.9", "element-plus": "^2.9.0", "lucide-vue-next": "^0.468.0", "pinia": "^2.2.6", "qrcode": "^1.5.4", "underscore": "^1.13.7", "vue": "^3.5.13", "vue-i18n": "^11.0.0-rc.1", "vue-pdf-embed": "^2.1.3", "vue-router": "^4.4.5"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/jsdom": "^21.1.7", "@types/node": "^22.9.3", "@types/qrcode": "^1.5.5", "@vitejs/plugin-vue": "^5.2.1", "@vitest/eslint-plugin": "1.1.10", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.20", "eslint": "^9.14.0", "eslint-plugin-oxlint": "^0.11.0", "eslint-plugin-vue": "^9.30.0", "jsdom": "^25.0.1", "npm-run-all2": "^7.0.1", "oxlint": "^0.11.0", "postcss": "^8.4.49", "prettier": "^3.3.3", "tailwindcss": "^3.4.16", "typescript": "~5.6.3", "vite": "^6.0.1", "vite-plugin-vue-devtools": "^7.6.5", "vitest": "^2.1.5", "vue-tsc": "^2.1.10"}}