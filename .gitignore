# 操作系统生成的文件
.DS_Store
Thumbs.db

# 编辑器生成的文件
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 依赖文件和编译输出
node_modules/
dist/
build/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.tgz

# # 环境变量和配置文件
# .env
# .env.local
# .env.development
# .env.production
# .env.test

# 日志文件
logs/
*.log

# 测试相关文件
coverage/
*.lcov

# 其他常见文件
*.tmp
*.temp
.cache/
*.zip
*.tar.gz

# Vue CLI 生成的文件
.vuepress/dist/
.vuepress/cache/

# # Vite 生成的文件
# .vite/
# *.local

# TypeScript 生成的文件
*.tsbuildinfo

# IDE 配置文件
.idea/
*.iml
*.ipr
*.iws

# # 本地开发文件
# .local