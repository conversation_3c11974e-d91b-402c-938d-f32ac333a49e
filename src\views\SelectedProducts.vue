<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 py-8">
    <!-- 装饰性背景元素 -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none z-0">
      <div
        class="absolute top-20 left-10 w-64 h-64 bg-purple-200 rounded-full opacity-20 blur-3xl"
      ></div>
      <div
        class="absolute bottom-20 right-10 w-80 h-80 bg-blue-200 rounded-full opacity-20 blur-3xl"
      ></div>
    </div>

    <!-- 头部导航 -->
    <header class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8 relative z-10">
      <div class="flex items-center justify-between">
        <button
          class="flex items-center gap-2 px-4 py-2 text-purple-600 hover:text-purple-700 bg-white/80 backdrop-blur-sm rounded-lg shadow-sm transition-all duration-200 hover:shadow-md group"
          @click="goToHome"
        >
          <span class="text-lg transition-transform group-hover:-translate-x-1">←</span>
          <span class="font-medium">返回首页</span>
        </button>
        <div class="text-center">
          <h1
            class="text-3xl md:text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-2"
          >
            精选商品
          </h1>
          <div class="text-sm text-gray-500 tracking-wider uppercase">为您精心挑选的优质商品</div>
        </div>
        <div class="w-[100px]"></div>
        <!-- 用于平衡布局的占位div -->
      </div>
    </header>

    <!-- 主内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <!-- 精选商品区域 -->
      <div
        class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-6 mb-8 relative overflow-hidden"
      >
        <!-- 背景装饰 -->
        <div
          class="absolute -top-24 -right-24 w-48 h-48 bg-purple-100 rounded-full opacity-50"
        ></div>
        <div
          class="absolute -bottom-24 -left-24 w-48 h-48 bg-blue-100 rounded-full opacity-50"
        ></div>

        <!-- 精选标志 -->
        <div class="relative mb-12">
          <div class="absolute -top-6 left-1/2 transform -translate-x-1/2">
            <div
              class="w-14 h-14 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center shadow-lg"
            >
              <span class="text-white text-2xl">✨</span>
            </div>
          </div>
          <div class="text-center pt-10">
            <h2 class="text-2xl font-bold text-gray-800 mb-3">精选好物</h2>
            <p class="text-gray-600 text-sm max-w-md mx-auto">
              精心挑选，品质保证，为您提供最优质的商品体验
            </p>
            <div class="flex justify-center gap-4 mt-6">
              <span
                class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-purple-100 text-purple-800 shadow-sm hover:shadow-md transition-shadow"
              >
                <span class="mr-2">⭐</span> 精品推荐
              </span>
              <span
                class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800 shadow-sm hover:shadow-md transition-shadow"
              >
                <span class="mr-2">💎</span> 品质保证
              </span>
            </div>
          </div>
        </div>

        <!-- 商品网格 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          <div
            v-for="product in products"
            :key="product.id"
            class="group bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden flex flex-col transform hover:-translate-y-2"
          >
            <div class="relative aspect-[4/3] overflow-hidden">
              <img
                :src="product.pic"
                :alt="product.name"
                class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
              />
              <div
                class="absolute top-3 left-3 bg-purple-600 text-white px-3 py-1 text-xs rounded-full shadow-lg"
              >
                精选
              </div>
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              >
                <div class="absolute bottom-3 left-3 right-3 flex justify-between items-center">
                  <span class="text-white font-medium text-sm">查看详情</span>
                  <button
                    @click.stop="toggleFavorite(product.id)"
                    class="p-2 bg-white/90 backdrop-blur-sm rounded-full hover:bg-purple-600 hover:text-white transition-colors duration-200"
                  >
                    <i class="icon-heart" :class="{ 'text-red-500': product.isFavorite }">❤</i>
                  </button>
                </div>
              </div>
            </div>
            <div class="p-4 flex-grow flex flex-col">
              <h3 class="text-lg font-medium text-gray-800 mb-2 line-clamp-1">
                {{ product.name }}
              </h3>
              <p class="text-sm text-gray-500 mb-3 line-clamp-2 flex-grow">
                {{ stripHtml(product.detailHtml || '') }}
              </p>
              <div class="flex items-center justify-between">
                <span
                  class="text-lg font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent"
                  >¥{{ product.price }}</span
                >
                <div class="flex gap-2">
                  <button
                    @click.stop="handleViewDetails(product)"
                    class="p-2 text-sm bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors"
                  >
                    详情
                  </button>
                  <button
                    @click.stop="handleAddToCart(product)"
                    class="p-2 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    购买
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div
          v-if="products.length === 0"
          class="flex flex-col items-center justify-center py-16 px-4"
        >
          <div class="bg-white p-8 rounded-2xl shadow-lg text-center max-w-md">
            <h3 class="text-xl font-medium text-gray-900 mb-2">暂无精选商品</h3>
            <p class="text-gray-500">暂时没有精选商品，请稍后再来查看</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部 -->
    <footer
      class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 text-center text-gray-500 text-sm relative z-10"
    >
      <div class="mb-2">精选商品 · 品质保障 · 正品直供</div>
      <div>© 2025 商城. 保留所有权利</div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'
import type { ProductVO } from '@/types/product'
import { cartApi } from '@/api/cart'

const { t } = useI18n()
const router = useRouter()
const products = ref<ProductVO[]>([])

// 获取精选商品
const getSelectedProducts = async () => {
  try {
    // 使用分类ID为0获取全部商品，然后筛选精选商品
    const res = await request.get('/products/category/0')
    if (res.data.code === 1) {
      const allProducts = Array.isArray(res.data.data) ? res.data.data : [res.data.data]
      // 这里可以根据实际情况筛选精选商品，例如根据某个标签或属性
      products.value = allProducts.map((item) => ({
        ...item,
        isFavorite: false,
      }))
    }
  } catch (error) {
    console.error('获取精选商品失败:', error)
    ElMessage.error('获取精选商品失败')
  }
}

// 解析HTML文本
const stripHtml = (html: string): string => {
  const tmp = document.createElement('div')
  tmp.innerHTML = html
  return tmp.textContent || tmp.innerText || ''
}

// 查看商品详情
const handleViewDetails = (product: ProductVO) => {
  router.push({
    path: `/product/${product.id}`,
    query: {
      name: product.name,
      image: product.pic,
      price: product.price.toString(),
      introduction: product.detailHtml,
    },
  })
}

// 添加到购物车
const handleAddToCart = async (product: ProductVO) => {
  const token = localStorage.getItem('token')
  if (!token) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }

  try {
    const userStr = localStorage.getItem('user')
    const user = userStr ? JSON.parse(userStr) : null
    const userId = user?.id
    if (!userId) {
      ElMessage.error('用户信息异常')
      return
    }

    const cartData = {
      buyerId: userId,
      productId: product.id.toString(),
      productName: product.name,
      number: 1,
      price: product.price,
      image: product.pic,
    }

    const res = await cartApi.addToCart(cartData)
    if (res.data.code === 1) {
      ElMessage.success('添加成功')
    } else {
      ElMessage.error(res.data.msg || '添加失败')
    }
  } catch (error) {
    console.error('添加购物车失败:', error)
    ElMessage.error('添加失败')
  }
}

// 返回首页方法
const goToHome = () => {
  router.push('/')
}

// 收藏商品
const toggleFavorite = async (productId) => {
  const token = localStorage.getItem('token')
  if (!token) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  const product = products.value.find((p) => p.id === productId)
  if (product) {
    try {
      const res = await request.post('/favorites/toggle', { productId })
      if (res.data.code === 1) {
        product.isFavorite = !product.isFavorite
        ElMessage.success(product.isFavorite ? '收藏成功' : '取消收藏成功')
      } else {
        ElMessage.error('操作失败')
      }
    } catch (error) {
      console.error('收藏操作失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

onMounted(() => {
  getSelectedProducts()
})
</script>

<style scoped>
.icon-heart {
  color: #999;
  font-size: 16px;
}

.icon-heart.text-red-500 {
  color: #ef4444;
}
</style>
