<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 动态背景效果 -->
    <div class="fixed inset-0 -z-10 overflow-hidden">
      <div
        class="absolute top-0 -left-4 w-72 h-72 bg-purple-300/30 rounded-full mix-blend-multiply filter blur-xl animate-blob"
      ></div>
      <div
        class="absolute top-0 -right-4 w-72 h-72 bg-yellow-300/30 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"
      ></div>
      <div
        class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300/30 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"
      ></div>
    </div>

    <!-- Top Bar -->
    <div class="bg-[#6B46C1] text-white py-1">
      <div class="container mx-auto px-6 flex justify-between items-center text-sm">
        <!-- 语言切换下拉框 -->
        <div class="relative">
          <button
            @click="showLanguageMenu = !showLanguageMenu"
            class="flex items-center space-x-1 text-white hover:text-gray-200"
          >
            <span>{{
              currentLanguage === 'english'
                ? 'English'
                : currentLanguage === 'chinese_traditional'
                  ? '繁體中文'
                  : '简体中文'
            }}</span>
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>

          <!-- 下拉菜单 -->
          <div
            v-if="showLanguageMenu"
            class="absolute top-full left-0 mt-1 bg-white rounded-md shadow-lg z-50 py-1 ignore"
          >
            <ul class="ignore">
              <li>
                <a
                  href="javascript:translate.changeLanguage('english');"
                  class="block px-4 py-2 text-gray-700 hover:bg-purple-100 whitespace-nowrap"
                >
                  English
                </a>
              </li>
              <li>
                <a
                  href="javascript:translate.changeLanguage('chinese_simplified');"
                  class="block px-4 py-2 text-gray-700 hover:bg-purple-100 whitespace-nowrap"
                >
                  简体中文
                </a>
              </li>
              <li>
                <a
                  href="javascript:translate.changeLanguage('chinese_traditional');"
                  class="block px-4 py-2 text-gray-700 hover:bg-purple-100 whitespace-nowrap"
                >
                  繁體中文
                </a>
              </li>
            </ul>
          </div>
        </div>

        <router-link
          to="/update-notification"
          class="hover:text-gray-200 transition-colors duration-200"
        >
          更新公告
        </router-link>
      </div>
    </div>

    <!-- Main Header -->
    <header class="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-10">
      <div class="container mx-auto px-6">
        <div class="flex items-center justify-between h-20">
          <div class="flex items-center space-x-12">
            <img
              src="/placeholder.svg?height=40&width=120"
              alt="NeoBund"
              class="h-10 object-contain"
            />
            <nav class="hidden lg:flex space-x-8">
              <router-link
                v-for="item in ['selected', 'newArrivals', 'bestSellers', 'allProducts']"
                :key="item"
                :to="`/${item}`"
                class="text-gray-600 hover:text-[#6B46C1] transition-colors duration-200"
              >
                {{
                  item === 'selected'
                    ? '精选商品'
                    : item === 'newArrivals'
                      ? '新品上市'
                      : item === 'bestSellers'
                        ? '热销商品'
                        : '全部商品'
                }}
              </router-link>
            </nav>
          </div>
          <div class="flex items-center space-x-6">
            <!-- 站内信徽章 -->
            <el-badge :show-zero="unReadCount !== 0" :value="unReadCount" :max="99" class="">
              <el-button circle @click="goToMessagePanel">
                <el-icon :size="20"><Bell /></el-icon>
              </el-button>
            </el-badge>
            <div class="relative">
              <input
                type="text"
                v-model="searchQuery"
                @keyup.enter="handleSearch"
                :placeholder="'搜索商品...'"
                class="w-64 px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#6B46C1] transition-colors duration-200"
              />
              <button
                @click="handleSearch"
                class="absolute right-3 top-2.5 text-gray-400 hover:text-[#6B46C1]"
              >
                <Search class="h-5 w-5" />
              </button>
            </div>
            <div class="flex items-center space-x-4">
              <div class="relative">
                <button
                  class="p-2 hover:bg-gray-100 rounded-full transition-colors relative cart-btn"
                  @click.stop="handleCartClick"
                >
                  <ShoppingCart class="h-6 w-6 text-gray-600" />
                  <span
                    v-if="cartItemCount > 0"
                    class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"
                  >
                    {{ cartItemCount }}
                  </span>
                </button>

                <!-- 购物车弹出面板 -->
                <div
                  v-if="showCart"
                  class="absolute right-0 top-full mt-2 w-80 bg-white rounded-lg shadow-xl p-4 z-50"
                  @click.stop
                >
                  <h3 class="text-lg font-medium mb-4">购物车</h3>
                  <div v-if="cartItems.length > 0" class="space-y-4">
                    <div v-for="item in cartItems" :key="item.id" class="flex items-center gap-4">
                      <img
                        :src="item.image"
                        :alt="item.productName"
                        class="w-16 h-16 object-cover rounded"
                      />
                      <div class="flex-1">
                        <h4 class="font-medium">{{ item.productName }}</h4>
                        <p class="text-gray-600">
                          {{
                            new Intl.NumberFormat('en-US', {
                              style: 'currency',
                              currency: 'USD',
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            }).format(item.price)
                          }}
                        </p>
                        <div class="flex items-center gap-2 mt-1">
                          <button
                            @click.stop="updateQuantity(item.id, item.number - 1)"
                            class="p-1 rounded border hover:bg-gray-50"
                          >
                            -
                          </button>
                          <span>{{ item.number }}</span>
                          <button
                            @click.stop="updateQuantity(item.id, item.number + 1)"
                            class="p-1 rounded border hover:bg-gray-50"
                          >
                            +
                          </button>
                        </div>
                      </div>
                      <button
                        @click.stop="removeFromCart(item.id)"
                        class="text-red-500 hover:text-red-600"
                      >
                        <X class="h-5 w-5" />
                      </button>
                    </div>
                    <div class="border-t pt-4">
                      <div class="flex justify-between mb-4">
                        <span>总计</span>
                        <span class="font-medium">{{ cartTotal }}</span>
                      </div>
                      <button
                        class="w-full bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700"
                      >
                        结算
                      </button>
                    </div>
                  </div>
                  <div v-else class="text-center py-8 text-gray-500">购物车是空的</div>
                </div>
              </div>
              <!-- 登录状态下显示用户信息和退出登录 -->
              <template v-if="isLoggedIn">
                <button
                  @click="handleUserInfo"
                  class="flex items-center space-x-2 text-gray-600 hover:text-[#6B46C1]"
                >
                  <User class="h-5 w-5" />
                  <span>用户个人信息</span>
                </button>
                <button
                  @click="handleLogout"
                  class="flex items-center space-x-2 text-gray-600 hover:text-red-500"
                >
                  <LogOut class="h-5 w-5" />
                  <span>退出登录</span>
                </button>
              </template>

              <!-- 未登录状态下显示登录/注册 -->
              <template v-else>
                <button
                  @click="handleLogin"
                  class="flex items-center space-x-2 text-gray-600 hover:text-[#6B46C1]"
                >
                  <User class="h-5 w-5" />
                  <span>登录/注册</span>
                </button>
              </template>
            </div>
          </div>
        </div>
      </div>
    </header>

    <main class="container mx-auto px-6 py-8">
      <!-- 添加悬浮动态装饰元素 -->
      <div class="fixed bottom-10 right-10 z-50 flex flex-col gap-4">
        <!-- 微信客服按钮和二维码 -->
        <div class="relative">
          <button
            @click="showQrCode = !showQrCode"
            class="p-3 bg-green-500/90 hover:bg-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-sm group"
          >
            <div class="transform transition-transform group-hover:scale-110">
              <svg class="h-6 w-6" viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M8.691 2C5.309 2 2.044 4.662 2.044 8.443c0 2.1 1.139 3.854 3.031 5.18l-0.754 2.432c-0.047 0.156-0.005 0.323 0.112 0.437 0.073 0.074 0.171 0.109 0.27 0.109 0.057 0 0.116-0.013 0.169-0.036l2.876-1.156c0.348 0.099 0.697 0.187 1.049 0.249 0.176 0.031 0.341-0.084 0.372-0.26 0.031-0.175-0.084-0.341-0.26-0.372-0.375-0.066-0.745-0.158-1.113-0.269-0.073-0.022-0.151-0.022-0.224 0.001l-2.281 0.915 0.57-1.841c0.042-0.133 0.009-0.276-0.083-0.379-1.813-2.021-1.9-4.193-0.22-6.189 1.602-1.907 4.254-2.044 5.345-2.044 3.218 0 5.686 2.154 5.686 4.973 0 2.16-1.293 3.906-3.236 4.941 0 0-0.977 0.375-2.664 0.375s-2.738-0.602-2.738-0.602c-0.168-0.051-0.345 0.044-0.396 0.213-0.051 0.168 0.044 0.345 0.213 0.396 0.092 0.028 1.338 0.743 2.921 0.743 1.826 0 2.931-0.426 2.931-0.426 2.218-1.169 3.717-3.138 3.717-5.64 0-3.261-2.777-5.723-6.436-5.723zM18.071 10.5c-1.591 0-3.29 0.088-5.318 1.874-2.133 1.876-1.858 4.759-1.853 4.805 0.005 0.14 0.106 0.254 0.242 0.277 0.135 0.023 0.27-0.057 0.318-0.188 0.004-0.009 0.337-0.928 1.093-1.642 0.777-0.735 1.865-1.083 3.233-1.036 3.115 0.106 5.481 2.368 5.481 5.254 0 1.464-0.578 2.564-1.769 3.374-0.06 0.041-0.101 0.102-0.118 0.171l-0.275 1.111-1.234-0.693c-0.056-0.031-0.119-0.044-0.182-0.039-0.201 0.02-0.402 0.031-0.604 0.031-3.292 0-5.974-2.327-5.974-5.188 0-0.177-0.143-0.319-0.319-0.319s-0.319 0.143-0.319 0.319c0 3.207 2.976 5.826 6.613 5.826 0.224 0 0.448-0.012 0.669-0.033l1.744 0.981c0.058 0.033 0.122 0.049 0.187 0.049 0.068 0 0.136-0.021 0.194-0.062 0.108-0.076 0.164-0.208 0.143-0.34l-0.391-1.576c1.269-0.906 1.947-2.155 1.947-3.806 0-3.234-2.724-5.768-6.308-5.887-1.506-0.052-2.738 0.347-3.638 1.189-0.304 0.288-0.551 0.594-0.755 0.899 0.289-1.352 1.013-2.707 2.395-3.927 1.82-1.6 3.351-1.686 4.851-1.686 0.177 0 0.319-0.143 0.319-0.319s-0.143-0.319-0.319-0.319z"
                ></path>
              </svg>
            </div>
          </button>

          <!-- 二维码卡片 -->
          <Transition
            enter-active-class="transition duration-300 ease-out"
            enter-from-class="transform translate-y-2 opacity-0"
            enter-to-class="transform translate-y-0 opacity-100"
            leave-active-class="transition duration-200 ease-in"
            leave-from-class="transform translate-y-0 opacity-100"
            leave-to-class="transform translate-y-2 opacity-0"
          >
            <div
              v-if="showQrCode"
              class="absolute bottom-full right-0 mb-4 bg-white rounded-xl shadow-2xl p-4 w-[280px]"
              @click.stop
            >
              <div class="relative">
                <!-- 关闭按钮 -->
                <button
                  @click="showQrCode = false"
                  class="absolute -top-2 -right-2 p-1 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors"
                >
                  <svg
                    class="w-4 h-4 text-gray-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>

                <div class="text-center mb-3">
                  <h3 class="text-lg font-medium text-gray-900">微信客服</h3>
                  <p class="text-sm text-gray-500 mt-1">扫描下方二维码联系客服</p>
                </div>

                <div class="qrcode-container p-2 border-2 border-green-500 rounded-lg bg-white">
                  <img :src="qrCodeUrl" alt="微信客服二维码" class="w-full h-full object-cover" />
                </div>

                <div class="mt-3 text-center text-sm text-gray-500">
                  <p>或直接访问：</p>
                  <a
                    href="https://work.weixin.qq.com/kfid/kfcbce26add2a69c670"
                    target="_blank"
                    class="text-green-600 hover:text-green-700 underline inline-flex items-center gap-1"
                  >
                    企业微信客服链接
                    <svg class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"
                      />
                      <path
                        d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"
                      />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </Transition>
        </div>
      </div>

      <!-- 添加页面加载进度指示器 -->
      <div
        class="fixed top-0 left-0 h-1 bg-gradient-to-r from-purple-500 via-pink-500 to-purple-500 transition-all duration-300"
        :style="{ width: `${scrollProgress}%` }"
      ></div>

      <!-- Hero Banner -->
      <div class="relative overflow-hidden">
        <!-- 添加装饰性图形 -->
        <div class="absolute top-0 left-0 w-full h-full">
          <div
            class="absolute top-10 left-10 w-20 h-20 border-4 border-purple-200 rounded-full animate-spin-slow"
          ></div>
          <div
            class="absolute bottom-20 right-20 w-32 h-32 border-8 border-yellow-200 rounded-lg animate-bounce-slow"
          ></div>
        </div>
        <div class="relative mb-12 rounded-xl overflow-hidden shadow-lg">
          <div
            class="relative h-[400px] transition-transform duration-500 ease-out"
            :style="{ transform: `translateX(-${currentSlide * 100}%)` }"
          >
            <div
              v-for="(slide, index) in slides"
              :key="slide.id"
              class="absolute top-0 left-0 w-full h-full"
              :style="{ transform: `translateX(${index * 100}%)` }"
            >
              <img :src="slide.image" :alt="slide.title" class="w-full h-full object-cover" />
              <div class="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent">
                <div class="h-full flex items-center">
                  <div class="text-white ml-16">
                    <h1 class="text-5xl font-bold mb-2">{{ slide.title }}</h1>
                    <p class="text-3xl font-light mb-8">{{ slide.subtitle }}</p>
                    <button
                      class="bg-[#6B46C1] hover:bg-[#553C9A] text-white px-8 py-3 rounded-full text-lg font-semibold transition-colors duration-200"
                    >
                      {{ slide.buttonText }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <button
            @click="changeSlide('prev')"
            class="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white p-2 rounded-full shadow-lg transition-all duration-200 hover:scale-110"
            :class="{ 'pointer-events-none opacity-50': isTransitioning }"
          >
            <ChevronLeft class="h-6 w-6" />
          </button>
          <button
            @click="changeSlide('next')"
            class="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white p-2 rounded-full shadow-lg transition-all duration-200 hover:scale-110"
            :class="{ 'pointer-events-none opacity-50': isTransitioning }"
          >
            <ChevronRight class="h-6 w-6" />
          </button>
          <div class="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
            <div
              v-for="(_, index) in slides"
              :key="index"
              :class="[
                'w-2 h-2 rounded-full transition-all duration-300',
                currentSlide === index ? 'bg-white w-4' : 'bg-white/50',
              ]"
            ></div>
          </div>
        </div>
      </div>

      <div class="flex gap-8">
        <!-- 分类目录 侧边栏 -->
        <div class="w-48 flex-shrink-0">
          <div class="bg-white rounded-xl shadow-sm overflow-hidden sticky top-24">
            <div class="p-4 bg-[#6B46C1] text-white">
              <h2 class="font-semibold">商品分类</h2>
            </div>
            <ul class="py-2 max-h-[calc(100vh-200px)] overflow-y-auto">
              <template v-if="categories && categories.length > 0">
                <template v-for="category in categories" :key="category.id">
                  <!-- 一级分类 -->
                  <li
                    @click="handleCategoryClick(category)"
                    @mouseenter="hoveredCategory = category.categoryName"
                    @mouseleave="hoveredCategory = null"
                    :class="[
                      'px-4 py-2.5 cursor-pointer text-sm transition-all duration-200',
                      'hover:bg-purple-50 hover:text-[#6B46C1]',
                      activeCategory === category.categoryName
                        ? 'bg-purple-50 text-[#6B46C1] font-medium'
                        : '',
                      hoveredCategory === category.categoryName ? 'bg-purple-50/50' : '',
                    ]"
                  >
                    <div class="flex items-center justify-between">
                      <span>{{
                        currentLanguage === 'english'
                          ? category.nameEn || category.categoryName
                          : category.categoryName
                      }}</span>
                      <ChevronRight
                        v-if="category.children && category.children.length > 0"
                        class="h-4 w-4 transition-transform duration-200"
                        :class="{ 'rotate-90': expandedCategories.has(category.id) }"
                      />
                    </div>
                  </li>

                  <!-- 二级和三级分类 -->
                  <template v-if="category.children && category.children.length > 0">
                    <Transition name="slide-fade">
                      <div v-show="expandedCategories.has(category.id)">
                        <template v-for="subCategory in category.children" :key="subCategory.id">
                          <!-- 二级分类 -->
                          <li
                            @click="handleCategoryClick(subCategory)"
                            @mouseenter="hoveredCategory = subCategory.categoryName"
                            @mouseleave="hoveredCategory = null"
                            :class="[
                              'px-4 py-2.5 cursor-pointer text-sm transition-all duration-200 ml-4',
                              'hover:bg-purple-50 hover:text-[#6B46C1]',
                              activeCategory === subCategory.categoryName
                                ? 'bg-purple-50 text-[#6B46C1] font-medium'
                                : '',
                              hoveredCategory === subCategory.categoryName ? 'bg-purple-50/50' : '',
                            ]"
                          >
                            <div class="flex items-center justify-between">
                              <span>{{
                                currentLanguage === 'english'
                                  ? subCategory.nameEn || subCategory.categoryName
                                  : subCategory.categoryName
                              }}</span>
                              <ChevronRight
                                v-if="subCategory.children && subCategory.children.length > 0"
                                class="h-4 w-4 transition-transform duration-200"
                                :class="{ 'rotate-90': expandedCategories.has(subCategory.id) }"
                              />
                            </div>
                          </li>

                          <!-- 三级分类 -->
                          <Transition name="slide-fade">
                            <div v-show="expandedCategories.has(subCategory.id)">
                              <li
                                v-for="thirdCategory in subCategory.children"
                                :key="thirdCategory.id"
                                @click="handleCategoryClick(thirdCategory)"
                                @mouseenter="hoveredCategory = thirdCategory.categoryName"
                                @mouseleave="hoveredCategory = null"
                                :class="[
                                  'px-4 py-2.5 cursor-pointer text-sm transition-all duration-200 ml-8',
                                  'hover:bg-purple-50 hover:text-[#6B46C1]',
                                  activeCategory === thirdCategory.categoryName
                                    ? 'bg-purple-50 text-[#6B46C1] font-medium'
                                    : '',
                                  hoveredCategory === thirdCategory.categoryName
                                    ? 'bg-purple-50/50'
                                    : '',
                                ]"
                              >
                                <div class="flex items-center justify-between">
                                  <span>{{
                                    currentLanguage === 'english'
                                      ? thirdCategory.nameEn || thirdCategory.categoryName
                                      : thirdCategory.categoryName
                                  }}</span>
                                  <ChevronRight
                                    v-if="hoveredCategory === thirdCategory.categoryName"
                                    class="h-4 w-4"
                                  />
                                </div>
                              </li>
                            </div>
                          </Transition>
                        </template>
                      </div>
                    </Transition>
                  </template>
                </template>
              </template>
              <template v-else>
                <li class="px-4 py-2.5 text-sm text-gray-500">暂无分类数据</li>
              </template>
            </ul>
          </div>
        </div>

        <!-- Products Grid -->
        <div class="flex-1 p-6">
          <div
            class="flex-1 p-6 bg-gradient-to-br from-gray-50 via-purple-50/30 to-gray-100 min-h-screen"
          >
            <TransitionGroup
              name="fade"
              tag="div"
              class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
            >
              <template v-if="loading">
                <div
                  v-for="n in 8"
                  :key="`skeleton-${n}`"
                  class="bg-white rounded-xl shadow-sm p-6 space-y-4 animate-pulse min-w-[300px]"
                >
                  <div class="w-full h-48 bg-gray-200 rounded-lg" />
                  <div class="h-6 bg-gray-200 w-3/4 rounded" />
                  <div class="h-4 bg-gray-200 w-full rounded" />
                  <div class="flex items-center justify-between">
                    <div class="h-6 bg-gray-200 w-20 rounded" />
                    <div class="h-9 bg-gray-200 w-28 rounded" />
                  </div>
                </div>
              </template>

              <template v-else>
                <div
                  v-for="product in filteredProducts"
                  :key="product.id"
                  class="group bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 p-4 relative overflow-hidden min-w-[250px]"
                >
                  <!-- 商品状态遮罩 -->
                  <Transition name="fade">
                    <div
                      v-if="product.publishStatus === 0"
                      class="absolute inset-0 bg-gray-900/60 backdrop-blur-sm flex items-center justify-center z-20"
                      aria-hidden="true"
                    >
                      <AlertCircleIcon class="w-6 h-6 text-white mr-2" />
                      <span class="text-white font-medium">商品未上架</span>
                    </div>
                  </Transition>

                  <!-- 商品图片容器 -->
                  <div
                    class="relative overflow-hidden rounded-lg aspect-square mb-3 cursor-zoom-in max-w-[220px] mx-auto"
                    @click="handleImageClick"
                  >
                    <img
                      :src="product.pic"
                      :alt="product.name"
                      class="w-full h-full object-contain transform transition-transform duration-300 hover:scale-110"
                      :class="{ 'scale-110': isImageClicked && clickedImageId === product.id }"
                    />
                    <!-- 图片悬停遮罩 -->
                    <div
                      class="absolute inset-0 bg-black/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      v-if="product.publishStatus === 1"
                    />
                  </div>

                  <!-- 商品信息 -->
                  <div class="space-y-1">
                    <div class="flex items-start justify-between gap-2">
                      <h3
                        class="text-lg font-medium text-gray-900 line-clamp-2 flex-1 group-hover:text-purple-600 transition-colors duration-300"
                        :title="product.name"
                      >
                        {{ product.name }}
                      </h3>
                    </div>

                    <p class="text-gray-600 text-sm line-clamp-2" :title="product.detailHtml">
                      {{ stripHtml(product.detailHtml || '') }}
                    </p>

                    <!-- 价格区域 -->
                    <div class="flex items-end justify-between pt-2">
                      <div class="space-y-1">
                        <div class="flex items-baseline gap-2">
                          <span
                            class="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent"
                          >
                            ${{ product.price }}
                          </span>
                        </div>
                      </div>
                    </div>

                    <!-- 操作按钮区 -->
                    <div class="flex items-center gap-2 pt-3">
                      <button
                        type="button"
                        @click="handleViewDetails(product)"
                        class="flex-1 inline-flex items-center justify-center px-2 py-2 rounded-lg text-sm font-medium border border-purple-200 text-purple-700 bg-purple-50 hover:bg-purple-100 transition-all duration-200 active:scale-98 hover:border-purple-300 whitespace-nowrap"
                      >
                        <ExternalLinkIcon class="w-4 h-4 mr-1" />
                        查看详情
                      </button>
                      <button
                        type="button"
                        @click="handleAddToCart(product)"
                        class="flex-1 inline-flex items-center justify-center px-2 py-2 rounded-lg text-sm font-medium bg-purple-600 text-white hover:bg-purple-700 hover:shadow-lg hover:shadow-purple-200 transition-all duration-200 active:scale-98 whitespace-nowrap"
                      >
                        <ShoppingCartIcon class="w-4 h-4 mr-1" />
                        加入购物车
                      </button>
                    </div>
                  </div>

                  <!-- 商品标签 -->
                  <!-- 因为ProductVO中没有tags属性，暂时注释掉这部分代码 -->
                  <!--
                  <div
                    v-if="product.tags && product.tags.length > 0"
                    class="absolute top-6 left-0 flex flex-col gap-2"
                  >
                    <span
                      v-for="tag in product.tags"
                      :key="tag"
                      class="bg-red-500 text-white text-xs px-3 py-1 rounded-r-full shadow-md"
                    >
                      {{ tag }}
                    </span>
                  </div>
                  -->
                </div>
              </template>
            </TransitionGroup>

            <!-- 空状态 -->
            <div
              v-if="!loading && filteredProducts.length === 0"
              class="flex flex-col items-center justify-center py-16 px-4"
            >
              <div class="bg-white p-8 rounded-2xl shadow-lg text-center max-w-md">
                <PackageIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 class="text-xl font-medium text-gray-900 mb-2">暂无商品 / No Products</h3>
                <p class="text-gray-500">
                  暂时没有可用的商品，请稍后再来查看
                  <span class="block mt-1 text-gray-400 text-sm"
                    >No products available at the moment. Please check back later.</span
                  >
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Product Modal -->
    <Teleport to="body">
      <div
        v-if="selectedProduct"
        class="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
        @click="selectedProduct = null"
      >
        <div
          class="bg-white rounded-xl max-w-2xl w-full p-6 transform transition-all duration-300 scale-100"
          @click.stop
        >
          <div class="flex justify-between items-start mb-4">
            <h2 class="text-2xl font-bold">{{ selectedProduct.name }}</h2>
            <button
              @click="selectedProduct = null"
              class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
            >
              <X class="h-6 w-6" />
            </button>
          </div>
          <div class="grid md:grid-cols-2 gap-6">
            <div class="relative h-64">
              <img
                :src="selectedProduct.image"
                :alt="selectedProduct.name"
                class="w-full h-full object-cover rounded-lg"
              />
              <span
                v-if="selectedProduct.tag"
                class="absolute top-2 left-2 bg-[#6B46C1] text-white px-2 py-1 text-xs rounded"
              >
                {{ selectedProduct.tag }}
              </span>
            </div>
            <div>
              <div class="mt-6 space-y-3">
                <button
                  class="w-full bg-[#6B46C1] hover:bg-[#553C9A] text-white px-4 py-2 rounded-lg transition-colors duration-200"
                  @click="handleBuyNow(selectedProduct)"
                >
                  {{ t('product.buyNow') }}
                </button>
                <button
                  class="w-full border border-[#6B46C1] text-[#6B46C1] hover:bg-[#6B46C1] hover:text-white px-4 py-2 rounded-lg transition-colors duration-200"
                  @click="addToCart(selectedProduct)"
                >
                  {{ t('product.addToCart') }}
                </button>
              </div>
              <p class="text-gray-600 mb-4">{{ selectedProduct.description }}</p>
              <div class="space-y-2">
                <p
                  v-for="detail in selectedProduct.details"
                  :key="detail"
                  class="text-sm text-gray-500"
                >
                  • {{ detail }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Teleport>

    <!-- Footer -->
    <footer class="bg-white border-t">
      <div class="container mx-auto px-4 py-8">
        <div class="flex flex-wrap justify-between">
          <!-- Contact Information -->
          <div class="w-full md:w-auto mb-6 md:mb-0">
            <h3 class="text-base font-semibold text-gray-800 mb-4">联系方式 / Contact</h3>
            <div class="space-y-3">
              <div class="flex items-center">
                <span class="text-gray-600 w-40">帮助与支持 / Help</span>
                <a
                  href="mailto:<EMAIL>"
                  class="text-purple-600 hover:text-purple-700 transition-colors flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  <EMAIL>
                </a>
              </div>
              <div class="flex items-center">
                <span class="text-gray-600 w-40">售后服务 / Service</span>
                <a
                  href="mailto:<EMAIL>"
                  class="text-purple-600 hover:text-purple-700 transition-colors flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>

          <!-- Quick Links -->
          <div class="w-full md:w-auto">
            <h3 class="text-base font-semibold text-gray-800 mb-4">Quick Links</h3>
            <div class="grid grid-cols-2 gap-x-12 gap-y-2">
              <router-link
                to="/privacy-policy"
                class="text-gray-600 hover:text-purple-600 transition-colors whitespace-nowrap"
              >
                Privacy Policy
              </router-link>
              <router-link
                to="/shipping-policy"
                class="text-gray-600 hover:text-purple-600 transition-colors whitespace-nowrap"
              >
                Shipping Policy
              </router-link>
              <router-link
                to="/refund-policy"
                class="text-gray-600 hover:text-purple-600 transition-colors whitespace-nowrap"
              >
                Refund Policy
              </router-link>
              <router-link
                to="/terms-of-service"
                class="text-gray-600 hover:text-purple-600 transition-colors whitespace-nowrap"
              >
                Terms of Service
              </router-link>
            </div>
          </div>
        </div>

        <!-- Copyright -->
        <div class="mt-6 pt-4 border-t border-gray-100">
          <p class="text-sm text-center text-gray-500">
            © {{ new Date().getFullYear() }} ShareWharf. All Rights Reserved.
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, onUnmounted, nextTick, inject } from 'vue'
import { ElLoading, ElMessage } from 'element-plus'
import { CircleClose } from '@element-plus/icons-vue'
import { ShoppingCartIcon } from '@heroicons/vue/16/solid'
import { ExternalLinkIcon } from 'lucide-vue-next'
import {
  Globe,
  Search,
  ShoppingCart,
  User,
  ChevronLeft,
  ChevronRight,
  Star,
  X,
  Store,
  ShoppingBag,
  BarChart,
  Users,
  LogOut,
  UserPlus,
} from 'lucide-vue-next'
import type { ProductVO } from '@/types/product'
import { cartApi } from '@/api/cart'
import type { CartItem } from '@/types/cart'
import { useRouter } from 'vue-router'
import request from '@/utils/request'
import zhLocale from '../locales/zh'
import enLocale from '../locales/en'
import QRCode from 'qrcode'
import { getUnreadCount } from '@/api/messageList'

// 获取当前登录的用户信息
const userInfo = JSON.parse(localStorage.getItem('user') || '{}')

const router = useRouter()
const unReadCount = ref(0)
// 为Window对象添加translate属性的类型声明
declare global {
  interface Window {
    translate: {
      changeLanguage: (lang: string) => void
      selectLanguageTag: {
        show: boolean
      }
      service: {
        use: (service: string) => void
      }
      execute: () => void
    }
  }
}

// 本地语言状态，不再依赖Vue I18n
const currentLanguage = ref(localStorage.getItem('selectedLanguage') || 'chinese_simplified')

// 图片点击状态
const isImageClicked = ref(false)
const clickedImageId = ref<number | null>(null)

const handleImageClick = (event: MouseEvent) => {
  // 简单点击效果
  const element = event.currentTarget as HTMLElement
  element.classList.add('scale-110')

  // 300ms 后重置状态
  setTimeout(() => {
    element.classList.remove('scale-110')
  }, 300)
}

const handleViewDetails = async (product: ProductVO) => {
  router.push({
    path: `/product/${product.id}`,
    query: {
      name: product.name,
      image: product.pic,
      price: product.price.toString(),
      introduction: product.detailHtml,
    },
  })
}

// 类型定义
interface Product {
  id: number
  name: string
  price: number
  image: string
  category: string
  description: string
  details: string[]
  tag?: string
}

interface Slide {
  id: number
  image: string
  title: string
  subtitle: string
  buttonText: string
}

// 定义分类数据的类型
interface CategoryVO {
  id: number
  categoryName: string
  nameEn?: string
  children?: CategoryVO[]
  level?: number
}

// 存储分类数据
const categories = ref<CategoryVO[]>([
  {
    id: 0,
    categoryName: '全部',
    nameEn: 'All',
    level: 0,
  },
])

// 获取分类列表
const getCategories = async () => {
  try {
    const res = await request.get('/categories/tree')
    console.log(res)

    if (res.data.code === 1 && Array.isArray(res.data.data)) {
      // 递归处理分类数据的函数
      const processCategories = (categories: any[]): CategoryVO[] => {
        return categories.map((item) => {
          const processedCategory = {
            ...item,
            categoryName: item.name || item.categoryName,
            nameEn: item.nameEn || item.name || item.categoryName,
            level: item.level || 1,
          }

          // 递归处理子分类
          if (item.children && Array.isArray(item.children) && item.children.length > 0) {
            processedCategory.children = processCategories(item.children)
          }

          return processedCategory
        })
      }

      // 应用处理函数
      const processedData = processCategories(res.data.data)

      categories.value = [
        { id: 0, categoryName: '全部', nameEn: 'All', level: 0 },
        ...processedData,
      ]
    } else {
      console.error('获取分类失败')
    }
  } catch (error) {
    console.error('获取分类失败:', error)
  }
}

// 添加检查登录状态的方法
const handleUserInfo = () => {
  const token = localStorage.getItem('token')
  if (!token) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  router.push('/dashboard')
}

// 状态定义
const activeCategory = ref<string>('全部')
const hoveredCategory = ref<string | null>(null)
const currentSlide = ref<number>(0)
const selectedProduct = ref<Product | null>(null)
const isTransitioning = ref<boolean>(false)
const showCart = ref(false)
const cartItems = ref<
  Array<{
    id: number
    productName: string
    price: number
    image: string
    number: number
  }>
>([])
const scrolled = ref(false)
const scrollProgress = ref(0)

// 数据定义

const slides: Slide[] = [
  {
    id: 1,
    image: '/placeholder.svg?height=400&width=1200',
    title: 'Empower Your Dropshipping',
    subtitle: 'E-commerce',
    buttonText: 'Buy Now',
  },
  // ... 其他幻灯片
]

// 添加一个辅助函数来检查商品状态
const hasStatusOne = (product: any): boolean => {
  // 如果product具有status属性，则检查是否为1
  if ('status' in product) {
    return product.status === 1
  }
  // 否则使用publishStatus作为备选
  return product.publishStatus === 1
}

// 计算属性
const filteredProducts = computed(() => {
  // 先过滤出status为1的商品
  const statusFilteredProducts = products.value.filter(hasStatusOne)

  // 再根据分类过滤
  return activeCategory.value === '全部'
    ? statusFilteredProducts
    : statusFilteredProducts.filter(
        (product) => product.categoryId.toString() === activeCategory.value.toString(),
      )
})

const featuredProducts = computed(() => {
  // 只显示符合状态条件的商品
  return products.value.filter(hasStatusOne).slice(0, 4)
})

const cartItemCount = computed(() => cartItems.value.length)

const cartTotal = computed(() => {
  const total = cartItems.value.reduce((sum, item) => sum + item.price * item.number, 0)
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(total)
})

// 方法定义
const changeSlide = (direction: 'prev' | 'next') => {
  if (isTransitioning.value) return

  isTransitioning.value = true
  if (direction === 'next') {
    currentSlide.value = (currentSlide.value + 1) % slides.length
  } else {
    currentSlide.value = (currentSlide.value - 1 + slides.length) % slides.length
  }

  setTimeout(() => {
    isTransitioning.value = false
  }, 500)
}
// 打开站内信
const goToMessagePanel = () => {
  const token = localStorage.getItem('token')
  if (!token) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  router.push('/messageList')
}

const updateQuantity = async (id: number, number: number) => {
  if (number < 1) {
    ElMessage.warning('商品数量不能小于1')
    return
  }

  try {
    const res = await cartApi.updateCartItemNumber(id, number)
    if (res.data.code === 1) {
      ElMessage.success('修改成功')
      // 重新获取最新的购物车数据
      const userStr = localStorage.getItem('user')
      const user = userStr ? JSON.parse(userStr) : null
      const userId = user?.id
      if (userId) {
        const cartRes = await cartApi.getCartList(userId)
        if (cartRes.data.code === 1) {
          cartItems.value = cartRes.data.data
        }
      }
    }
  } catch (error) {
    console.error('更新数量失败:', error)
    ElMessage.error('更新失败')
  }
}

const selectCart = async () => {
  // 重新获取最新的购物车数据
  const userStr = localStorage.getItem('user')
  const user = userStr ? JSON.parse(userStr) : null
  const userId = user?.id
  if (userId) {
    const cartRes = await cartApi.getCartList(userId)
    if (cartRes.data.code === 1) {
      cartItems.value = cartRes.data.data
    }
  }
}

const removeFromCart = async (id: number) => {
  try {
    const res = await cartApi.removeFromCart(id)
    if (res.data.code === 1) {
      ElMessage.success('删除成功')
      // 重新获取最新的购物车数据
      const userStr = localStorage.getItem('user')
      const user = userStr ? JSON.parse(userStr) : null
      const userId = user?.id
      if (userId) {
        const cartRes = await cartApi.getCartList(userId)
        if (cartRes.data.code === 1) {
          cartItems.value = cartRes.data.data
        }
      }
    }
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

const addToCart = (product: Product) => {
  const existingItem = cartItems.value.find((item) => item.id === product.id)
  if (existingItem) {
    existingItem.number++
  } else {
    cartItems.value.push({
      id: product.id,
      productName: product.name,
      price: product.price,
      image: product.image,
      number: 1,
    })
  }
}

const handleBuyNow = (product: Product) => {
  // 创建新订单
  const newOrder = {
    id: Date.now(),
    items: [
      {
        ...product,
        quantity: 1,
      },
    ],
    totalAmount: product.price,
    status: '待付款',
    createTime: new Date().toLocaleString(),
  }

  // 保存到 localStorage
  const orders = JSON.parse(localStorage.getItem('orders') || '[]')
  orders.push(newOrder)
  localStorage.setItem('orders', JSON.stringify(orders))

  ElMessage.success('订单已创建')
  router.push('/user') // 跳转到用户页面
}

// 处理滚动事件
const handleScroll = () => {
  scrolled.value = document.documentElement.scrollTop > 50
  // 计算滚动进度
  const winScroll = document.documentElement.scrollTop
  const height = document.documentElement.scrollHeight - document.documentElement.clientHeight
  scrollProgress.value = (winScroll / height) * 100

  // 滚动时关闭语言下拉菜单
  if (showLanguageMenu.value) {
    showLanguageMenu.value = false
  }
}

// 添加商品列表的响应式变量
const loading = ref(false)
const products = ref<ProductVO[]>([])

// 根据分类ID获取商品列表
const getProductsByCategory = async (categoryId: number) => {
  try {
    const res = await request.get(`/products/category/${categoryId}`)
    if (res.data.code === 1) {
      products.value = Array.isArray(res.data.data) ? res.data.data : [res.data.data]
    } else {
      console.error('获取商品列表失败')
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
  } finally {
  }
}

// 添加展开状态跟踪
const expandedCategories = ref<Set<number>>(new Set())

// 修改分类点击事件处理
const handleCategoryClick = (category: CategoryVO) => {
  // 如果是叶子节点，直接更新选中状态并获取商品
  if (!category.children || category.children.length === 0) {
    activeCategory.value = category.categoryName
    getProductsByCategory(category.id)
    return
  }

  // 切换展开状态
  if (expandedCategories.value.has(category.id)) {
    expandedCategories.value.delete(category.id)
  } else {
    expandedCategories.value.add(category.id)
  }
}

// 添加退出登录方法
const handleLogout = () => {
  // 清除本地存储的用户信息和token
  localStorage.removeItem('token')
  localStorage.removeItem('user')
  localStorage.removeItem('tokenTimestamp')
  localStorage.removeItem('userId')

  // 提示用户
  ElMessage.success('退出登录成功')

  // 跳转到登录页
  router.push('/login')
}

// 添加登录方法
const handleLogin = () => {
  router.push('/login')
}

// 添加解析HTML文本的函数
const stripHtml = (html: string): string => {
  const tmp = document.createElement('div')
  tmp.innerHTML = html
  return tmp.textContent || tmp.innerText || ''
}

// 获取购物车列表
const getCartList = async () => {
  try {
    const userStr = localStorage.getItem('user')
    const user = userStr ? JSON.parse(userStr) : null
    const userId = user?.id?.toString()
    if (!userId) {
      ElMessage.error('用户信息异常')
      return
    }

    const res = await cartApi.getCartList(userId)
    if (res.code === 1) {
      cartItems.value = res.data
    }
  } catch (error) {
    console.error('获取购物车列表失败:', error)
  }
}

// 修改添加到购物车的方法
const handleAddToCart = async (product: ProductVO) => {
  const token = localStorage.getItem('token')
  if (!token) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }

  try {
    const userStr = localStorage.getItem('user')
    const user = userStr ? JSON.parse(userStr) : null
    const userId = user?.id
    if (!userId) {
      ElMessage.error('用户信息异常')
      return
    }

    const cartData: AddToCartDTO = {
      buyerId: userId,
      productId: product.id.toString(),
      productName: product.name,
      number: 1,
      price: product.price,
      image: product.pic,
    }

    const res = await cartApi.addToCart(cartData)
    if (res.data.code === 1) {
      ElMessage.success('添加成功')
      // 重新获取购物车数据以更新图标
      const cartRes = await cartApi.getCartList(userId)
      if (cartRes.data.code === 1) {
        cartItems.value = cartRes.data.data
      }
    } else {
      ElMessage.error(res.data.msg || '添加失败')
    }
  } catch (error) {
    console.error('添加购物车失败:', error)
    ElMessage.error('添加失败')
  }
}

// 修改购物车点击事件
const handleCartClick = async () => {
  if (showCart.value) {
    showCart.value = false
    return
  }
  const token = localStorage.getItem('token')
  if (!token) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }

  try {
    const userStr = localStorage.getItem('user')
    const user = userStr ? JSON.parse(userStr) : null
    const userId = user?.id
    if (!userId) {
      ElMessage.error('用户信息异常')
      return
    }

    const res = await cartApi.getCartList(userId)

    if (res.data.code === 1) {
      cartItems.value = res.data.data
      showCart.value = true
    }
  } catch (error) {
    console.error('获取购物车列表失败:', error)
    ElMessage.error('获取购物车列表失败')
  }
}

// 添加处理更新公告点击的方法
const handleUpdateNotification = () => {
  router.push('/update-notification')
}

// 搜索相关的变量
const searchQuery = ref('')
const originalProducts = ref<ProductVO[]>([])

// 搜索处理函数
const handleSearch = () => {
  const query = searchQuery.value.trim().toLowerCase()

  // 如果搜索框为空，恢复原始商品列表
  if (!query) {
    if (originalProducts.value.length > 0) {
      products.value = [...originalProducts.value]
      originalProducts.value = []
    }
    return
  }

  // 如果是第一次搜索，保存原始商品列表
  if (originalProducts.value.length === 0) {
    originalProducts.value = [...products.value]
  }

  // 根据商品名称和详情进行搜索
  const searchResults = originalProducts.value.filter((product) => {
    const nameMatch = product.name.toLowerCase().includes(query)
    const detailMatch = product.detailHtml
      ? product.detailHtml.toLowerCase().includes(query)
      : false
    return nameMatch || detailMatch
  })

  // 更新商品列表
  products.value = searchResults

  // 根据搜索结果显示不同的提示信息
  if (searchResults.length === 0) {
    ElMessage({
      message: `未找到包含"${searchQuery.value}"的商品`,
      type: 'warning',
      duration: 3000,
    })
  } else {
    ElMessage({
      message: `找到 ${searchResults.length} 个相关商品`,
      type: 'success',
      duration: 2000,
    })
  }
}

// 监听分类变化，重置搜索状态
watch(activeCategory, () => {
  searchQuery.value = ''
  if (originalProducts.value.length > 0) {
    products.value = [...originalProducts.value]
    originalProducts.value = []
  }
})

// 添加登录状态的计算属性
const isLoggedIn = computed(() => {
  return !!localStorage.getItem('token')
})

// 添加微信二维码的响应式变量
const showQrCode = ref(false)
const qrCodeUrl = ref('')

// 生成随机二维码的函数
const generateQrCode = async () => {
  try {
    // 企业微信客服链接
    const url = 'https://work.weixin.qq.com/kfid/kfcbce26add2a69c670'

    // 生成随机参数，确保每次扫码都是新的
    const randomParam = `?t=${Date.now()}-${Math.floor(Math.random() * 1000)}`

    // 生成二维码
    const qrDataUrl = await QRCode.toDataURL(url + randomParam, {
      errorCorrectionLevel: 'H',
      margin: 1,
      width: 256,
      color: {
        dark: '#10B981', // 绿色
        light: '#FFFFFF', // 白色
      },
    })

    qrCodeUrl.value = qrDataUrl
  } catch (error) {
    console.error('生成二维码失败:', error)
    ElMessage.error('生成二维码失败，请重试')
  }
}

// 监听弹窗显示状态，当弹窗打开时生成新的二维码
watch(showQrCode, (newVal) => {
  if (newVal) {
    generateQrCode()
  }
})

// 添加下拉菜单状态
const showLanguageMenu = ref(false)

// 处理全局点击事件，关闭语言下拉菜单
const handleClick = (event: MouseEvent) => {
  // 检查点击的元素是否在语言选择器外部
  const target = event.target as HTMLElement
  const languageMenu = document.querySelector('.ignore') // 使用之前添加的 ignore 类
  const languageButton = document.querySelector('[class*="flex items-center space-x-1 text-white"]')

  // 如果点击的不是语言菜单内部元素，也不是语言按钮本身，则关闭下拉菜单
  if (
    showLanguageMenu.value &&
    languageMenu &&
    !languageMenu.contains(target) &&
    languageButton &&
    !languageButton.contains(target)
  ) {
    showLanguageMenu.value = false
  }
}

const translateLoaded = ref(false)

const getUnreadCounts = async () => {
  const token = localStorage.getItem('token')
  if (!token) {
    return
  }
  try {
    const data = { type: 'user', id: userInfo.id }
    console.log(data)

    const res = await getUnreadCount(data)
    unReadCount.value = res.data.data
  } catch (error) {
    ElMessage.error('获取未读消息数量失败')
  }
}
// 加载 translate.js 脚本
const loadTranslateScript = () => {
  return new Promise<void>((resolve, reject) => {
    if (window.translate) {
      resolve()
      return
    }

    const script = document.createElement('script')
    script.src = 'https://cdn.staticfile.net/translate.js/3.12.0/translate.js'
    script.async = true
    script.onload = () => resolve()
    script.onerror = () => reject(new Error('Failed to load translate.js'))
    document.head.appendChild(script)
  })
}

// 初始化 translate.js
const initTranslate = () => {
  if (!window.translate) return

  window.translate.selectLanguageTag.show = false
  window.translate.service.use('client.edge')

  // 设置初始语言
  const savedLanguage = localStorage.getItem('selectedLanguage') || 'chinese_simplified'
  currentLanguage.value = savedLanguage
  window.translate.changeLanguage(savedLanguage)

  translateLoaded.value = true
}
const shutDownshowCart = () => {
  if (showCart.value) {
    showCart.value = false
    return
  }
}
onMounted(async () => {
  // 加载和初始化翻译库
  // try {
  // await loadTranslateScript()
  // initTranslate()
  // } catch (error) {
  // console.error('加载翻译库失败:', error)
  // ElMessage.error('加载翻译库失败')
  // }

  getUnreadCounts()
  // 现有的onMounted代码
  selectCart()
  // 不需要token也可以获取商品列表
  getCategories()
  getProductsByCategory(0)

  // 添加全局点击事件监听器
  document.addEventListener('click', handleClick)
  // 添加滚动事件监听器
  document.addEventListener('scroll', handleScroll)
  window.addEventListener('click', shutDownshowCart)
})

onUnmounted(() => {
  // 清理事件监听器
  document.removeEventListener('click', handleClick)
  document.removeEventListener('scroll', handleScroll)
  window.removeEventListener('click', shutDownshowCart)

})
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.fade-move {
  transition: transform 0.5s ease;
}

/* 添加商品卡片悬停时的光晕效果 */
.group:hover {
  box-shadow: 0 0 40px rgba(124, 58, 237, 0.15);
}

/* 确保禁用状态的按钮样式正确 */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 优化图片加载时的过渡效果 */
img {
  opacity: 0;
  animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* 添加按钮悬停效果 */
button {
  transform: translateY(0);
  transition: transform 0.2s ease;
}

button:not(:disabled):hover {
  transform: translateY(-1px);
}

button:not(:disabled):active {
  transform: translateY(0);
}

/* 添加渐变背景动画 */
.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradientMove 15s ease infinite;
}

@keyframes gradientMove {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}

/* 优化图片点击效果 */
img {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

.ignore {
  pointer-events: auto; /* 确保按钮可以点击 */
}

/* 添加展开/折叠动画 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
  max-height: 500px;
  opacity: 1;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
}

.slide-fade-move {
  transition: transform 0.3s ease;
}
</style>
