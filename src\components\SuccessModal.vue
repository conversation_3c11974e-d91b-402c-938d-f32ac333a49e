<template>
  <el-dialog :visible="visible" @close="handleClose">
    <template #default>
      <div class="text-center">
        <i class="el-icon-success text-green-500 text-5xl mb-4"></i>
        <h2 class="text-2xl font-bold mb-2">已提交申请</h2>
        <p class="text-gray-600">管理员将于24内进行审核并以短信形式进行通知</p>
      </div>
    </template>
    <template #footer>
      <el-button @click="handleLoginClick">用户登录</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()
const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits<{
  (e: 'close'): void
}>()

const handleClose = () => {
  emit('close')
}

const handleLoginClick = () => {
  emit('close') // 关闭弹窗
  router.push('/login') // 跳转到登录页面
}
</script>

<style scoped>
.el-dialog {
  border-radius: 8px;
}
</style>
