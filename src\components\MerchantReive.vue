<template>
  <div class="merchant-review min-h-screen">
    <!-- 背景装饰 -->
    <div class="fixed inset-0 bg-gradient-to-br from-purple-50 to-white -z-10 opacity-80"></div>
    <div
      class="fixed top-20 right-20 w-64 h-64 bg-purple-100 rounded-full blur-3xl opacity-30 -z-10"
    ></div>
    <div
      class="fixed bottom-20 left-20 w-72 h-72 bg-indigo-100 rounded-full blur-3xl opacity-30 -z-10"
    ></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 py-8">
      <!-- 顶部导航栏 -->
      <header
        class="bg-white rounded-2xl shadow-sm p-4 mb-8 sticky top-4 z-10 backdrop-blur-sm bg-opacity-90"
      >
        <div class="flex justify-between items-center">
          <div class="flex items-center gap-3">
            <div class="bg-gradient-to-r from-purple-600 to-indigo-600 p-3 rounded-xl shadow-md">
              <el-icon class="text-white text-xl"><Shop /></el-icon>
            </div>
            <div>
              <h1
                class="text-2xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent"
              >
                商家审核管理
              </h1>
              <p class="text-sm text-gray-500">审核商家入驻申请，确保平台商家质量</p>
            </div>
          </div>

          <div class="flex items-center gap-4">
            <div class="flex items-center gap-2 text-sm text-gray-500">
              <el-icon><Calendar /></el-icon>
              {{ currentDate }}
            </div>
            
            <button
              class="bg-purple-100 p-2 rounded-lg hover:bg-purple-200 transition-colors"
              @click="refreshData"
            >
              <el-icon class="text-purple-600"><Refresh /></el-icon>
            </button>
          </div>
        </div>
      </header>



      <!-- 商家列表 -->
      <div class="space-y-6">
        <div
          v-if="merchants.length === 0 && !loading"
          class="text-center py-16 bg-white rounded-xl shadow-sm"
        >
          <el-icon class="text-5xl text-gray-300 mb-4"><Box /></el-icon>
          <p class="text-gray-500">暂无商家数据</p>
          <button
            @click="fetchMerchants"
            class="mt-4 px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            刷新数据
          </button>
        </div>

        <transition-group name="merchant-list">
          <div
            v-for="merchant in merchants"
            :key="merchant.shopId"
            class="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden group"
          >
            <div class="p-6">
              <!-- 头部信息 -->
              <div
                class="flex flex-col md:flex-row md:justify-between md:items-center pb-4 border-b border-gray-100 mb-6 gap-4"
              >
                <div class="flex items-center gap-4">
                  <div
                    class="bg-purple-100 p-3 rounded-lg group-hover:scale-110 transition-transform"
                  >
                    <el-icon class="text-2xl text-purple-600"><Shop /></el-icon>
                  </div>
                  <div>
                    <div class="flex items-center gap-2">
                      <h3 class="text-xl font-semibold text-gray-800">{{ merchant.shopName }}</h3>
                      <span
                        :class="[
                          'px-2 py-0.5 rounded-full text-xs font-medium',
                          merchant.status === 0
                            ? 'bg-yellow-100 text-yellow-800'
                            : merchant.status === 1
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800',
                        ]"
                      >
                        {{
                          merchant.status === 0
                            ? '待审核'
                            : merchant.status === 1
                              ? '已通过'
                              : '已拒绝'
                        }}
                      </span>
                    </div>
                    <p class="text-gray-500 text-sm mt-1">
                      申请时间: {{ formatDate(merchant.createTime) }}
                    </p>
                  </div>
                </div>
                <div class="flex gap-3">
                  <button
                    @click="handleApprove(merchant)"
                    class="px-6 py-2.5 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 text-sm font-medium shadow-sm flex items-center gap-2"
                  >
                    <el-icon><CircleCheck /></el-icon>
                    通过审核
                  </button>
                  <button
                    @click="handleReject(merchant)"
                    class="px-6 py-2.5 bg-white border border-red-500 text-red-500 rounded-lg hover:bg-red-50 transition-all duration-300 text-sm font-medium flex items-center gap-2"
                  >
                    <el-icon><CircleClose /></el-icon>
                    拒绝
                  </button>
                </div>
              </div>

              <!-- 商家详细信息 -->
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div
                  class="flex items-center bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <el-icon class="text-gray-400 mr-3"><Calendar /></el-icon>
                  <span class="text-gray-500 w-24">申请时间</span>
                  <span class="text-gray-800 font-medium">{{
                    formatDate(merchant.createTime)
                  }}</span>
                </div>
                <div
                  class="flex items-center bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <el-icon class="text-gray-400 mr-3"><User /></el-icon>
                  <span class="text-gray-500 w-24">联系人</span>
                  <span class="text-gray-800 font-medium">{{ merchant.contactName }}</span>
                </div>
                <div
                  class="flex items-center bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <el-icon class="text-gray-400 mr-3"><Phone /></el-icon>
                  <span class="text-gray-500 w-24">联系电话</span>
                  <span class="text-gray-800 font-medium">{{ merchant.mobile }}</span>
                </div>
                <div
                  class="flex items-center bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <el-icon class="text-gray-400 mr-3"><InfoFilled /></el-icon>
                  <span class="text-gray-500 w-24">店铺ID</span>
                  <span class="text-gray-800 font-medium">{{ merchant.shopId }}</span>
                </div>
                <div
                  class="flex items-center bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors lg:col-span-2"
                >
                  <el-icon class="text-gray-400 mr-3"><Location /></el-icon>
                  <span class="text-gray-500 w-24">店铺地址</span>
                  <span class="text-gray-800 font-medium">
                    {{ merchant.province }} {{ merchant.city }} {{ merchant.detailAddress }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </transition-group>
      </div>

      <!-- 分页 -->
      <div class="mt-8 flex justify-center">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[5, 10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>

      <!-- 加载状态 -->
      <div
        v-if="loading"
        class="fixed inset-0 bg-white bg-opacity-80 flex items-center justify-center z-50"
      >
        <div class="text-center p-8 bg-white rounded-xl shadow-lg">
          <div class="relative w-20 h-20 mx-auto mb-4">
            <div
              class="absolute inset-0 border-4 border-t-purple-600 border-r-purple-300 border-b-purple-100 border-l-purple-300 rounded-full animate-spin"
            ></div>
            <el-icon
              class="absolute inset-0 flex items-center justify-center text-3xl text-purple-600"
            >
              <Loading />
            </el-icon>
          </div>
          <p class="text-gray-600">正在加载商家数据...</p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div
        v-if="error"
        class="fixed inset-0 bg-white bg-opacity-80 flex items-center justify-center z-50"
      >
        <div class="text-center p-8 bg-white rounded-xl shadow-lg max-w-md">
          <el-icon class="text-5xl text-red-600 mb-4">
            <CircleClose />
          </el-icon>
          <p class="text-red-600 font-medium text-lg mb-2">获取数据失败</p>
          <p class="text-gray-600 mb-6">{{ error }}</p>
          <button
            @click="fetchMerchants"
            class="px-6 py-3 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
          >
            重试
          </button>
        </div>
      </div>

      <!-- 成功提示 -->
      <div
        v-if="success"
        class="fixed bottom-8 right-8 bg-green-600 text-white px-6 py-4 rounded-lg shadow-lg flex items-center gap-3 z-50 animate-slide-up"
      >
        <el-icon class="text-2xl"><CircleCheck /></el-icon>
        <span>{{ successMessage }}</span>
        <button @click="success = false" class="ml-4 text-white hover:text-green-200">
          <el-icon><Close /></el-icon>
        </button>
      </div>

      <!-- 审核弹窗 -->
      <el-dialog
        v-model="showModal"
        :title="isApproving ? '确认通过审核' : '确认拒绝审核'"
        width="400px"
        class="custom-dialog"
        destroy-on-close
      >
        <div class="space-y-4">
          <!-- 通过审核的提示 -->
          <div v-if="isApproving" class="bg-yellow-50 p-4 rounded-lg">
            <div class="flex items-start gap-2">
              <el-icon class="text-yellow-600 mt-0.5"><Warning /></el-icon>
              <p class="text-sm text-yellow-700">
                通过审核后，该商家将获得平台入驻资格。请确认已仔细核对商家资料。
              </p>
            </div>
          </div>

          <!-- 拒绝审核的提示 -->
          <div v-else class="bg-red-50 p-4 rounded-lg">
            <div class="flex items-start gap-2">
              <el-icon class="text-red-600 mt-0.5"><Warning /></el-icon>
              <p class="text-sm text-red-700">
                拒绝审核后，该商家的申请将被删除。此操作不可撤销，请谨慎操作。
              </p>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end gap-3">
            <el-button @click="closeModal">取消</el-button>
            <el-button
              :type="isApproving ? 'primary' : 'danger'"
              :loading="submitting"
              @click="isApproving ? submitReview() : handleReject(selectedMerchant)"
            >
              {{ isApproving ? '确认通过' : '确认拒绝' }}
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 图片预览弹窗 -->
      <el-dialog v-model="showImagePreview" title="图片预览" width="800px" class="custom-dialog">
        <div class="flex items-center justify-center">
          <img :src="previewImage" alt="预览图片" class="max-w-full max-h-[70vh] object-contain" />
        </div>
      </el-dialog>

      <!-- 商家详情弹窗 -->
      <el-dialog
        v-model="showDetailsModal"
        title="商家详细信息"
        width="800px"
        class="custom-dialog"
      >
        <div v-if="selectedMerchant" class="space-y-6">
          <div class="flex items-center gap-4">
            <div class="bg-purple-100 p-4 rounded-full">
              <el-icon class="text-3xl text-purple-600"><Shop /></el-icon>
            </div>
            <div>
              <h3 class="text-xl font-bold text-gray-800">{{ selectedMerchant.shopName }}</h3>
              <p class="text-gray-500">店铺ID: {{ selectedMerchant.shopId }}</p>
            </div>
            <span
              :class="[
                'ml-auto px-3 py-1 rounded-full text-sm font-medium',
                selectedMerchant.status === 0
                  ? 'bg-yellow-100 text-yellow-800'
                  : selectedMerchant.status === 1
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800',
              ]"
            >
              {{
                selectedMerchant.status === 0
                  ? '待审核'
                  : selectedMerchant.status === 1
                    ? '已通过'
                    : '已拒绝'
              }}
            </span>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 class="font-medium text-gray-700 mb-3 flex items-center gap-2">
                <el-icon><InfoFilled /></el-icon>
                基本信息
              </h4>
              <div class="space-y-3">
                <div class="flex bg-gray-50 p-3 rounded-lg">
                  <span class="text-gray-500 w-24">联系人</span>
                  <span class="text-gray-800">{{ selectedMerchant.contactName }}</span>
                </div>
                <div class="flex bg-gray-50 p-3 rounded-lg">
                  <span class="text-gray-500 w-24">联系电话</span>
                  <span class="text-gray-800">{{ selectedMerchant.mobile }}</span>
                </div>
                <div class="flex bg-gray-50 p-3 rounded-lg">
                  <span class="text-gray-500 w-24">店铺地址</span>
                  <span class="text-gray-800">
                    {{ selectedMerchant.province }} {{ selectedMerchant.city }}
                    {{ selectedMerchant.detailAddress }}
                  </span>
                </div>
                <div class="flex bg-gray-50 p-3 rounded-lg">
                  <span class="text-gray-500 w-24">申请时间</span>
                  <span class="text-gray-800">{{ formatDate(selectedMerchant.createTime) }}</span>
                </div>
              </div>
            </div>

            <div>
              <h4 class="font-medium text-gray-700 mb-3 flex items-center gap-2">
                <el-icon><Document /></el-icon>
                资质文件
              </h4>
              <div class="grid grid-cols-2 gap-4">
                <div class="bg-gray-50 p-4 rounded-lg">
                  <p class="text-sm text-gray-500 mb-2">身份证正反面</p>
                  <div
                    class="aspect-[3/4] bg-gray-100 rounded-lg overflow-hidden flex items-center justify-center"
                  >
                    <img
                      src="/placeholder.svg?height=300&width=200&text=身份证"
                      alt="身份证"
                      class="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                      @click="viewImage('/placeholder.svg?height=800&width=600&text=身份证')"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h4 class="font-medium text-gray-700 mb-3 flex items-center gap-2">
              <el-icon><PictureFilled /></el-icon>
              店铺照片
            </h4>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div
                v-for="i in 4"
                :key="i"
                class="aspect-square bg-gray-100 rounded-lg overflow-hidden flex items-center justify-center"
              >
                <img
                  :src="`/placeholder.svg?height=200&width=200&text=店铺照片${i}`"
                  alt="店铺照片"
                  class="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                  @click="viewImage(`/placeholder.svg?height=800&width=800&text=店铺照片${i}`)"
                />
              </div>
            </div>
          </div>

          <div>
            <h4 class="font-medium text-gray-700 mb-3 flex items-center gap-2">
              <el-icon><ChatDotRound /></el-icon>
              店铺简介
            </h4>
            <div class="bg-gray-50 p-4 rounded-lg">
              <p class="text-gray-600">
                {{ selectedMerchant.description || '该商家暂无店铺简介' }}
              </p>
            </div>
          </div>

          <div class="flex justify-end gap-3 pt-4 border-t border-gray-100">
            <el-button @click="showDetailsModal = false">关闭</el-button>
            <el-button type="primary" @click="handleApprove(selectedMerchant)">
              通过审核
            </el-button>
            <el-button type="danger" @click="handleReject(selectedMerchant)"> 拒绝申请 </el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import {
  Loading,
  CircleClose,
  CircleCheck,
  Search,
  Shop,
  User,
  Phone,
  Location,
  InfoFilled,
  Document,
  View,
  Calendar,
  Refresh,
  ArrowDown,
  Close,
  Warning,
  Timer,
  Box,
  Goods,
  PictureFilled,
  ChatDotRound,
} from '@element-plus/icons-vue'
import request from '@/utils/request'
import { ElMessage } from 'element-plus'

// 定义商家数据类型
interface Merchant {
  shopId: number
  shopName: string
  contactName: string
  mobile: string
  province: string
  city: string
  detailAddress: string
  status: number
  createTime: Date
}

// 状态变量
const merchants = ref<Merchant[]>([])
const searchQuery = ref('')
const filterStatus = ref('')
const showModal = ref(false)
const reviewComment = ref('')
const rejectReason = ref('')
const isApproving = ref(false)
const selectedMerchant = ref<Merchant | null>(null)
const loading = ref(true)
const submitting = ref(false)
const error = ref('')
const success = ref(false)
const successMessage = ref('')
const showAdvancedFilters = ref(false)
const dateRange = ref([])
const regionFilter = ref([])
const expandedMerchants = ref<number[]>([])
const showImagePreview = ref(false)
const previewImage = ref('')
const showDetailsModal = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(100)

// 计算属性
const currentDate = computed(() => {
  const now = new Date()
  return now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long',
  })
})

const pendingCount = computed(() => {
  return merchants.value.filter((m) => m.status === 0).length
})

const processedCount = computed(() => {
  return merchants.value.filter((m) => m.status !== 0).length
})

// 模拟统计数据
const stats = ref({
  today: 12,
  approved: 8,
  rejected: 2,
  avgTime: 15,
})

// 模拟地区选项
const regionOptions = [
  {
    value: 'beijing',
    label: '北京',
    children: [
      { value: 'chaoyang', label: '朝阳区' },
      { value: 'haidian', label: '海淀区' },
    ],
  },
  {
    value: 'shanghai',
    label: '上海',
    children: [
      { value: 'pudong', label: '浦东新区' },
      { value: 'huangpu', label: '黄浦区' },
    ],
  },
]

// 获取商家数据
const fetchMerchants = async () => {
  loading.value = true
  error.value = ''

  try {
    const response = await request.get('/MerChant/review', {
      params: {
        page: currentPage.value,
        pageSize: pageSize.value,
        status: filterStatus.value,
        keyword: searchQuery.value,
        // 其他筛选参数
      },
    })

    if (response.data.code === 1) {
      merchants.value = response.data.data || []
      totalItems.value = response.data.total || merchants.value.length

      // 为演示添加一些模拟数据
      merchants.value.forEach((merchant) => {
        merchant.createTime = merchant.createTime || new Date()
      })
    } else {
      error.value = response.data.msg || '获取数据失败'
    }
  } catch (err) {
    console.error('获取商家数据失败:', err)
    error.value = '获取数据失败，请稍后再试'
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchMerchants()
}

// 重置筛选条件
const resetFilters = () => {
  searchQuery.value = ''
  filterStatus.value = ''
  dateRange.value = []
  regionFilter.value = []
  showAdvancedFilters.value = false
  fetchMerchants()
}

// 刷新数据
const refreshData = () => {
  fetchMerchants()
  ElMessage({
    message: '数据已刷新',
    type: 'success',
  })
}

// 处理审核
const handleApprove = (merchant: Merchant) => {
  selectedMerchant.value = merchant
  isApproving.value = true
  showModal.value = true
  showDetailsModal.value = false
}

// 处理拒绝
const handleReject = async (merchant: Merchant) => {
  try {
    submitting.value = true
    // 使用 DELETE 请求
    const response = await request({
      method: 'delete',
      url: `/MerChant/Delete`,
      params: {
        ShopId: merchant.shopId,
      },
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (response.data.code === 1) {
      // 更新本地数据
      const index = merchants.value.findIndex((m) => m.shopId === merchant.shopId)
      if (index !== -1) {
        merchants.value.splice(index, 1)
      }

      ElMessage.success(response.data.msg || '已拒绝商家申请')

      // 刷新数据
      fetchMerchants()
    } else {
      ElMessage.error(response.data.msg || '操作失败')
    }
  } catch (error) {
    console.error('拒绝操作失败:', error)
    ElMessage.error('操作失败，请稍后再试')
  } finally {
    submitting.value = false
  }
}

const closeModal = () => {
  showModal.value = false
  reviewComment.value = ''
  rejectReason.value = ''
  selectedMerchant.value = null
}

const submitReview = async () => {
  if (!selectedMerchant.value) return
  submitting.value = true

  try {
    // 使用 request.put 并添加必要的请求头
    const response = await request({
      method: 'put',
      url: `/MerChant/change`,
      params: {
        ShopId: selectedMerchant.value.shopId,
      },
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (response.data.code === 1) {
      // 更新本地数据
      const index = merchants.value.findIndex((m) => m.shopId === selectedMerchant.value?.shopId)
      if (index !== -1) {
        merchants.value.splice(index, 1)
      }

      ElMessage.success(response.data.msg || '审核操作成功')
      closeModal()

      // 刷新数据
      fetchMerchants()
    } else {
      ElMessage.error(response.data.msg || '操作失败')
    }
  } catch (error) {
    console.error('审核操作失败:', error)
    ElMessage.error('操作失败，请稍后再试')
  } finally {
    submitting.value = false
  }
}

// 查看营业执照
const viewLicense = (merchant: Merchant) => {
  viewImage('/placeholder.svg?height=800&width=600&text=营业执照')
}

// 查看图片
const viewImage = (url: string) => {
  previewImage.value = url
  showImagePreview.value = true
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchMerchants()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchMerchants()
}

// 格式化日期
const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 监听筛选条件变化
watch([filterStatus], () => {
  currentPage.value = 1
  fetchMerchants()
})

onMounted(() => {
  fetchMerchants()
})
</script>

<style scoped>
.merchant-review {
  background-color: #f9fafb;
}

.custom-dialog :deep(.el-dialog) {
  border-radius: 1rem;
  overflow: hidden;
}

.custom-dialog :deep(.el-dialog__header) {
  margin: 0;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.custom-dialog :deep(.el-dialog__body) {
  padding: 1.5rem;
}

.custom-dialog :deep(.el-dialog__footer) {
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

/* 动画效果 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.bg-gradient-to-r {
  background-size: 200% auto;
  animation: gradient 8s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% center;
  }
  50% {
    background-position: 100% center;
  }
  100% {
    background-position: 0% center;
  }
}

/* 商家列表动画 */
.merchant-list-enter-active,
.merchant-list-leave-active {
  transition: all 0.5s ease;
}

.merchant-list-enter-from,
.merchant-list-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

/* 成功提示动画 */
.animate-slide-up {
  animation: slideUp 0.3s ease-out forwards;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 美化滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c7d2fe;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a5b4fc;
}
</style>
