# ShareWharf 数据库结构总结

## 数据库概览

- **数据库名称**: sharewharf
- **总表数量**: 55个表
- **主要业务模块**: 用户管理、商品管理、订单管理、佣金系统、物流管理、支付系统

## 核心业务表分类

### 1. 用户管理模块

#### 1.1 用户账户表

- **Manager**: 管理员账户

  - 主键: id (bigint, 自增)
  - 唯一账户名: account_name (varchar(100))
  - 密码: password (varchar(255))
  - 创建时间: create_time (timestamp)

- **buyer**: 买家账户

  - 主键: id (bigint, 自增)
  - 唯一账户名: account_name (varchar(100))
  - 个人信息: gender, photo_url, phone, email
  - 账户状态: account_status (tinyint)
  - 邀请码: invitation_code (varchar(50))
  - 时间字段: create_time, last_login_time

- **seller**: 卖家账户
  - 主键: id (bigint, 自增)
  - 基本信息: account_name, password, gender, phone, email
  - 认证信息: certification_info (text)
  - 用户角色: user_role (enum: 超级管理员/管理员/普通用户)
  - 子账户标识: is_sub_account
  - 备注: remark (text)

#### 1.2 权限管理表

- **sys_role**: 系统角色
- **sys_permission**: 系统权限
- **sys_role_permission**: 角色权限关联
- **seller_role**: 卖家角色
- **seller_permission**: 卖家权限

#### 1.3 地址管理表

- **address**: 地址字典表

  - 主键: id (int, 自增)
  - 地址编码: code (bigint)
  - 父级编码: parent_code (bigint)
  - 地址名称: name (varchar(255))
  - 地址级别: level (tinyint)

- **address_book**: 地址簿
- **user_address**: 用户地址
- **us_city_dict**: 美国城市字典

### 2. 商品管理模块 (PMS - Product Management System)

#### 2.1 商品核心表

- **pms_product**: 商品主表

  - 主键: id (bigint, 自增)
  - 商品快照ID: product_snapshot_id
  - 品牌ID: brand_id
  - 分类ID: category_id
  - 商品名称: name (varchar(64))
  - 商品图片: pic, album_pics, introduct_pics
  - 发布状态: publish_status
  - 价格: price (decimal(10,2))
  - 库存: inventory
  - 商品详情: detail_html, detail_mobile_html
  - PDF文档: pdf_document
  - 商品属性: product_attr

- **pms_sku**: 商品SKU表
- **pms_product_snapshot**: 商品快照表
- **pms_sku_snapshot**: SKU快照表

#### 2.2 商品分类和品牌

- **pms_brand**: 品牌表
- **pms_product_category**: 商品分类表
- **category**: 分类表
- **product_category**: 产品分类表

#### 2.3 商品相关功能

- **product_favorite**: 商品收藏
- **product_review**: 商品评价
- **view_record**: 浏览记录
- **shopping_cart**: 购物车

### 3. 订单管理模块

#### 3.1 订单核心表

- **orders**: 订单主表

  - 主键: id (bigint, 自增)
  - 订单号: number (varchar(50), 唯一)
  - 买家ID: buyer_id (bigint)
  - 地址ID: address_id
  - 订单状态: status (tinyint)
  - 订单金额: amount (decimal(10,2))
  - 时间字段: order_time, pay_time, ship_time, complete_time, cancel_time
  - 支付方式: pay_method
  - 支付交易ID: payment_transaction_id
  - 退款相关: refund_status, refund_amount, refund_time
  - 物流信息: tracking_number, courier_code, courier_name

- **order_detail**: 订单详情
- **order_log**: 订单日志

#### 3.2 商家订单

- **merchant_order**: 商家订单
- **merchant_product**: 商家商品

### 4. 支付系统模块

- **payment_record**: 支付记录

  - 订单ID: order_id (bigint)
  - 支付方式: payment_method (varchar(250))
  - 支付金额: amount (decimal(10,2))
  - 交易状态: transaction_status
  - 交易时间: transaction_time

- **payment_method**: 支付方式
- **wechat_refund_record**: 微信退款记录

### 5. 佣金系统模块

#### 5.1 佣金核心表

- **commission_record**: 佣金记录

  - 主键: id (varchar(32))
  - 团长信息: leader_id, leader_name
  - 订单信息: order_id, order_amount
  - 佣金信息: commission_amount, commission_type
  - 买家信息: buyer_name
  - 状态和时间: status, create_time, settle_time, withdraw_time

- **commission_settings**: 佣金设置
- **commission_leaders**: 佣金团长
- **commission_leader_team_members**: 团长团队成员

#### 5.2 团队管理

- **team_leader**: 团队长
- **team_member**: 团队成员

### 6. 物流管理模块

- **logistics**: 物流公司
- **logistics_info**: 物流信息
- **shipping_method**: 配送方式

### 7. 售后服务模块

- **refund_application**: 退款申请
- **refund_approval_record**: 退款审批记录
- **return_record**: 退货记录

### 8. 商家管理模块

- **merchant**: 商家信息

  - 主键: shop_id (bigint, 自增)
  - 店铺名称: shop_name (varchar(100))
  - 联系方式: mobile (varchar(20), 唯一)
  - 联系人: contact_name (varchar(50))
  - 地址信息: province, city, detail_address
  - 状态: status (tinyint)

- **store**: 店铺信息
- **shop_info**: 店铺详情

### 9. 消息系统模块

- **message**: 消息表
- **message_recipient**: 消息接收者
- **customer_service_message**: 客服消息

### 10. 系统管理模块

- **admin_account**: 管理员账户
- **admin_account_log**: 管理员账户日志
- **gen_table**: 代码生成表
- **gen_table_column**: 代码生成表字段
- **sales_trend**: 销售趋势

## 数据库设计特点

1. **模块化设计**: 按业务模块清晰分离，便于维护和扩展
2. **快照机制**: 商品和SKU都有快照表，保证历史数据完整性
3. **多角色支持**: 支持管理员、买家、卖家等多种角色
4. **完整的订单流程**: 从下单到支付、发货、完成的完整流程
5. **佣金分销系统**: 支持团长分销模式
6. **权限管理**: 基于角色的权限控制系统
7. **地址管理**: 支持多级地址结构
8. **物流跟踪**: 完整的物流信息管理

## 主要业务流程

1. **用户注册登录** → buyer/seller表
2. **商品发布** → pms_product → pms_sku
3. **下单购买** → orders → order_detail
4. **支付处理** → payment_record
5. **物流配送** → logistics_info
6. **佣金结算** → commission_record
7. **售后服务** → refund_application

## 完整表列表

### 用户相关表 (11个)

- Manager, buyer, seller
- sys_role, sys_permission, sys_role_permission
- seller_role, seller_permission
- address, address_book, user_address, us_city_dict

### 商品相关表 (12个)

- pms_product, pms_sku, pms_product_snapshot, pms_sku_snapshot
- pms_brand, pms_product_category, category, product_category
- product_favorite, product_review, view_record, shopping_cart

### 订单相关表 (6个)

- orders, order_detail, order_log
- merchant_order, merchant_product
- product_address

### 支付相关表 (3个)

- payment_record, payment_method, wechat_refund_record

### 佣金相关表 (6个)

- commission_record, commission_settings, commission_leaders
- commission_leader_team_members, team_leader, team_member

### 物流相关表 (3个)

- logistics, logistics_info, shipping_method

### 售后相关表 (3个)

- refund_application, refund_approval_record, return_record

### 商家相关表 (3个)

- merchant, store, shop_info

### 消息相关表 (3个)

- message, message_recipient, customer_service_message

### 系统管理表 (5个)

- admin_account, admin_account_log
- gen_table, gen_table_column, sales_trend

## 总结

这是一个功能完整的电商平台数据库，支持：

- **B2C电商**: 买家购买商品的完整流程
- **分销系统**: 团长佣金分销模式
- **多商家**: 支持多个商家入驻
- **完整售后**: 退款、退货等售后服务
- **权限管理**: 多角色权限控制
- **物流跟踪**: 完整的物流信息管理

数据库设计规范，表结构清晰，能够支撑大型电商平台的业务需求。
