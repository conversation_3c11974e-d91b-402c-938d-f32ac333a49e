<template>
  <div class="max-w-4xl mx-auto p-6">
    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">个人信息</h2>
        <div class="space-x-2">
          <el-button
            type="primary"
            :icon="dashboardIcons.EditIcon"
            @click="startEdit"
            v-if="!isEditing"
          >
            编辑资料
          </el-button>
          <el-button
            type="danger"
            :icon="dashboardIcons.DeleteIcon"
            @click="confirmDelete"
            v-if="!isEditing"
          >
            删除账户
          </el-button>
        </div>
      </div>

      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="max-w-lg">
        <el-form-item label="账号名" prop="accountName">
          <el-input v-model="form.accountName" :disabled="true" />
        </el-form-item>

        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="form.gender" :disabled="!isEditing">
            <el-radio label="男">男</el-radio>
            <el-radio label="女">女</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" :disabled="!isEditing" />
        </el-form-item>

        <el-form-item label="电子邮箱" prop="email">
          <el-input v-model="form.email" :disabled="!isEditing" />
        </el-form-item>

        <el-form-item label="账户状态">
          <el-tag :type="userInfo.accountStatus === 1 ? 'success' : 'danger'">
            {{ userInfo.accountStatus === 1 ? '正常' : '已停用' }}
          </el-tag>
        </el-form-item>

        <el-form-item label="创建时间">
          <span>{{ formatDate(userInfo.createTime) }}</span>
        </el-form-item>

        <el-form-item label="最后登录">
          <span>{{ formatDate(userInfo.lastLoginTime) }}</span>
        </el-form-item>

        <el-form-item v-if="isEditing">
          <el-button type="primary" @click="handleSubmit" :loading="loading"> 保存修改 </el-button>
          <el-button @click="cancelEdit" :disabled="loading"> 取消 </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-dialog v-model="showDeleteConfirm" title="删除账户" width="400px">
      <div class="text-red-600 mb-4">警告：此操作将永久删除您的账户，且无法恢复！</div>
      <div class="text-gray-600">请输入您的账号名以确认删除：</div>
      <el-input v-model="deleteConfirmInput" placeholder="请输入账号名" class="mt-2" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDeleteConfirm = false">取消</el-button>
          <el-button
            type="danger"
            :loading="deleteLoading"
            :disabled="deleteConfirmInput !== userInfo.accountName"
            @click="handleDelete"
          >
            确认删除
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { dashboardIcons } from '@/components/icons/dashboard'
import { buyerApi } from '@/api'
import type { BuyerDTO, BuyerInfoVO } from '@/api/buyer'
import { useRouter } from 'vue-router'

const router = useRouter()
const formRef = ref<FormInstance>()
const loading = ref(false)
const isEditing = ref(false)
const userInfo = ref<BuyerInfoVO>({} as BuyerInfoVO)
const showDeleteConfirm = ref(false)
const deleteConfirmInput = ref('')
const deleteLoading = ref(false)

const form = reactive<BuyerDTO>({
  id: undefined,
  accountName: '',
  gender: '男',
  phone: '',
  email: '',
})

const rules: FormRules = {
  accountName: [
    { required: true, message: '请输入账号名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' },
  ],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入电子邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString()
}

// 获取用户信息
const getUserInfo = async () => {
  const userStr = localStorage.getItem('user')
  if (!userStr) return

  userInfo.value = JSON.parse(userStr) as BuyerInfoVO

  // 填充表单
  form.id = userInfo.value.id
  form.accountName = userInfo.value.accountName
  form.gender = userInfo.value.gender
  form.phone = userInfo.value.phone
  form.email = userInfo.value.email
}

// 开始编辑
const startEdit = () => {
  isEditing.value = true
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
  getUserInfo() // 重置表单数据
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const response = await buyerApi.updateInfo(form)
        if (response?.data?.code === 1) {
          // 更新本地存储的用户信息
          const newUserInfo = response.data.data
          localStorage.setItem('user', JSON.stringify(newUserInfo))
          userInfo.value = newUserInfo
          ElMessage.success('修改成功')
          isEditing.value = false
        } else {
          ElMessage.error(response?.data?.msg || '修改失败')
        }
      } catch (error) {
        console.error('Update error:', error)
        ElMessage.error('修改失败，请重试')
      } finally {
        loading.value = false
      }
    }
  })
}

// 确认删除
const confirmDelete = () => {
  showDeleteConfirm.value = true
  deleteConfirmInput.value = ''
}

// 删除账户
const handleDelete = async () => {
  if (!userInfo.value.id) return

  deleteLoading.value = true
  try {
    const response = await buyerApi.deleteAccount(userInfo.value.id)
    if (response?.data?.code === 1) {
      ElMessage.success('账户已删除')
      // 清除登录信息
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      localStorage.removeItem('tokenTimestamp')
      // 跳转到登录页
      router.push('/login')
    } else {
      ElMessage.error(response?.data?.msg || '删除失败')
    }
  } catch (error) {
    console.error('Delete error:', error)
    ElMessage.error('删除失败，请重试')
  } finally {
    deleteLoading.value = false
    showDeleteConfirm.value = false
  }
}

onMounted(() => {
  getUserInfo()
})
</script>
