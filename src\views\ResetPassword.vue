<template>
  <div class="min-h-screen flex items-center justify-center relative overflow-hidden">
    <!-- 主要内容 -->
    <div class="w-full max-w-md relative z-10 p-6">
      <!-- 玻璃态卡片 -->
      <div
        class="backdrop-blur-lg bg-white/40 rounded-3xl shadow-2xl p-8 space-y-8 border border-white/20"
      >
        <!-- Logo和标题 -->
        <div class="text-center space-y-4">
          <div class="w-20 h-20 mx-auto">
            <img src="@/assets/logo1.jpg" alt="Logo" class="w-full h-full object-contain" />
          </div>
          <div>
            <h2
              class="text-3xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent"
            >
              重置密码
            </h2>
            <p class="mt-2 text-gray-600">请设置您的新密码</p>
          </div>
        </div>

        <!-- 重置密码表单 -->
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- 新密码 -->
          <div class="space-y-2">
            <label for="newPassword" class="block text-sm font-medium text-gray-700">新密码</label>
            <div class="relative">
              <input
                v-model="form.newPassword"
                id="newPassword"
                :type="showPassword ? 'text' : 'password'"
                required
                class="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ease-in-out"
                :class="{ 'border-red-500 focus:ring-red-500': errors.newPassword }"
                placeholder="请输入新密码"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <EyeIcon v-if="showPassword" class="h-5 w-5" />
                <EyeOffIcon v-else class="h-5 w-5" />
              </button>
              <div v-if="errors.newPassword" class="absolute -bottom-6 left-0 text-sm text-red-500">
                {{ errors.newPassword }}
              </div>
            </div>
            <div class="mt-2 text-sm bg-white/60 rounded-lg p-3">
              <div class="flex items-center justify-between mb-2">
                <span class="text-gray-600">密码强度:</span>
                <span :class="strengthColorClass">{{ strengthText }}</span>
              </div>
              <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                <div
                  class="h-full transition-all duration-300"
                  :class="strengthBarColorClass"
                  :style="{ width: `${(passwordStrength / 4) * 100}%` }"
                ></div>
              </div>
              <ul class="mt-2 text-gray-500 space-y-1 text-xs">
                <li :class="{ 'text-green-600': hasMinLength }">✓ 至少8个字符</li>
                <li :class="{ 'text-green-600': hasUpperCase }">✓ 至少包含一个大写字母</li>
                <li :class="{ 'text-green-600': hasLowerCase }">✓ 至少包含一个小写字母</li>
                <li :class="{ 'text-green-600': hasNumber }">✓ 至少包含一个数字</li>
              </ul>
            </div>
          </div>

          <!-- 确认新密码 -->
          <div class="space-y-2">
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700"
              >确认新密码</label
            >
            <div class="relative">
              <input
                v-model="form.confirmPassword"
                id="confirmPassword"
                :type="showConfirmPassword ? 'text' : 'password'"
                required
                class="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ease-in-out"
                :class="{ 'border-red-500 focus:ring-red-500': errors.confirmPassword }"
                placeholder="请再次输入新密码"
              />
              <button
                type="button"
                @click="showConfirmPassword = !showConfirmPassword"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <EyeIcon v-if="showConfirmPassword" class="h-5 w-5" />
                <EyeOffIcon v-else class="h-5 w-5" />
              </button>
              <div
                v-if="errors.confirmPassword"
                class="absolute -bottom-6 left-0 text-sm text-red-500"
              >
                {{ errors.confirmPassword }}
              </div>
            </div>
          </div>

          <!-- 提交按钮 -->
          <button
            type="submit"
            :disabled="loading"
            class="w-full py-3 px-4 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <div class="flex items-center justify-center">
              <span v-if="loading" class="w-5 h-5 mr-2 animate-spin">
                <svg viewBox="0 0 24 24" fill="none" class="w-5 h-5">
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              </span>
              <span>{{ loading ? '提交中...' : '重置密码' }}</span>
            </div>
          </button>

          <!-- 返回登录 -->
          <div class="text-center mt-6">
            <p class="text-sm text-gray-600">
              已想起密码？
              <router-link
                to="/login"
                class="text-purple-600 hover:text-purple-700 font-medium transition-colors"
              >
                返回登录
              </router-link>
            </p>
          </div>
        </form>

        <!-- 成功提示弹窗 -->
        <el-dialog
          v-model="showSuccessModal"
          :show-close="false"
          width="460px"
          :modal="true"
          :close-on-click-modal="false"
          class="reset-success-dialog"
          align-center
        >
          <div class="success-container">
            <!-- 成功图标动画 -->
            <div class="success-animation mb-8">
              <div class="success-circle">
                <div class="success-icon">
                  <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                    <circle class="checkmark-circle" cx="26" cy="26" r="25" fill="none" />
                    <path class="checkmark-check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                  </svg>
                </div>
              </div>
            </div>

            <!-- 成功文本 -->
            <h2 class="text-3xl font-bold text-gray-800 mb-4">密码重置成功!</h2>
            <p class="text-gray-600 text-lg mb-8">
              您的密码已经成功重置，请使用新密码登录您的账户。
            </p>

            <!-- 登录按钮 -->
            <button
              @click="goToLogin"
              class="w-full max-w-sm mx-auto py-4 px-6 bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-lg font-medium rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
            >
              <span class="flex items-center justify-center">
                <svg
                  class="w-6 h-6 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
                  ></path>
                </svg>
                立即登录
              </span>
            </button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { EyeIcon, EyeOffIcon } from 'lucide-vue-next'
import request from '@/utils/request'
import type { ResetPasswordRequest, ResetPasswordResponse, ApiResponse } from '@/types/account'

const router = useRouter()
const route = useRoute()
const loading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const showSuccessModal = ref(false)

// 从路由参数中获取用户名、邮箱和token
const userName = ref((route.query.userName as string) || '')
const address = ref((route.query.address as string) || '')
const userId = ref(route.query.userId ? Number(route.query.userId) : undefined)

// 表单数据
const form = reactive({
  newPassword: '',
  confirmPassword: '',
})

// 表单错误信息
const errors = reactive({
  newPassword: '',
  confirmPassword: '',
})

// 密码强度相关的响应式变量
const passwordStrength = ref(0)
const strengthText = ref('弱')
const strengthColorClass = ref('text-red-500')
const strengthBarColorClass = ref('bg-red-500')

// 密码验证状态
const hasMinLength = ref(false)
const hasUpperCase = ref(false)
const hasLowerCase = ref(false)
const hasNumber = ref(false)

// 检查参数是否完整
onMounted(() => {
  if (!userName.value || !address.value) {
    ElMessage.error('参数不完整，请重新验证身份')
    router.push('/forgot-account')
  }
})

// 检查密码强度
watch(
  () => form.newPassword,
  (newValue) => {
    let strength = 0

    // 检查长度
    hasMinLength.value = newValue.length >= 8
    if (hasMinLength.value) strength++

    // 检查是否包含大写字母
    hasUpperCase.value = /[A-Z]/.test(newValue)
    if (hasUpperCase.value) strength++

    // 检查是否包含小写字母
    hasLowerCase.value = /[a-z]/.test(newValue)
    if (hasLowerCase.value) strength++

    // 检查是否包含数字
    hasNumber.value = /\d/.test(newValue)
    if (hasNumber.value) strength++

    // 更新密码强度
    passwordStrength.value = strength

    // 更新强度文本和颜色
    switch (strength) {
      case 0:
      case 1:
        strengthText.value = '弱'
        strengthColorClass.value = 'text-red-500'
        strengthBarColorClass.value = 'bg-red-500'
        break
      case 2:
      case 3:
        strengthText.value = '中'
        strengthColorClass.value = 'text-yellow-500'
        strengthBarColorClass.value = 'bg-yellow-500'
        break
      case 4:
        strengthText.value = '强'
        strengthColorClass.value = 'text-green-500'
        strengthBarColorClass.value = 'bg-green-500'
        break
    }
  },
)

// 验证表单
const validateForm = () => {
  let isValid = true
  errors.newPassword = ''
  errors.confirmPassword = ''

  // 验证新密码
  if (!form.newPassword) {
    errors.newPassword = '请输入新密码'
    isValid = false
  } else if (form.newPassword.length < 8) {
    errors.newPassword = '密码长度至少为8个字符'
    isValid = false
  } else if (!/[A-Z]/.test(form.newPassword)) {
    errors.newPassword = '密码必须包含至少一个大写字母'
    isValid = false
  } else if (!/[a-z]/.test(form.newPassword)) {
    errors.newPassword = '密码必须包含至少一个小写字母'
    isValid = false
  } else if (!/\d/.test(form.newPassword)) {
    errors.newPassword = '密码必须包含至少一个数字'
    isValid = false
  }

  // 验证确认密码
  if (!form.confirmPassword) {
    errors.confirmPassword = '请确认新密码'
    isValid = false
  } else if (form.confirmPassword !== form.newPassword) {
    errors.confirmPassword = '两次输入的密码不一致'
    isValid = false
  }

  return isValid
}

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true
  try {
    const requestData = {
      password: form.newPassword,
      email: address.value,
    }

    console.log('重置密码请求数据:', requestData) // 添加日志

    const response = await request<ApiResponse<null>>({
      method: 'put',
      url: '/buyer/change',
      data: requestData,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (response.data.code === 1) {
      // 重置成功
      showSuccessModal.value = true
    } else {
      // 重置失败
      ElMessage.error(response.data.msg || '密码重置失败，请重试')
    }
  } catch (error) {
    console.error('密码重置失败:', error)

    // 安全地获取错误消息
    let errMsg = '密码重置失败，请重试'
    if (error && typeof error === 'object' && 'response' in error) {
      const response = error.response
      if (response && typeof response === 'object' && 'data' in response) {
        const data = response.data
        if (data && typeof data === 'object' && 'msg' in data) {
          errMsg = data.msg as string
        }
      }
    }

    ElMessage.error(errMsg)
  } finally {
    loading.value = false
  }
}

// 跳转到登录页面
const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
/* 自定义样式 */

/* 成功弹窗样式 */
.reset-success-dialog :deep(.el-dialog) {
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.98));
  backdrop-filter: blur(20px);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.5);
  border: none;
  padding: 0;
}

.reset-success-dialog :deep(.el-dialog__header) {
  display: none;
}

.reset-success-dialog :deep(.el-dialog__body) {
  padding: 0;
  margin: 0;
}

.success-container {
  padding: 3rem 2rem;
  text-align: center;
}

/* 成功动画样式 */
.success-animation {
  position: relative;
  width: 100px;
  height: 100px;
  margin: 0 auto;
}

.checkmark {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: block;
  stroke-width: 3;
  stroke: #7c3aed;
  stroke-miterlimit: 10;
  margin: 0 auto;
  box-shadow: 0 0 30px rgba(124, 58, 237, 0.2);
  animation: scale 0.5s ease-in-out 0.3s forwards;
}

.checkmark-circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 3;
  stroke-miterlimit: 10;
  stroke: #7c3aed;
  fill: none;
  animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

.checkmark-check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  stroke-width: 3;
  animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
}

@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes scale {
  0%,
  100% {
    transform: none;
  }
  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}
</style>
