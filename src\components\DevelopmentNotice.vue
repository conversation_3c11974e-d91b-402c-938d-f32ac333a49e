<template>
  <div class="development-notice" v-if="isVisible">
    <el-alert
      title="该网站正在开发中，请先注册再登录，登录账号将不定期保留直到正式上线，敬请谅解"
      type="info"
      show-icon
      class="rounded-lg custom-alert"
      :closable="false"
    >
      <template #title>
        <div class="flex justify-between items-center w-full pr-2">
          <span class="text-base font-medium text-black">
            该网站正在开发中，请先注册再登录，登录账号将不定期保留直到正式上线，敬请谅解
          </span>
          <button class="close-btn" @click="handleClose">×</button>
        </div>
      </template>
      <template #description>
        <div class="mt-2">
          <span class="font-bold text-black">我们正在不断改进，感谢您的耐心等待！</span>
        </div>
      </template>
    </el-alert>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const isVisible = ref(true)

const handleClose = () => {
  isVisible.value = false
}
</script>

<script lang="ts">
export default {
  name: 'DevelopmentNotice'
}
</script>

<style scoped>
.development-notice {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  width: 400px;
  animation: slideIn 0.5s ease-out;
  border-radius: 8px;
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.custom-alert {
  margin: 0;
  padding: 15px;
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.3), rgba(79, 70, 229, 0.3));
  color: black;
}

.custom-alert :deep(.el-alert__title) {
  font-size: 16px;
  line-height: 1.5;
  margin: 0;
  padding-right: 0;
  color: black;
}

.custom-alert :deep(.el-alert__content) {
  padding: 0;
  color: black;
}

.custom-alert :deep(.el-alert__icon) {
  font-size: 20px;
  margin-right: 12px;
}

.close-btn {
  background: none;
  border: none;
  color: black;
  font-size: 24px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.close-btn:hover {
  color: #dc2626;
}

.el-alert {
  border-color: rgba(255, 0, 0, 0.5);
}
</style>
