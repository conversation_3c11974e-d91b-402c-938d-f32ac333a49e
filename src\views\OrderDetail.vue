<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 py-12 px-4">
    <div class="max-w-5xl mx-auto">
      <!-- 顶部导航和标题 -->
      <div class="mb-8 flex items-center justify-between">
        <div class="flex items-center">
          <button
            @click="goBack"
            class="flex items-center text-blue-600 hover:text-blue-800 transition-colors"
          >
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            <span class="text-sm font-medium">返回订单列表</span>
          </button>
        </div>
        <h1 class="text-3xl font-bold text-slate-800 flex items-center">
          <span class="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600"
            >订单详情</span
          >
        </h1>
        <div class="w-24"></div>
        <!-- 占位元素，保持标题居中 -->
      </div>

      <!-- 加载状态 -->
      <div
        v-if="loading"
        class="bg-white rounded-2xl shadow-xl p-12 flex flex-col items-center justify-center border border-slate-100"
      >
        <div
          class="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-6"
        ></div>
        <p class="text-slate-600 text-lg">正在加载订单详情...</p>
      </div>

      <!-- 错误状态 -->
      <div
        v-else-if="error"
        class="bg-white rounded-2xl shadow-xl p-12 text-center border border-slate-100"
      >
        <div class="text-red-500 text-6xl mb-6">
          <svg class="w-20 h-20 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
        </div>
        <h2 class="text-2xl font-semibold text-slate-800 mb-3">加载失败</h2>
        <p class="text-slate-600 mb-8 max-w-md mx-auto">{{ error }}</p>
        <button
          @click="fetchOrderDetail"
          class="bg-blue-600 text-white px-8 py-3 rounded-xl hover:bg-blue-700 transition-colors shadow-md hover:shadow-lg"
        >
          重新加载
        </button>
      </div>

      <!-- 订单不存在 -->
      <div
        v-else-if="!orderDetail"
        class="bg-white rounded-2xl shadow-xl p-12 text-center border border-slate-100"
      >
        <div class="text-slate-400 text-6xl mb-6">
          <svg class="w-20 h-20 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <h2 class="text-2xl font-semibold text-slate-800 mb-3">订单不存在</h2>
        <p class="text-slate-600 mb-8 max-w-md mx-auto">找不到该订单信息或订单已被删除</p>
        <button
          @click="goBack"
          class="bg-blue-600 text-white px-8 py-3 rounded-xl hover:bg-blue-700 transition-colors shadow-md hover:shadow-lg"
        >
          返回订单列表
        </button>
      </div>

      <!-- 订单详情内容 -->
      <div v-else class="space-y-8">
        <!-- 订单状态卡片 -->
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden border border-slate-100">
          <div class="p-8">
            <div class="flex justify-between items-center">
              <div>
                <h2 class="text-2xl font-bold text-slate-800">订单 #{{ orderDetail.number }}</h2>
                <p class="text-slate-500 mt-2">下单时间：{{ formatDate(orderDetail.orderTime) }}</p>
              </div>
              <div
                :class="[
                  'px-6 py-3 rounded-xl font-medium text-sm',
                  getStatusClass(orderDetail.status),
                ]"
              >
                {{ getStatusText(orderDetail.status) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 商品信息 -->
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden border border-slate-100">
          <div class="border-b border-slate-200">
            <h3 class="text-xl font-semibold p-8 text-slate-800 flex items-center">
              <svg
                class="w-5 h-5 mr-2 text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                />
              </svg>
              商品信息
            </h3>
          </div>
          <div class="divide-y divide-slate-100">
            <div
              v-for="(item, index) in orderDetail.orderDetails"
              :key="index"
              class="p-8 flex items-center hover:bg-slate-50 transition-colors"
            >
              <div
                class="w-24 h-24 flex-shrink-0 bg-slate-100 rounded-xl overflow-hidden shadow-sm"
              >
                <img
                  :src="item.productImage || '/placeholder.svg'"
                  :alt="item.productName"
                  class="w-full h-full object-cover"
                  @error="handleImageError"
                />
              </div>
              <div class="ml-6 flex-1">
                <h4 class="text-lg font-medium text-slate-800">{{ item.productName }}</h4>
                <p v-if="item.productSpec" class="text-sm text-slate-500 mt-2">
                  {{ item.productSpec }}
                </p>
              </div>
              <div class="text-right">
                <p class="text-sm text-slate-500">
                  {{ item.quantity }} × ¥{{ item.unitPrice.toFixed(2) }}
                </p>
                <p class="text-lg font-medium text-slate-800 mt-2">
                  ¥{{ item.subtotal.toFixed(2) }}
                </p>
              </div>
            </div>
          </div>
          <div class="bg-slate-50 p-8">
            <div class="flex justify-between text-sm text-slate-500">
              <span>商品金额</span>
              <span>¥{{ calculateSubtotal().toFixed(2) }}</span>
            </div>
            <div class="flex justify-between text-sm text-slate-500 mt-3">
              <span>运费</span>
              <span>¥{{ shippingFee.toFixed(2) }}</span>
            </div>
            <div
              class="flex justify-between text-xl font-medium text-slate-800 mt-6 pt-6 border-t border-slate-200"
            >
              <span>订单总计</span>
              <span class="text-blue-600">¥{{ orderDetail.amount.toFixed(2) }}</span>
            </div>
          </div>
        </div>

        <!-- 收货信息 -->
        <div
          v-if="orderDetail.address"
          class="bg-white rounded-2xl shadow-lg overflow-hidden border border-slate-100"
        >
          <div class="border-b border-slate-200">
            <h3 class="text-xl font-semibold p-8 text-slate-800 flex items-center">
              <svg
                class="w-5 h-5 mr-2 text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                />
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
              收货信息
            </h3>
          </div>
          <div class="p-8 bg-gradient-to-r from-slate-50 to-white">
            <div class="flex items-start">
              <div class="p-4 bg-blue-50 rounded-xl mr-6">
                <svg
                  class="w-8 h-8 text-blue-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
              </div>
              <div class="flex-1">
                <div class="flex items-center mb-3">
                  <span class="text-xl font-medium text-slate-800 mr-4">{{
                    orderDetail.address.consignee
                  }}</span>
                  <span class="text-slate-600 bg-slate-100 px-3 py-1 rounded-full text-sm">{{
                    orderDetail.address.phone
                  }}</span>
                </div>
                <p class="text-slate-600 text-lg">
                  {{ formatAddress(orderDetail.address) }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 支付信息 -->
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden border border-slate-100">
          <div class="border-b border-slate-200">
            <h3 class="text-xl font-semibold p-8 text-slate-800 flex items-center">
              <svg
                class="w-5 h-5 mr-2 text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                />
              </svg>
              支付信息
            </h3>
          </div>
          <div class="p-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="bg-slate-50 p-6 rounded-xl">
                <p class="text-sm text-slate-500 mb-2">支付方式</p>
                <div class="flex items-center">
                  <div
                    class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3"
                  >
                    <svg
                      class="w-5 h-5 text-blue-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                      />
                    </svg>
                  </div>
                  <p class="text-lg font-medium text-slate-800">
                    {{ getPayMethodText(orderDetail.payMethod) }}
                  </p>
                </div>
              </div>
              <div class="bg-slate-50 p-6 rounded-xl">
                <p class="text-sm text-slate-500 mb-2">支付时间</p>
                <div class="flex items-center">
                  <div
                    class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3"
                  >
                    <svg
                      class="w-5 h-5 text-green-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <p class="text-lg font-medium text-slate-800">
                    {{ orderDetail.payTime ? formatDate(orderDetail.payTime) : '未支付' }}
                  </p>
                </div>
              </div>
              <div v-if="orderDetail.orderRemark" class="bg-slate-50 p-6 rounded-xl md:col-span-2">
                <p class="text-sm text-slate-500 mb-2">订单备注</p>
                <div class="flex items-center">
                  <div
                    class="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center mr-3"
                  >
                    <svg
                      class="w-5 h-5 text-amber-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                      />
                    </svg>
                  </div>
                  <p class="text-lg text-slate-800">{{ orderDetail.orderRemark }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 物流信息 -->
        <div
          v-if="orderDetail.logistics"
          class="bg-white rounded-2xl shadow-lg overflow-hidden border border-slate-100"
        >
          <div class="border-b border-slate-200">
            <h3 class="text-xl font-semibold p-8 text-slate-800 flex items-center">
              <svg
                class="w-5 h-5 mr-2 text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
                />
              </svg>
              物流信息
            </h3>
          </div>
          <div class="p-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="bg-slate-50 p-6 rounded-xl">
                <p class="text-sm text-slate-500 mb-2">物流公司</p>
                <div class="flex items-center">
                  <div
                    class="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-3"
                  >
                    <svg
                      class="w-5 h-5 text-indigo-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z"
                      />
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0"
                      />
                    </svg>
                  </div>
                  <p class="text-lg font-medium text-slate-800">
                    {{ orderDetail.logistics.logisticsCompany }}
                  </p>
                </div>
              </div>
              <div class="bg-slate-50 p-6 rounded-xl">
                <p class="text-sm text-slate-500 mb-2">物流单号</p>
                <div class="flex items-center">
                  <div
                    class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3"
                  >
                    <svg
                      class="w-5 h-5 text-blue-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"
                      />
                    </svg>
                  </div>
                  <p class="text-lg font-medium text-slate-800">
                    {{ orderDetail.logistics.logisticsNumber }}
                  </p>
                </div>
              </div>
              <div class="bg-slate-50 p-6 rounded-xl">
                <p class="text-sm text-slate-500 mb-2">物流状态</p>
                <div class="flex items-center">
                  <div
                    class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3"
                  >
                    <svg
                      class="w-5 h-5 text-green-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <p class="text-lg font-medium text-slate-800">
                    {{ orderDetail.logistics.logisticsStatusDesc }}
                  </p>
                </div>
              </div>
              <div v-if="orderDetail.logistics.shippingDate" class="bg-slate-50 p-6 rounded-xl">
                <p class="text-sm text-slate-500 mb-2">发货时间</p>
                <div class="flex items-center">
                  <div
                    class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3"
                  >
                    <svg
                      class="w-5 h-5 text-purple-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <p class="text-lg font-medium text-slate-800">
                    {{ formatDate(orderDetail.logistics.shippingDate) }}
                  </p>
                </div>
              </div>
            </div>
            <div class="mt-6 flex justify-center">
              <button
                @click="viewLogistics"
                class="flex items-center px-6 py-3 bg-blue-50 hover:bg-blue-100 text-blue-600 rounded-xl transition-colors"
              >
                <span class="font-medium">查看物流详情</span>
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M14 5l7 7m0 0l-7 7m7-7H3"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex flex-wrap justify-end gap-4 mt-10">
          <button
            v-if="orderDetail.status === OrderStatus.PENDING_PAYMENT"
            @click="payOrder"
            class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-3 rounded-xl hover:shadow-lg transition-all transform hover:-translate-y-1"
          >
            <span class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
              立即付款
            </span>
          </button>
          <button
            v-if="orderDetail.status === OrderStatus.SHIPPED"
            @click="confirmOrder"
            class="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-8 py-3 rounded-xl hover:shadow-lg transition-all transform hover:-translate-y-1"
          >
            <span class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
              确认收货
            </span>
          </button>
          <button
            v-if="orderDetail.status === OrderStatus.COMPLETED"
            @click="reviewOrder"
            class="bg-gradient-to-r from-amber-500 to-yellow-500 text-white px-8 py-3 rounded-xl hover:shadow-lg transition-all transform hover:-translate-y-1"
          >
            <span class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                />
              </svg>
              评价订单
            </span>
          </button>
          <button
            v-if="canRefund(orderDetail)"
            @click="applyRefund"
            class="bg-gradient-to-r from-orange-500 to-red-500 text-white px-8 py-3 rounded-xl hover:shadow-lg transition-all transform hover:-translate-y-1"
          >
            <span class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"
                />
              </svg>
              申请退款
            </span>
          </button>
          <button
            v-if="orderDetail.status === OrderStatus.PENDING_PAYMENT"
            @click="cancelOrder"
            class="bg-slate-200 text-slate-700 px-8 py-3 rounded-xl hover:bg-slate-300 transition-colors"
          >
            <span class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
              取消订单
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  orderApi,
  OrderStatus,
  orderDetailApi,
  refundApi,
  type OrderVO,
  type RefundApplicationDTO,
  type RefundCheckResult,
  RefundType,
  RefundMethod,
} from '@/api/order'

const route = useRoute()
const router = useRouter()

// 状态变量
const loading = ref(true)
const error = ref('')
const orderDetail = ref<OrderVO | null>(null)
const shippingFee = ref(0) // 运费，可以根据实际情况调整

// 获取订单详情
const fetchOrderDetail = async () => {
  loading.value = true
  error.value = ''

  try {
    const orderId = Number(route.params.id)
    if (isNaN(orderId)) {
      throw new Error('无效的订单ID')
    }

    // 获取基本订单信息
    const orderResponse = await orderApi.getOrderById(orderId)

    if (orderResponse.data.code === 1) {
      orderDetail.value = orderResponse.data.data

      // 获取详细的订单项信息
      const detailResponse = await orderDetailApi.getOrderDetails(orderId)

      if (detailResponse.data.code === 1 && Array.isArray(detailResponse.data.data)) {
        // 如果订单详情API返回了数据，则更新订单详情
        if (orderDetail.value) {
          orderDetail.value.orderDetails = detailResponse.data.data
        }
      }
    } else {
      throw new Error(orderResponse.data.msg || '获取订单详情失败')
    }
  } catch (err: unknown) {
    console.error('获取订单详情失败:', err)
    const errorMsg = err instanceof Error ? err.message : '获取订单详情失败，请稍后重试'
    error.value = errorMsg
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '未知时间'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

// 格式化地址
const formatAddress = (address: {
  provinceName?: string
  cityName?: string
  districtName?: string
  detail?: string
}) => {
  if (!address) return ''

  const parts = [
    address.provinceName,
    address.cityName,
    address.districtName,
    address.detail,
  ].filter(Boolean)

  return parts.join(' ')
}

// 计算商品小计
const calculateSubtotal = () => {
  if (!orderDetail.value || !orderDetail.value.orderDetails) return 0

  return orderDetail.value.orderDetails.reduce((sum, item) => {
    return sum + (item.subtotal || item.unitPrice * item.quantity)
  }, 0)
}

// 获取订单状态文本
const getStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    [OrderStatus.PENDING_PAYMENT]: '待付款',
    [OrderStatus.PAID]: '已付款',
    [OrderStatus.PROCESSING]: '处理中',
    [OrderStatus.SHIPPED]: '已发货',
    [OrderStatus.COMPLETED]: '已完成',
    [OrderStatus.CANCELLED]: '已取消',
    [OrderStatus.REFUNDED]: '已退款',
  }
  return statusMap[status] || '未知状态'
}

// 获取订单状态样式类
const getStatusClass = (status: number): string => {
  const statusClassMap: Record<number, string> = {
    [OrderStatus.PENDING_PAYMENT]: 'bg-yellow-100 text-yellow-800',
    [OrderStatus.PAID]: 'bg-blue-100 text-blue-800',
    [OrderStatus.PROCESSING]: 'bg-blue-100 text-blue-800',
    [OrderStatus.SHIPPED]: 'bg-purple-100 text-purple-800',
    [OrderStatus.COMPLETED]: 'bg-green-100 text-green-800',
    [OrderStatus.CANCELLED]: 'bg-gray-100 text-gray-800',
    [OrderStatus.REFUNDED]: 'bg-red-100 text-red-800',
  }
  return statusClassMap[status] || 'bg-gray-100 text-gray-800'
}

// 获取支付方式文本
const getPayMethodText = (payMethod: number) => {
  const payMethodMap: Record<number, string> = {
    1: '微信支付',
    2: '支付宝',
    3: '信用卡',
    4: '货到付款',
  }
  return payMethodMap[payMethod] || '其他方式'
}

// 处理图片加载错误
const handleImageError = (e: Event) => {
  const target = e.target as HTMLImageElement
  target.src = '/placeholder.svg'
}

// 返回订单列表
const goBack = () => {
  router.push('/user/orders')
}

// 支付订单
const payOrder = () => {
  if (!orderDetail.value) return

  router.push({
    path: '/payment',
    query: { orderId: orderDetail.value.id.toString() },
  })
}

// 查看物流
const viewLogistics = () => {
  if (!orderDetail.value) return

  router.push(`/logistics/tracking/${orderDetail.value.id}`)
}

// 确认收货
const confirmOrder = async () => {
  if (!orderDetail.value) return

  try {
    await ElMessageBox.confirm('确认已收到商品吗？', '确认收货', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await orderApi.confirmOrder(orderDetail.value.id)
    if (response.data.code === 1) {
      ElMessage.success('已确认收货')
      fetchOrderDetail() // 刷新订单详情
    } else {
      ElMessage.error(response.data.msg || '确认收货失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认收货失败:', error)
      ElMessage.error('确认收货失败，请稍后重试')
    }
  }
}

// 评价订单
const reviewOrder = () => {
  if (!orderDetail.value) return

  router.push(`/review/order/${orderDetail.value.id}`)
}

// 取消订单
const cancelOrder = async () => {
  if (!orderDetail.value) return

  try {
    // 弹出对话框让用户输入取消原因
    const { value: cancelReason } = await ElMessageBox.prompt('请输入取消订单的原因', '取消订单', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      inputPlaceholder: '例如：商品不符合预期',
    })

    // 调用API取消订单
    const response = await orderApi.cancelOrder(orderDetail.value.id, cancelReason)

    if (response.data.code === 1) {
      ElMessage.success('订单已取消')
      fetchOrderDetail() // 刷新订单详情
    } else {
      ElMessage.error(response.data.msg || '取消订单失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订单失败:', error)
      ElMessage.error('取消订单失败，请稍后重试')
    }
  }
}

// 判断订单是否可以退款
const canRefund = (order: OrderVO | null): boolean => {
  if (!order) return false
  // 已付款、待发货、已发货、已完成的订单都可以申请退款
  return (
    order.status === OrderStatus.PAID ||
    order.status === OrderStatus.PROCESSING ||
    order.status === OrderStatus.SHIPPED ||
    order.status === OrderStatus.COMPLETED
  )
}

// 显示优化的退款申请对话框 (复用Orders.vue中的逻辑)
const showRefundDialog = async (
  order: OrderVO,
  checkResult: RefundCheckResult,
  refundForm: {
    refundReason: string
    refundAmount: number
    refundType: number
    refundMethod: number
  },
): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 创建对话框容器
    const dialogContainer = document.createElement('div')
    dialogContainer.innerHTML = `
      <div class="refund-dialog-overlay" style="
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        backdrop-filter: blur(4px);
      ">
        <div class="refund-dialog" style="
          background: white;
          border-radius: 16px;
          padding: 0;
          width: 90%;
          max-width: 500px;
          max-height: 90vh;
          overflow: hidden;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
          animation: dialogSlideIn 0.3s ease-out;
        ">
          <!-- 对话框头部 -->
          <div class="dialog-header" style="
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px;
            text-align: center;
            position: relative;
          ">
            <h2 style="margin: 0; font-size: 20px; font-weight: 600;">申请退款</h2>
            <button class="close-btn" style="
              position: absolute;
              top: 16px;
              right: 16px;
              background: rgba(255, 255, 255, 0.2);
              border: none;
              color: white;
              width: 32px;
              height: 32px;
              border-radius: 50%;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 18px;
              transition: all 0.3s;
            " onmouseover="this.style.background='rgba(255, 255, 255, 0.3)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'">×</button>
          </div>

          <!-- 对话框内容 -->
          <div class="dialog-content" style="
            padding: 24px;
            max-height: 60vh;
            overflow-y: auto;
          ">
            <!-- 订单信息卡片 -->
            <div class="order-info-card" style="
              background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
              border-radius: 12px;
              padding: 16px;
              margin-bottom: 20px;
              border-left: 4px solid #667eea;
            ">
              <h3 style="margin: 0 0 12px 0; color: #333; font-size: 16px;">订单信息</h3>
              <div style="display: grid; gap: 8px; font-size: 14px;">
                <div style="display: flex; justify-content: space-between;">
                  <span style="color: #666;">订单号：</span>
                  <span style="color: #333; font-weight: 500;">${order.number}</span>
                </div>
                <div style="display: flex; justify-content: space-between;">
                  <span style="color: #666;">订单金额：</span>
                  <span style="color: #e74c3c; font-weight: 600;">¥${order.amount.toFixed(2)}</span>
                </div>
                <div style="display: flex; justify-content: space-between;">
                  <span style="color: #666;">最大退款：</span>
                  <span style="color: #27ae60; font-weight: 600;">¥${checkResult.maxRefundAmount.toFixed(2)}</span>
                </div>
              </div>
            </div>

            <!-- 退款表单 -->
            <form class="refund-form" style="display: grid; gap: 20px;">
              <!-- 退款金额 -->
              <div class="form-group">
                <label style="
                  display: block;
                  margin-bottom: 8px;
                  color: #333;
                  font-weight: 500;
                  font-size: 14px;
                ">退款金额 <span style="color: #e74c3c;">*</span></label>
                <div style="position: relative;">
                  <input
                    id="refundAmount"
                    type="number"
                    value="${Math.min(order.amount, checkResult.maxRefundAmount)}"
                    max="${checkResult.maxRefundAmount}"
                    min="0.01"
                    step="0.01"
                    style="
                      width: 100%;
                      padding: 12px 16px;
                      border: 2px solid #e1e5e9;
                      border-radius: 8px;
                      font-size: 14px;
                      transition: all 0.3s;
                      box-sizing: border-box;
                    "
                    onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 3px rgba(102, 126, 234, 0.1)'"
                    onblur="this.style.borderColor='#e1e5e9'; this.style.boxShadow='none'"
                    oninput="validateRefundAmount(this, ${checkResult.maxRefundAmount})"
                  />
                  <span style="
                    position: absolute;
                    left: 12px;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #666;
                    font-size: 14px;
                    pointer-events: none;
                  ">¥</span>
                </div>
                <div id="amountError" style="margin-top: 4px; font-size: 12px; color: #e74c3c; display: none;">
                  退款金额必须在 ¥0.01 到 ¥${checkResult.maxRefundAmount.toFixed(2)} 之间
                </div>
                <div id="amountHint" style="margin-top: 4px; font-size: 12px; color: #666;">
                  可退款范围：¥0.01 - ¥${checkResult.maxRefundAmount.toFixed(2)}
                </div>
              </div>

              <!-- 退款类型 -->
              <div class="form-group">
                <label style="
                  display: block;
                  margin-bottom: 8px;
                  color: #333;
                  font-weight: 500;
                  font-size: 14px;
                ">退款类型 <span style="color: #e74c3c;">*</span></label>
                <select id="refundType" style="
                  width: 100%;
                  padding: 12px 16px;
                  border: 2px solid #e1e5e9;
                  border-radius: 8px;
                  font-size: 14px;
                  background: white;
                  cursor: pointer;
                  transition: all 0.3s;
                  box-sizing: border-box;
                " onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e1e5e9'">
                  <option value="1">🔄 仅退款（不退货）</option>
                  <option value="2">📦 退货退款（需退回商品）</option>
                </select>
              </div>

              <!-- 退款方式 -->
              <div class="form-group">
                <label style="
                  display: block;
                  margin-bottom: 8px;
                  color: #333;
                  font-weight: 500;
                  font-size: 14px;
                ">退款方式</label>
                <select id="refundMethod" style="
                  width: 100%;
                  padding: 12px 16px;
                  border: 2px solid #e1e5e9;
                  border-radius: 8px;
                  font-size: 14px;
                  background: white;
                  cursor: pointer;
                  transition: all 0.3s;
                  box-sizing: border-box;
                " onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e1e5e9'">
                  <option value="1">💳 原路退回（推荐）</option>
                  <option value="2">💰 余额退款</option>
                </select>
              </div>

              <!-- 退款原因 -->
              <div class="form-group">
                <label style="
                  display: block;
                  margin-bottom: 8px;
                  color: #333;
                  font-weight: 500;
                  font-size: 14px;
                ">退款原因 <span style="color: #e74c3c;">*</span></label>
                <textarea
                  id="refundReason"
                  placeholder="请详细说明退款原因，至少5个字符..."
                  style="
                    width: 100%;
                    height: 80px;
                    padding: 12px 16px;
                    border: 2px solid #e1e5e9;
                    border-radius: 8px;
                    font-size: 14px;
                    resize: vertical;
                    font-family: inherit;
                    transition: all 0.3s;
                    box-sizing: border-box;
                  "
                  onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 3px rgba(102, 126, 234, 0.1)'"
                  onblur="this.style.borderColor='#e1e5e9'; this.style.boxShadow='none'"
                  oninput="validateRefundReason(this)"
                ></textarea>
                <div id="reasonError" style="margin-top: 4px; font-size: 12px; color: #e74c3c; display: none;">
                  退款原因至少需要5个字符
                </div>
                <div id="reasonHint" style="margin-top: 4px; font-size: 12px; color: #666;">
                  请详细描述退款原因，有助于我们改进服务质量
                </div>
              </div>
            </form>
          </div>

          <!-- 对话框底部 -->
          <div class="dialog-footer" style="
            padding: 20px 24px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            background: #fafafa;
          ">
            <button class="cancel-btn" style="
              padding: 10px 20px;
              border: 2px solid #ddd;
              background: white;
              color: #666;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              font-weight: 500;
              transition: all 0.3s;
            " onmouseover="this.style.borderColor='#bbb'; this.style.color='#333'" onmouseout="this.style.borderColor='#ddd'; this.style.color='#666'">取消</button>
            <button class="confirm-btn" style="
              padding: 10px 24px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              border: none;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              font-weight: 500;
              transition: all 0.3s;
              box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            " onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(102, 126, 234, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(102, 126, 234, 0.3)'">
              <span class="btn-text">确认申请</span>
            </button>
          </div>
        </div>
      </div>

      <style>
        @keyframes dialogSlideIn {
          from {
            opacity: 0;
            transform: scale(0.9) translateY(-20px);
          }
          to {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }

        .refund-dialog-overlay {
          animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes fadeOut {
          from { opacity: 1; }
          to { opacity: 0; }
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
          outline: none;
        }

        .confirm-btn:disabled {
          background: #ccc !important;
          cursor: not-allowed !important;
          transform: none !important;
          box-shadow: none !important;
        }

        .error-border {
          border-color: #e74c3c !important;
          box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1) !important;
        }
      </style>

      <script>
        // 验证退款金额
        function validateRefundAmount(input, maxAmount) {
          const value = parseFloat(input.value);
          const errorDiv = document.getElementById('amountError');
          const hintDiv = document.getElementById('amountHint');

          if (isNaN(value) || value < 0.01 || value > maxAmount) {
            input.classList.add('error-border');
            errorDiv.style.display = 'block';
            hintDiv.style.display = 'none';
            updateConfirmButton();
            return false;
          } else {
            input.classList.remove('error-border');
            errorDiv.style.display = 'none';
            hintDiv.style.display = 'block';
            updateConfirmButton();
            return true;
          }
        }

        // 验证退款原因
        function validateRefundReason(textarea) {
          const value = textarea.value.trim();
          const errorDiv = document.getElementById('reasonError');
          const hintDiv = document.getElementById('reasonHint');

          if (value.length < 5) {
            textarea.classList.add('error-border');
            errorDiv.style.display = 'block';
            hintDiv.style.display = 'none';
            updateConfirmButton();
            return false;
          } else {
            textarea.classList.remove('error-border');
            errorDiv.style.display = 'none';
            hintDiv.style.display = 'block';
            updateConfirmButton();
            return true;
          }
        }

        // 更新确认按钮状态
        function updateConfirmButton() {
          const confirmBtn = document.querySelector('.confirm-btn');
          const amountInput = document.getElementById('refundAmount');
          const reasonTextarea = document.getElementById('refundReason');

          const amountValid = !amountInput.classList.contains('error-border') && amountInput.value.trim() !== '';
          const reasonValid = !reasonTextarea.classList.contains('error-border') && reasonTextarea.value.trim().length >= 5;

          if (amountValid && reasonValid) {
            confirmBtn.disabled = false;
            confirmBtn.style.opacity = '1';
          } else {
            confirmBtn.disabled = true;
            confirmBtn.style.opacity = '0.6';
          }
        }
      </script>`

    document.body.appendChild(dialogContainer)

    // 获取表单元素
    const amountInput = dialogContainer.querySelector('#refundAmount') as HTMLInputElement
    const typeSelect = dialogContainer.querySelector('#refundType') as HTMLSelectElement
    const methodSelect = dialogContainer.querySelector('#refundMethod') as HTMLSelectElement
    const reasonTextarea = dialogContainer.querySelector('#refundReason') as HTMLTextAreaElement
    const confirmBtn = dialogContainer.querySelector('.confirm-btn') as HTMLButtonElement
    const cancelBtn = dialogContainer.querySelector('.cancel-btn') as HTMLButtonElement
    const closeBtn = dialogContainer.querySelector('.close-btn') as HTMLButtonElement

    // 关闭对话框函数
    const closeDialog = () => {
      dialogContainer.style.animation = 'fadeOut 0.3s ease-out'
      setTimeout(() => {
        document.body.removeChild(dialogContainer)
      }, 300)
    }

    // 确认按钮事件
    confirmBtn.addEventListener('click', () => {
      // 最终验证（表单内已经实时验证了）
      const amount = parseFloat(amountInput.value)
      const reason = reasonTextarea.value.trim()

      if (reason.length < 5 || amount < 0.01 || amount > checkResult.maxRefundAmount) {
        // 如果验证失败，触发一次验证显示错误
        validateRefundAmount(amountInput, checkResult.maxRefundAmount)
        validateRefundReason(reasonTextarea)
        return
      }

      // 设置表单数据
      refundForm.refundAmount = amount
      refundForm.refundType = parseInt(typeSelect.value)
      refundForm.refundMethod = parseInt(methodSelect.value)
      refundForm.refundReason = reason

      // 显示加载状态
      confirmBtn.disabled = true
      confirmBtn.querySelector('.btn-text')!.textContent = '提交中...'

      closeDialog()
      resolve()
    })

    // 取消按钮事件
    cancelBtn.addEventListener('click', () => {
      closeDialog()
      reject('cancel')
    })

    // 关闭按钮事件
    closeBtn.addEventListener('click', () => {
      closeDialog()
      reject('cancel')
    })

    // 点击遮罩关闭
    dialogContainer.addEventListener('click', (e) => {
      if (e.target === dialogContainer) {
        closeDialog()
        reject('cancel')
      }
    })

    // ESC键关闭
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        closeDialog()
        reject('cancel')
        document.removeEventListener('keydown', handleEsc)
      }
    }
    document.addEventListener('keydown', handleEsc)

    // 自动聚焦到金额输入框并初始化验证状态
    setTimeout(() => {
      amountInput.focus()
      amountInput.select()
      // 初始化验证状态
      updateConfirmButton()
    }, 100)
  })
}

// 申请退款
const applyRefund = async (): Promise<void> => {
  if (!orderDetail.value) return

  try {
    // 首先检查订单是否可以退款
    const checkResponse = await refundApi.checkRefundEligibility(orderDetail.value.id)
    if (checkResponse.data.code !== 1) {
      ElMessage.error(checkResponse.data.msg || '该订单不符合退款条件')
      return
    }

    const checkResult = checkResponse.data.data

    // 创建退款申请表单
    const refundForm = {
      refundReason: '',
      refundAmount: orderDetail.value.amount,
      refundType: 1, // 默认仅退款
      refundMethod: 1, // 默认原路退回
    }

    // 显示优化后的退款申请对话框
    await showRefundDialog(orderDetail.value, checkResult, refundForm)

    // 构建退款请求数据
    const refundRequest: RefundApplicationDTO = {
      orderId: orderDetail.value.id,
      refundAmount: refundForm.refundAmount,
      refundReason: refundForm.refundReason,
      refundType: refundForm.refundType,
      refundMethod: refundForm.refundMethod,
      remark: `订单号：${orderDetail.value.number}，申请时间：${new Date().toLocaleString()}`,
    }

    console.log('退款请求数据:', refundRequest)

    // 调用退款API
    const response = await refundApi.applyRefund(refundRequest)

    if (response.data.code === 1) {
      ElMessage.success('退款申请已提交，请等待处理')
      // 刷新订单详情
      fetchOrderDetail()
    } else {
      ElMessage.error(response.data.msg || '退款申请失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('申请退款失败:', error)
      ElMessage.error('申请退款失败，请稍后重试')
    }
  }
}

// 页面加载时获取订单详情
onMounted(() => {
  fetchOrderDetail()
})
</script>

<style scoped>
/* 添加一些自定义样式 */
.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradientAnimation 15s ease infinite;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}
</style>
