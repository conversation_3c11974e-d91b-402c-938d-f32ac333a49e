<template>
  <div class="tracking-info-container">
    <div v-if="loading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-purple-500"></div>
    </div>

    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 my-4">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path
            fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
            clip-rule="evenodd"
          ></path>
        </svg>
        <p class="text-red-600">{{ error }}</p>
      </div>
    </div>

    <div v-else-if="trackingInfo" class="bg-white rounded-lg shadow-md overflow-hidden">
      <!-- 物流基本信息 -->
      <div class="p-4 bg-gradient-to-r from-purple-50 to-purple-100 border-b">
        <div class="flex justify-between items-center mb-2">
          <h3 class="text-lg font-semibold text-gray-800">物流追踪信息</h3>
          <span
            :class="[
              'px-2 py-1 text-xs font-medium rounded-full',
              getStatusClass(trackingInfo.delivery_status),
            ]"
          >
            {{ getStatusText(trackingInfo.delivery_status) }}
          </span>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p class="text-sm text-gray-500">物流单号</p>
            <p class="font-medium">{{ trackingInfo.tracking_number }}</p>
          </div>
          <div>
            <p class="text-sm text-gray-500">物流公司</p>
            <p class="font-medium">{{ trackingInfo.courier_code }}</p>
          </div>
          <div v-if="trackingInfo.order_number">
            <p class="text-sm text-gray-500">订单编号</p>
            <p class="font-medium">{{ trackingInfo.order_number }}</p>
          </div>
          <div v-if="trackingInfo.latest_checkpoint_time">
            <p class="text-sm text-gray-500">最新更新时间</p>
            <p class="font-medium">{{ formatDate(trackingInfo.latest_checkpoint_time) }}</p>
          </div>
        </div>
      </div>

      <!-- 物流轨迹时间线 -->
      <div class="p-4">
        <h4 class="text-base font-medium text-gray-700 mb-4">物流轨迹</h4>

        <div v-if="trackEvents.length > 0" class="relative">
          <!-- 时间线 -->
          <div class="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

          <!-- 轨迹事件 -->
          <div v-for="(event, index) in trackEvents" :key="index" class="relative pl-10 pb-8">
            <!-- 时间点 -->
            <div
              class="absolute left-2 top-1 transform -translate-x-1/2 w-4 h-4 rounded-full"
              :class="[index === 0 ? 'bg-green-500' : 'bg-gray-300']"
            ></div>

            <!-- 事件内容 -->
            <div class="bg-gray-50 rounded-lg p-3 shadow-sm">
              <p class="text-sm text-gray-500">{{ formatDate(event.checkpoint_date) }}</p>
              <p class="font-medium mt-1">{{ event.tracking_detail }}</p>
              <p v-if="event.location" class="text-sm text-gray-600 mt-1">
                <span class="inline-block mr-1">
                  <svg
                    class="w-4 h-4 inline-block"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    ></path>
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    ></path>
                  </svg>
                </span>
                {{ event.location }}
              </p>
            </div>
          </div>

          <div v-if="trackEvents.length === 0" class="text-center py-8 text-gray-500">
            暂无物流轨迹信息
          </div>
        </div>
      </div>

      <!-- 刷新按钮 -->
      <div class="p-4 border-t bg-gray-50 flex justify-end">
        <button
          @click="refreshTracking"
          class="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          :disabled="refreshing"
        >
          <svg
            v-if="refreshing"
            class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <span>{{ refreshing ? '刷新中...' : '刷新物流信息' }}</span>
        </button>
      </div>
    </div>

    <div v-else class="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
      <svg
        class="w-12 h-12 text-gray-400 mx-auto mb-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z"
        ></path>
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0"
        ></path>
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无物流信息</h3>
      <p class="text-gray-500">该订单尚未添加物流追踪信息</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { trackingService, trackingApi } from '@/api/tracking'
import type { TrackingInfo } from '@/api/tracking'
import { ElMessage } from 'element-plus'

// 添加默认导出
defineOptions({
  name: 'TrackingInfo',
})

const props = defineProps({
  orderId: {
    type: Number,
    required: true,
  },
  trackingNumber: {
    type: String,
    required: true,
  },
})

const trackingInfo = ref<TrackingInfo | null>(null)
const loading = ref(true)
const error = ref<string | null>(null)
const refreshing = ref(false)

// 获取物流轨迹事件，合并并按时间排序
const trackEvents = computed(() => {
  if (!trackingInfo.value) return []

  const events = []

  // 添加目的地信息
  if (trackingInfo.value.destination_info?.trackinfo) {
    events.push(...trackingInfo.value.destination_info.trackinfo)
  }

  // 添加起始地信息
  if (trackingInfo.value.origin_info?.trackinfo) {
    events.push(...trackingInfo.value.origin_info.trackinfo)
  }

  // 按时间降序排序（最新的在前面）
  return events.sort((a, b) => {
    return new Date(b.checkpoint_date).getTime() - new Date(a.checkpoint_date).getTime()
  })
})

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''

  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 获取状态对应的样式类
const getStatusClass = (status: string) => {
  switch (status) {
    case 'delivered':
      return 'bg-green-100 text-green-800'
    case 'transit':
      return 'bg-blue-100 text-blue-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'expired':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取状态对应的文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'delivered':
      return '已送达'
    case 'transit':
      return '运输中'
    case 'pending':
      return '待处理'
    case 'expired':
      return '已过期'
    case 'undelivered':
      return '未送达'
    case 'exception':
      return '异常'
    default:
      return '未知状态'
  }
}

// 获取物流信息
const fetchTrackingInfo = async () => {
  loading.value = true
  error.value = null

  try {
    // 首先尝试使用trackingService.getOrderTracking获取订单关联的物流信息
    const response = await trackingService.getOrderTracking(props.orderId, props.trackingNumber)

    if (response.code === 1 && response.data) {
      trackingInfo.value = response.data
    } else {
      // 如果通过订单关联获取失败，直接使用trackingApi.getTracking获取物流信息
      try {
        const apiResponse = await trackingApi.getTracking(props.trackingNumber)

        if (
          apiResponse.data.meta.code === 200 &&
          apiResponse.data.data.success &&
          apiResponse.data.data.success.length > 0
        ) {
          trackingInfo.value = apiResponse.data.data.success[0]
        } else {
          error.value = '未找到物流信息'
        }
      } catch (apiErr) {
        console.error('通过API获取物流信息出错:', apiErr)
        error.value = response.msg || '获取物流信息失败'
      }
    }
  } catch (err) {
    console.error('获取物流信息出错:', err)
    error.value = '获取物流信息时发生错误，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 刷新物流信息
const refreshTracking = async () => {
  if (refreshing.value) return

  refreshing.value = true

  try {
    // 直接使用trackingApi.getTracking获取最新物流信息
    const apiResponse = await trackingApi.getTracking(props.trackingNumber)

    if (
      apiResponse.data.meta.code === 200 &&
      apiResponse.data.data.success &&
      apiResponse.data.data.success.length > 0
    ) {
      trackingInfo.value = apiResponse.data.data.success[0]
      ElMessage.success('物流信息已更新')
    } else {
      ElMessage.warning('未找到最新物流信息')
    }
  } catch (err) {
    console.error('刷新物流信息出错:', err)
    ElMessage.error('刷新物流信息失败，请稍后重试')
  } finally {
    refreshing.value = false
  }
}

// 组件挂载时获取物流信息
onMounted(() => {
  fetchTrackingInfo()
})
</script>

<style scoped>
.tracking-info-container {
  @apply w-full max-w-3xl mx-auto;
}
</style>
