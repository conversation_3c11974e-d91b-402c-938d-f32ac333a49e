<template>
  <div class="max-w-4xl mx-auto p-6">
    <div class="bg-white rounded-lg shadow p-6">
      <h2 class="text-2xl font-bold mb-6">购物车</h2>

      <!-- 购物车商品列表 -->
      <div class="space-y-4">
        <div v-if="cartItems.length > 0">
          <div v-for="item in cartItems" :key="item.id" class="flex items-center border-b py-4">
            <el-checkbox v-model="item.selected" @change="updateSelected"></el-checkbox>
            <img :src="item.image" :alt="item.productName" class="w-16 h-16 object-cover mx-4" />
            <div class="flex-1">
              <h3 class="font-medium">{{ item.productName }}</h3>
              <p class="text-gray-500 text-sm">¥{{ item.price }}</p>
              <div class="flex items-center mt-2">
                <button @click="updateQuantity(item, -1)" class="border px-2 py-0.5 text-sm">
                  -
                </button>
                <span class="mx-2">{{ item.number }}</span>
                <button @click="updateQuantity(item, 1)" class="border px-2 py-0.5 text-sm">
                  +
                </button>
              </div>
            </div>
            <div class="text-right ml-4">
              <p class="font-bold">¥{{ (item.price * item.number).toFixed(2) }}</p>
              <button @click="removeItem(item.id)" class="text-red-500 text-sm mt-2">删除</button>
            </div>
          </div>
        </div>
        <div v-else class="py-10 text-center text-gray-500">购物车是空的，去添加一些商品吧！</div>
      </div>

      <!-- 底部结算栏 -->
      <div class="flex justify-between items-center mt-6 pt-6 border-t">
        <div class="flex items-center">
          <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox>
          <span class="ml-4">已选 {{ selectedCount }} 件商品</span>
        </div>
        <div class="flex items-center">
          <span class="mr-4"
            >合计：<span class="text-red-600 text-xl font-bold">¥{{ totalAmount }}</span></span
          >
          <el-button type="primary" @click="handleCheckout" :disabled="selectedCount === 0"
            >结算</el-button
          >
        </div>
      </div>
    </div>
  </div>

  <!-- 微信支付对话框 -->
  <el-dialog
    v-model="wechatPayDialogVisible"
    title="微信支付"
    width="400px"
    @closed="closeWechatPayDialog"
    class="wechat-pay-dialog-container"
  >
    <div class="wechat-pay-dialog">
      <!-- 加载状态 -->
      <div v-if="wechatPayLoading" class="wechat-pay-loading">
        <div class="loading-spinner"></div>
        <p class="loading-text">正在生成支付二维码...</p>
      </div>

      <!-- 支付内容 -->
      <div v-else class="wechat-pay-content">
        <!-- 支付头部 -->
        <div class="pay-header">
          <div class="pay-brand">
            <img src="/icons/wechat-pay.svg" alt="微信支付" class="pay-logo" />
            <h3 class="pay-title">微信安全支付</h3>
          </div>
          <div
            class="pay-status"
            :class="{
              'pay-status-success': orderStatus === 'SUCCESS',
              'pay-status-expired': isExpired || orderStatus === 'EXPIRED',
            }"
          >
            <span v-if="orderStatus === 'SUCCESS'">支付成功</span>
            <span v-else-if="isExpired || orderStatus === 'EXPIRED'">已过期</span>
            <span v-else-if="isPaying">扫码成功</span>
            <span v-else>待支付</span>
          </div>
        </div>

        <!-- 支付内容 -->
        <div class="pay-content-container">
          <div class="qrcode-container">
            <!-- 二维码 -->
            <div
              class="qrcode-wrapper"
              :class="{
                'qrcode-scanned': hasScanned,
                'qrcode-expired': isExpired || orderStatus === 'EXPIRED',
              }"
            >
              <img
                v-if="wechatPayQrCode && !isExpired && orderStatus !== 'EXPIRED'"
                :src="wechatPayQrCode"
                alt="微信支付二维码"
                class="wechat-qrcode"
                :class="{ 'blur-qrcode': hasScanned }"
              />

              <!-- 扫码状态 -->
              <div v-if="isPaying" class="scan-status-message">
                <svg
                  class="scan-success-icon"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#07c160"
                  stroke-width="2"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span class="scan-status-text">扫码成功</span>
                <span class="scan-status-subtext">请在微信上完成支付</span>
              </div>

              <!-- 刷新二维码 -->
              <button
                v-else-if="isExpired && !orderStatus"
                @click="initiateWechatPayment"
                class="refresh-btn"
              >
                <svg class="refresh-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                <span>刷新二维码</span>
              </button>

              <!-- 二维码错误 -->
              <div v-else-if="!wechatPayQrCode" class="qrcode-error">
                <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
                <span>获取二维码失败</span>
                <button @click="initiateWechatPayment" class="retry-btn">重试</button>
              </div>
            </div>
          </div>

          <!-- 支付信息 -->
          <div class="payment-info">
            <div class="payment-info-row">
              <span class="info-label">商品</span>
              <span class="info-value">购物车商品 x {{ selectedCount }}</span>
            </div>
            <div class="payment-info-row">
              <span class="info-label">订单号</span>
              <span class="info-value order-number">{{ currentOrderNumber }}</span>
            </div>

            <!-- 支付金额显示 -->
            <div class="payment-amount-display">
              <span class="amount-label">金额</span>
              <span class="price-value">¥{{ totalAmount }}</span>
            </div>
          </div>
        </div>

        <!-- 支付底部 -->
        <div class="pay-footer">
          <div class="pay-instructions">
            <p class="instructions-title">支付说明</p>
            <ol class="instructions-list">
              <li class="instruction-item">打开微信</li>
              <li class="instruction-item">扫描左侧二维码</li>
              <li class="instruction-item">输入支付密码完成支付</li>
            </ol>
          </div>

          <!-- 倒计时 -->
          <div class="pay-countdown" :class="{ 'countdown-critical': countdownTime < 60 }">
            <svg class="countdown-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10" />
              <polyline points="12 6 12 12 16 14" />
            </svg>
            <span class="countdown-text">
              {{ Math.floor(countdownTime / 60) }}:{{
                (countdownTime % 60).toString().padStart(2, '0')
              }}
            </span>
          </div>

          <!-- 安全提示 -->
          <div class="pay-security">
            <svg class="security-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
            <span class="security-text">全程加密，安全支付</span>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>

  <!-- 支付结果对话框 -->
  <el-dialog
    v-model="paymentResultVisible"
    :title="paymentResult.title"
    width="400px"
    class="payment-result-dialog-container"
  >
    <div class="payment-result-dialog">
      <!-- 结果图标 -->
      <div class="result-icon" :class="paymentResult.iconClass">
        <component :is="paymentResult.icon" class="w-16 h-16" />
      </div>

      <!-- 结果标题 -->
      <h3 class="result-title" :class="paymentResult.textClass">
        {{ paymentResult.title }}
      </h3>

      <!-- 结果消息 -->
      <div class="result-message">
        <p class="result-message-cn">
          {{ paymentResult.messageCn }}
        </p>
        <p class="result-message-en">
          {{ paymentResult.messageEn }}
        </p>
      </div>

      <!-- 错误代码 -->
      <p v-if="paymentResult.errorCode" class="result-error-code">
        错误代码 (Error Code): {{ paymentResult.errorCode }}
      </p>

      <!-- 操作按钮 -->
      <div class="result-actions">
        <!-- 成功时显示查看订单按钮 -->
        <button
          v-if="paymentResult.status === 'success'"
          @click="viewOrder"
          class="action-button view-order-button"
        >
          查看订单 (View Order)
        </button>
        <!-- 失败时显示重试按钮 -->
        <button v-else @click="retryPayment" class="action-button retry-button">
          重试支付 (Retry)
        </button>
        <!-- 关闭按钮 -->
        <button @click="closePaymentResult" class="action-button close-button">
          {{ paymentResult.status === 'success' ? '继续购物 (Continue)' : '关闭 (Close)' }}
        </button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { cartApi } from '@/api/cart'
import request from '@/utils/request'
import {
  CheckCircle,
  XCircle,
  AlertCircle,
  AlertTriangle,
  RefreshCw,
  Clock,
  ServerCrash,
} from 'lucide-vue-next'
import QRCode from 'qrcode'
import type { Component } from 'vue'

// 路由和状态
const router = useRouter()
const cartItems = ref([])
const selectAll = ref(false)

// 微信支付相关状态
const wechatPayDialogVisible = ref(false)
const wechatPayLoading = ref(false)
const wechatPayQrCode = ref('')
const currentOrderNumber = ref('')
const hasScanned = ref(false)
const isPaying = ref(false)
const isExpired = ref(false)
const paymentRetryCount = ref(0)
const maxRetryCount = 2
const orderStatus = ref('')
const orderStatusDesc = ref('')
const countdownTime = ref(1800) // 30分钟倒计时
let countdownInterval = null
let paymentStatusInterval = null
const serviceFeeRate = 0.02 // 服务费率 2%
const serviceFee = ref(0)
const paymentAmount = ref(0)
const paymentAmountCny = ref(0)

// 支付结果状态
interface PaymentResult {
  status: 'success' | 'error' | 'pending'
  title: string
  messageCn: string
  messageEn: string
  icon: Component
  iconClass: string
  textClass: string
  errorCode?: string
  orderId?: string
}

const paymentResultVisible = ref(false)
const paymentResult = reactive<PaymentResult>({
  status: 'success',
  title: '支付成功 (Payment Successful)',
  messageCn: '您的订单已成功支付',
  messageEn: 'Your payment has been successfully processed',
  icon: CheckCircle,
  iconClass: 'success-icon',
  textClass: 'success-text',
})

// 计算属性
const selectedCount = computed(() => {
  return cartItems.value.filter((item) => item.selected).length
})

const totalAmount = computed(() => {
  return cartItems.value
    .filter((item) => item.selected)
    .reduce((sum, item) => sum + item.price * item.number, 0)
    .toFixed(2)
})

// 方法
const handleCheckout = () => {
  if (selectedCount.value === 0) {
    ElMessage.warning('请至少选择一件商品')
    return
  }

  // 启动微信支付流程
  initiateWechatPayment()
}

const getCartItems = async () => {
  const token = localStorage.getItem('token')
  if (!token) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  try {
    const userStr = localStorage.getItem('user')
    if (!userStr) {
      throw new Error('User info not found')
    }
    const user = JSON.parse(userStr)
    const response = (await cartApi.getCartList(user.id)) as unknown as ApiResponse<any>
    if (response.code === 1) {
      // 添加selected字段
      cartItems.value = response.data.map((item) => ({ ...item, selected: false }))
    } else {
      ElMessage.error(response.msg || '获取购物车失败')
    }
  } catch (error) {
    console.error('Get cart error:', error)
    if (error.message === 'User info not found') {
      ElMessage.error('用户信息异常，请重新登录')
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      router.push('/login')
    } else {
      ElMessage.error('获取购物车失败，请稍后重试')
    }
  }
}

// 定义响应类型
interface ApiResponse<T> {
  code: number
  data: T
  msg: string
}

// 更新商品数量
const updateQuantity = async (item, change) => {
  const newQuantity = item.number + change
  if (newQuantity < 1) return

  try {
    const response = (await cartApi.updateCartItemNumber(
      item.id,
      newQuantity,
    )) as unknown as ApiResponse<any>
    if (response.code === 1) {
      item.number = newQuantity
      // 更新商品总额
      item.amount = item.price * newQuantity
    } else {
      ElMessage.error(response.msg || '更新数量失败')
    }
  } catch (error) {
    console.error('Update quantity error:', error)
    ElMessage.error('更新数量失败，请稍后重试')
  }
}

// 从购物车中移除商品
const removeItem = async (id) => {
  try {
    const response = (await cartApi.removeFromCart(id)) as unknown as ApiResponse<any>
    if (response.code === 1) {
      cartItems.value = cartItems.value.filter((item) => item.id !== id)
      ElMessage.success('商品已从购物车移除')
    } else {
      ElMessage.error(response.msg || '删除商品失败')
    }
  } catch (error) {
    console.error('Remove item error:', error)
    ElMessage.error('删除商品失败，请稍后重试')
  }
}

// 全选/取消全选
const handleSelectAll = () => {
  cartItems.value.forEach((item) => {
    item.selected = selectAll.value
  })
}

// 更新选中状态
const updateSelected = () => {
  // 如果所有商品都被选中，则全选也被选中
  selectAll.value = cartItems.value.length > 0 && cartItems.value.every((item) => item.selected)
}

// 微信支付相关方法
const initiateWechatPayment = async () => {
  try {
    // 如果是新的支付请求，重置重试计数器
    if (!wechatPayDialogVisible.value) {
      paymentRetryCount.value = 0
    }

    wechatPayLoading.value = true
    wechatPayDialogVisible.value = true
    isExpired.value = false

    // 计算支付金额
    const baseAmount = parseFloat(totalAmount.value)
    serviceFee.value = parseFloat((baseAmount * serviceFeeRate).toFixed(2))
    paymentAmount.value = baseAmount + serviceFee.value
    paymentAmountCny.value = parseFloat((paymentAmount.value * 7.23).toFixed(2))

    // 生成唯一订单号
    const generateOutTradeNo = () => {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const seconds = String(now.getSeconds()).padStart(2, '0')
      const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`

      // 生成8位随机字符串
      const randomStr = Math.random().toString(36).substring(2, 10)

      // 组合订单号
      return `CART_${timestamp}_${randomStr}`
    }

    const outTradeNo = generateOutTradeNo()
    currentOrderNumber.value = outTradeNo

    // 构建商品描述
    const itemsDescription = cartItems.value
      .filter((item) => item.selected)
      .map((item) => `${item.productName} x ${item.number}`)
      .join(', ')

    // 构建支付数据
    const paymentData = {
      total: Math.round(paymentAmount.value * 100), // 转换为分
      title: `购物车结算`, // 订单标题
      outTradeNo: outTradeNo, // 商户订单号
      description: itemsDescription, // 商品描述
    }

    console.log('微信支付请求参数:', paymentData)

    // 使用接口获取支付信息
    const response = await request.post('/pay/wechat/native/qrcode', paymentData).catch((error) => {
      console.error('微信支付请求失败:', error)
      wechatPayLoading.value = false
      wechatPayDialogVisible.value = false
      return null
    })

    if (response && response.data && response.data.code === 1) {
      // 如果返回的是JSON数据
      if (response.data.data) {
        // 获取二维码URL
        const codeUrl = response.data.data.codeUrl || response.data.data.code_url

        if (!codeUrl) {
          throw new Error('返回数据中没有二维码链接')
        }

        // 更新订单号（如果有返回）
        if (response.data.data.orderNo || response.data.data.outTradeNo) {
          currentOrderNumber.value = response.data.data.orderNo || response.data.data.outTradeNo
        }

        // 生成二维码
        try {
          const qrCodeDataUrl = await generateQRCode(codeUrl)
          wechatPayQrCode.value = qrCodeDataUrl
        } catch (qrError) {
          console.error('生成二维码失败:', qrError)
          wechatPayQrCode.value = codeUrl // 备用方案：直接显示链接
        }

        wechatPayLoading.value = false

        // 启动轮询检查支付状态
        startPollingPaymentStatus(currentOrderNumber.value)
      } else {
        throw new Error('返回数据格式不正确')
      }
    } else {
      console.debug('获取二维码失败，无效的响应格式')
      // 检查是否需要重试
      if (paymentRetryCount.value < maxRetryCount) {
        paymentRetryCount.value++
        console.log(`支付二维码生成失败，正在进行第${paymentRetryCount.value}次重试...`)
        wechatPayLoading.value = false
        // 延迟1秒后重试
        setTimeout(() => {
          initiateWechatPayment()
        }, 1000)
        return
      } else {
        wechatPayLoading.value = false
        wechatPayDialogVisible.value = false
        ElMessage.error('支付功能繁忙，请稍后重试')
      }
    }

    // 启动倒计时
    startCountdown()
  } catch (error) {
    console.error('支付初始化失败:', error)
    // 检查是否需要重试
    if (paymentRetryCount.value < maxRetryCount) {
      paymentRetryCount.value++
      console.log(`支付初始化失败，正在进行第${paymentRetryCount.value}次重试...`)
      wechatPayLoading.value = false
      // 延迟1秒后重试
      setTimeout(() => {
        initiateWechatPayment()
      }, 1000)
      return
    } else {
      wechatPayLoading.value = false
      wechatPayDialogVisible.value = false
      ElMessage.error('支付功能繁忙，请稍后重试')
    }
  }
}

// 生成二维码
const generateQRCode = async (text) => {
  try {
    return await QRCode.toDataURL(text, {
      errorCorrectionLevel: 'H',
      margin: 1,
      width: 300,
      color: {
        dark: '#000000',
        light: '#ffffff',
      },
    })
  } catch (error) {
    console.error('生成二维码失败:', error)
    throw error
  }
}

// 倒计时
const startCountdown = () => {
  // 重置倒计时
  countdownTime.value = 1800 // 30分钟

  // 清除之前的倒计时
  if (countdownInterval) {
    clearInterval(countdownInterval)
  }

  // 每秒更新倒计时
  countdownInterval = setInterval(() => {
    if (countdownTime.value > 0) {
      countdownTime.value--
    } else {
      // 倒计时结束
      clearInterval(countdownInterval)
      countdownInterval = null

      // 标记为已过期
      isExpired.value = true
    }
  }, 1000)
}

// 轮询支付状态
const startPollingPaymentStatus = (outTradeNo) => {
  if (paymentStatusInterval) {
    clearInterval(paymentStatusInterval)
  }

  // 每3秒检查一次支付状态
  paymentStatusInterval = window.setInterval(async () => {
    try {
      // 查询订单状态
      const response = await request
        .post(
          '/pay/wechat/order/query',
          {
            outTradeNo: outTradeNo,
          },
          {
            timeout: 5000,
          },
        )
        .catch((err) => {
          console.debug('查询支付状态请求失败:', err)
          return null
        })

      // 处理响应
      if (response && response.data && response.data.code === 1) {
        const orderInfo = response.data.data

        // 更新订单状态
        if (orderInfo) {
          orderStatus.value = orderInfo.trade_state || ''
          orderStatusDesc.value = orderInfo.trade_state_desc || ''

          // 根据状态更新UI
          switch (orderStatus.value) {
            case 'NOTPAY': // 未支付
              hasScanned.value = false
              isPaying.value = false
              break
            case 'USERPAYING': // 用户支付中
              hasScanned.value = true
              isPaying.value = true
              break
            case 'SUCCESS': // 支付成功
              if (paymentStatusInterval) {
                clearInterval(paymentStatusInterval)
              }
              wechatPayDialogVisible.value = false

              // 清空已购买的商品
              clearPurchasedItems()

              // 显示成功结果
              showPaymentResult({
                status: 'success',
                title: '支付成功 (Payment Successful)',
                messageCn: '您的订单已成功支付',
                messageEn: 'Your payment has been successfully processed',
                icon: CheckCircle,
                iconClass: 'success-icon',
                textClass: 'success-text',
                orderId: outTradeNo,
              })
              break
            case 'CLOSED': // 已关闭
            case 'REVOKED': // 已撤销
            case 'PAYERROR': // 支付失败
              if (paymentStatusInterval) {
                clearInterval(paymentStatusInterval)
              }

              // 显示失败结果
              showPaymentResult({
                status: 'error',
                title: '支付失败 (Payment Failed)',
                messageCn: `支付失败: ${orderStatusDesc.value}`,
                messageEn: `Payment failed: ${orderStatusDesc.value}`,
                icon: XCircle,
                iconClass: 'error-icon',
                textClass: 'error-text',
              })
              wechatPayDialogVisible.value = false
              break
          }
        }
      }
    } catch (error) {
      console.debug('查询支付状态处理错误:', error)
    }
  }, 3000)
}

// 支付成功后清空已购买的商品
const clearPurchasedItems = async () => {
  try {
    const selectedItems = cartItems.value.filter((item) => item.selected)

    // 为每个选中的商品调用删除API
    for (const item of selectedItems) {
      await cartApi.removeFromCart(item.id)
    }

    // 从购物车列表中移除选中的商品
    cartItems.value = cartItems.value.filter((item) => !item.selected)

    // 更新全选状态
    updateSelected()
  } catch (error) {
    console.error('清除已购买商品失败:', error)
    ElMessage.warning('商品已支付成功，但从购物车移除失败，请手动清理购物车')
  }
}

// 关闭微信支付对话框
const closeWechatPayDialog = () => {
  if (paymentStatusInterval) {
    clearInterval(paymentStatusInterval)
    paymentStatusInterval = null
  }

  if (countdownInterval) {
    clearInterval(countdownInterval)
    countdownInterval = null
  }

  wechatPayDialogVisible.value = false
  wechatPayQrCode.value = ''
  wechatPayLoading.value = false
  currentOrderNumber.value = ''
  isExpired.value = false
  hasScanned.value = false
  isPaying.value = false
  orderStatus.value = ''
}

// 显示支付结果
const showPaymentResult = (result) => {
  Object.assign(paymentResult, result)
  paymentResultVisible.value = true
}

// 关闭支付结果
const closePaymentResult = () => {
  paymentResultVisible.value = false

  // 如果是成功状态，可以跳转到主页或继续购物
  if (paymentResult.status === 'success') {
    // 不跳转，留在购物车页面
  }
}

// 查看订单
const viewOrder = () => {
  if (paymentResult.orderId) {
    router.push(`/order/detail?id=${paymentResult.orderId}`)
  }
  paymentResultVisible.value = false
}

// 重试支付
const retryPayment = () => {
  paymentResultVisible.value = false
  initiateWechatPayment()
}

// 清理资源
onUnmounted(() => {
  if (countdownInterval) {
    clearInterval(countdownInterval)
  }
  if (paymentStatusInterval) {
    clearInterval(paymentStatusInterval)
  }
})

onMounted(() => {
  getCartItems()
})
</script>

<style scoped>
/* 微信支付对话框样式 */
.wechat-pay-dialog-container {
  /* 对话框容器 */
}

.wechat-pay-dialog {
  display: flex;
  flex-direction: column;
  min-height: 400px;
}

/* 加载状态 */
.wechat-pay-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 16px;
  font-size: 14px;
  color: #333;
}

/* 支付内容 */
.wechat-pay-content {
  display: flex;
  flex-direction: column;
}

.pay-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pay-brand {
  display: flex;
  align-items: center;
}

.pay-logo {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.pay-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.pay-status {
  font-size: 14px;
  font-weight: 500;
}

.pay-status-success {
  color: #07c160;
}

.pay-status-expired {
  color: #ff4d4f;
}

.pay-content-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

@media (min-width: 640px) {
  .pay-content-container {
    flex-direction: row;
  }
}

.qrcode-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
}

.qrcode-wrapper {
  position: relative;
  width: 200px;
  height: 200px;
  padding: 10px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  background: white;
  display: flex;
  justify-content: center;
  align-items: center;
}

.wechat-qrcode {
  max-width: 100%;
  max-height: 100%;
}

.blur-qrcode {
  filter: blur(3px);
}

.qrcode-scanned::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.qrcode-expired::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.scan-status-message {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2;
}

.scan-success-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 8px;
}

.scan-status-text {
  font-size: 16px;
  font-weight: 600;
  color: #07c160;
  margin-bottom: 4px;
}

.scan-status-subtext {
  font-size: 14px;
  color: #333;
}

.refresh-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border: none;
  background: transparent;
  cursor: pointer;
  color: #1890ff;
}

.refresh-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 8px;
}

.qrcode-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.error-icon {
  width: 32px;
  height: 32px;
  color: #ff4d4f;
  margin-bottom: 8px;
}

.retry-btn {
  margin-top: 12px;
  padding: 4px 12px;
  border: 1px solid #1890ff;
  border-radius: 4px;
  background: transparent;
  color: #1890ff;
  cursor: pointer;
}

.payment-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.payment-info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.info-label {
  color: #666;
  font-size: 14px;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.order-number {
  font-family: monospace;
  word-break: break-all;
}

.payment-amount-display {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.amount-label {
  font-size: 14px;
  color: #666;
}

.price-value {
  font-size: 20px;
  font-weight: 600;
  color: #ff4d4f;
}

.cny-value {
  font-size: 14px;
  color: #666;
}

.pay-footer {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.pay-instructions {
  padding: 12px;
  background-color: #fafafa;
  border-radius: 4px;
}

.instructions-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

.instructions-list {
  padding-left: 20px;
  font-size: 12px;
  color: #666;
}

.instruction-item {
  margin-bottom: 4px;
}

.pay-countdown {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.countdown-critical {
  color: #ff4d4f;
}

.countdown-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.countdown-text {
  font-family: monospace;
  font-size: 14px;
}

.pay-security {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #07c160;
  font-size: 12px;
  margin-top: 8px;
}

.security-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}

/* 支付结果对话框样式 */
.payment-result-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 0;
}

.result-icon {
  margin-bottom: 16px;
}

.success-icon {
  color: #07c160;
}

.error-icon {
  color: #ff4d4f;
}

.warning-icon {
  color: #faad14;
}

.result-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  text-align: center;
}

.success-text {
  color: #07c160;
}

.error-text {
  color: #ff4d4f;
}

.warning-text {
  color: #faad14;
}

.result-message {
  text-align: center;
  margin-bottom: 20px;
}

.result-message-cn {
  font-size: 14px;
  margin-bottom: 4px;
}

.result-message-en {
  font-size: 12px;
  color: #666;
}

.result-error-code {
  font-family: monospace;
  font-size: 12px;
  color: #666;
  margin-bottom: 16px;
}

.result-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.action-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.view-order-button {
  background-color: #07c160;
  border: 1px solid #07c160;
  color: white;
}

.view-order-button:hover {
  background-color: #06ad56;
}

.retry-button {
  background-color: #1890ff;
  border: 1px solid #1890ff;
  color: white;
}

.retry-button:hover {
  background-color: #0c7fd9;
}

.close-button {
  background-color: white;
  border: 1px solid #d9d9d9;
  color: #333;
}

.close-button:hover {
  border-color: #1890ff;
  color: #1890ff;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 普通样式 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
