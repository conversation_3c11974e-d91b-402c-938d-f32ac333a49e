import request from '@/utils/request'
import type { ApiResponse } from './types'
import { ElMessage } from 'element-plus'

// 店铺状态枚举
export enum StoreStatus {
  CLOSED = 0,
  OPEN = 1,
}

// 店铺错误码枚举
export enum StoreErrorCode {
  STORE_NOT_EXIST = 2001,
  STORE_NAME_EXIST = 2002,
  NO_PERMISSION = 2003,
}

// 店铺DTO接口
interface StoreDTO {
  id?: number
  sellerId: number
  storeName: string
  photo?: string
  storeDescription?: string
  storeAddress?: string
  storeStatus: StoreStatus
}

// 店铺VO接口
interface StoreVO extends StoreDTO {
  createTime: string
  updateTime: string
}

// 店铺API错误处理
const handleStoreError = (code: number): string => {
  switch (code) {
    case StoreErrorCode.STORE_NOT_EXIST:
      return '店铺不存在'
    case StoreErrorCode.STORE_NAME_EXIST:
      return '店铺名称已存在'
    case StoreErrorCode.NO_PERMISSION:
      return '没有操作权限'
    default:
      return '未知错误'
  }
}

// 店铺API封装
export const storeApi = {
  // 创建店铺
  async createStore(data: StoreDTO): Promise<ApiResponse<null>> {
    try {
      const response = await request.post<ApiResponse<null>>('/store/create', data)
      if (response.data.code !== 1) {
        ElMessage.error(handleStoreError(response.data.code))
      }
      return response.data
    } catch (error) {
      console.error('Create store error:', error)
      throw error
    }
  },

  // 获取店铺信息
  async getStoreInfo(id: number): Promise<ApiResponse<StoreVO>> {
    try {
      const response = await request.get<ApiResponse<StoreVO>>(`/store/${id}`)
      if (response.data.code !== 1) {
        ElMessage.error(handleStoreError(response.data.code))
      }
      return response.data
    } catch (error) {
      console.error('Get store info error:', error)
      throw error
    }
  },

  // 更新店铺信息
  async updateStore(data: StoreDTO): Promise<ApiResponse<null>> {
    try {
      const response = await request.put<ApiResponse<null>>('/store/update', data)
      if (response.data.code !== 1) {
        ElMessage.error(handleStoreError(response.data.code))
      }
      return response.data
    } catch (error) {
      console.error('Update store error:', error)
      throw error
    }
  },

  // 删除店铺
  async deleteStore(id: number): Promise<ApiResponse<null>> {
    try {
      const response = await request.delete<ApiResponse<null>>(`/store/delete/${id}`)
      if (response.data.code !== 1) {
        ElMessage.error(handleStoreError(response.data.code))
      }
      return response.data
    } catch (error) {
      console.error('Delete store error:', error)
      throw error
    }
  },

  // 检查店铺是否存在
  async checkStoreExists(id: number): Promise<boolean> {
    try {
      const response = await this.getStoreInfo(id)
      return response.code === 1
    } catch {
      return false
    }
  },

  // 获取当前用户的店铺ID
  getCurrentStoreId(): number {
    return JSON.parse(localStorage.getItem('storeId') || '0')
  },

  // 保存店铺ID到本地
  saveStoreId(id: number): void {
    localStorage.setItem('storeId', JSON.stringify(id))
  },

  // 清除店铺信息
  clearStoreInfo(): void {
    localStorage.removeItem('storeId')
  },
}

export type { StoreDTO, StoreVO }
