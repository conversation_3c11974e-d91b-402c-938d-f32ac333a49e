<template>
  <div class="refund-demo">
    <div class="container">
      <div class="header">
        <h1>退款功能演示</h1>
        <p>这是一个演示页面，展示退款功能的各种使用场景</p>
      </div>

      <div class="demo-sections">
        <!-- 退款申请演示 -->
        <div class="demo-section">
          <h2>1. 退款申请演示</h2>
          <div class="demo-content">
            <div class="order-examples">
              <div v-for="order in mockOrders" :key="order.id" class="mock-order">
                <h3>{{ order.title }}</h3>
                <div class="order-info">
                  <p><strong>订单号：</strong>{{ order.number }}</p>
                  <p><strong>订单状态：</strong>{{ getOrderStatusText(order.status) }}</p>
                  <p><strong>订单金额：</strong>¥{{ order.amount.toFixed(2) }}</p>
                  <p><strong>可否退款：</strong>{{ canRefund(order) ? '是' : '否' }}</p>
                </div>
                <button
                  v-if="canRefund(order)"
                  @click="demoApplyRefund"
                  class="demo-btn refund-btn"
                >
                  申请退款（演示）
                </button>
                <span v-else class="demo-btn disabled-btn"> 不可退款 </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 退款状态演示 -->
        <div class="demo-section">
          <h2>2. 退款状态演示</h2>
          <div class="demo-content">
            <div class="status-examples">
              <div v-for="status in refundStatuses" :key="status.code" class="status-item">
                <span :class="['status-badge', `status-${status.code.toLowerCase()}`]">
                  {{ status.text }}
                </span>
                <span class="status-desc">{{ status.description }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 退款记录演示 -->
        <div class="demo-section">
          <h2>3. 退款记录演示</h2>
          <div class="demo-content">
            <div class="refund-records">
              <div
                v-for="record in mockRefundRecords"
                :key="record.outRefundNo"
                class="refund-record"
              >
                <div class="record-header">
                  <span class="refund-no">{{ record.outRefundNo }}</span>
                  <span :class="['status-badge', `status-${record.status.toLowerCase()}`]">
                    {{ getRefundStatusText(record.status) }}
                  </span>
                </div>
                <div class="record-details">
                  <p>原订单号：{{ record.outTradeNo }}</p>
                  <p>退款金额：¥{{ (record.amount.refund / 100).toFixed(2) }}</p>
                  <p>申请时间：{{ formatDate(record.createTime) }}</p>
                  <p v-if="record.successTime">成功时间：{{ formatDate(record.successTime) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- API接口演示 -->
        <div class="demo-section">
          <h2>4. API接口演示</h2>
          <div class="demo-content">
            <div class="api-examples">
              <div class="api-item">
                <h4>申请退款</h4>
                <pre><code>{{ JSON.stringify(sampleRefundRequest, null, 2) }}</code></pre>
              </div>
              <div class="api-item">
                <h4>查询退款</h4>
                <pre><code>{{ JSON.stringify(sampleRefundQuery, null, 2) }}</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { OrderStatus } from '@/api/order'

defineOptions({
  name: 'RefundDemo',
})

// 模拟订单数据
const mockOrders = ref([
  {
    id: 1,
    title: '已完成订单（可退款）',
    number: 'ORDER_20240115001',
    status: OrderStatus.COMPLETED,
    amount: 199.99,
  },
  {
    id: 2,
    title: '已发货订单（可退款）',
    number: 'ORDER_20240115002',
    status: OrderStatus.SHIPPED,
    amount: 299.99,
  },
  {
    id: 3,
    title: '已付款订单（可退款）',
    number: 'ORDER_20240115003',
    status: OrderStatus.PAID,
    amount: 99.99,
  },
  {
    id: 4,
    title: '待付款订单（不可退款）',
    number: 'ORDER_20240115004',
    status: OrderStatus.PENDING_PAYMENT,
    amount: 159.99,
  },
  {
    id: 5,
    title: '已取消订单（不可退款）',
    number: 'ORDER_20240115005',
    status: OrderStatus.CANCELLED,
    amount: 79.99,
  },
])

// 退款状态列表
const refundStatuses = [
  { code: 'SUCCESS', text: '退款成功', description: '退款已成功到账' },
  { code: 'PROCESSING', text: '退款处理中', description: '退款申请正在处理中' },
  { code: 'REFUNDCLOSE', text: '退款关闭', description: '退款申请已关闭' },
  { code: 'ABNORMAL', text: '退款异常', description: '退款处理出现异常' },
]

// 模拟退款记录
const mockRefundRecords = ref([
  {
    id: 'wx_refund_001',
    outRefundNo: 'RF1703123456001',
    outTradeNo: 'ORDER_20240115001',
    status: 'SUCCESS',
    amount: { refund: 19999 },
    createTime: '2024-01-15T10:30:00',
    successTime: '2024-01-15T10:35:00',
  },
  {
    id: 'wx_refund_002',
    outRefundNo: 'RF1703123456002',
    outTradeNo: 'ORDER_20240114001',
    status: 'PROCESSING',
    amount: { refund: 15999 },
    createTime: '2024-01-14T15:20:00',
  },
  {
    id: 'wx_refund_003',
    outRefundNo: 'RF1703123456003',
    outTradeNo: 'ORDER_20240113001',
    status: 'REFUNDCLOSE',
    amount: { refund: 8999 },
    createTime: '2024-01-13T09:15:00',
  },
])

// 示例API请求数据
const sampleRefundRequest = {
  outTradeNo: 'ORDER_20240115001',
  outRefundNo: 'RF1703123456001',
  reason: '商品质量问题',
  refundAmount: 19999,
  totalAmount: 19999,
  source: 'REFUND_SOURCE_UNSETTLED_FUNDS',
}

const sampleRefundQuery = {
  outRefundNo: 'RF1703123456001',
}

// 判断订单是否可以退款
const canRefund = (order: any): boolean => {
  // 已付款、待发货、已发货、已完成的订单都可以申请退款
  return (
    order.status === OrderStatus.PAID ||
    order.status === OrderStatus.PROCESSING ||
    order.status === OrderStatus.SHIPPED ||
    order.status === OrderStatus.COMPLETED
  )
}

// 获取订单状态文本
const getOrderStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    [OrderStatus.PENDING_PAYMENT]: '待付款',
    [OrderStatus.PAID]: '已付款',
    [OrderStatus.PROCESSING]: '处理中',
    [OrderStatus.SHIPPED]: '已发货',
    [OrderStatus.COMPLETED]: '已完成',
    [OrderStatus.CANCELLED]: '已取消',
    [OrderStatus.REFUNDED]: '已退款',
  }
  return statusMap[status] || '未知状态'
}

// 获取退款状态文本
const getRefundStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    SUCCESS: '退款成功',
    REFUNDCLOSE: '退款关闭',
    PROCESSING: '退款处理中',
    ABNORMAL: '退款异常',
  }
  return statusMap[status] || '未知状态'
}

// 格式化日期
const formatDate = (dateString: string): string => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 演示申请退款
const demoApplyRefund = () => {
  ElMessage.success('这是演示模式，实际退款请在订单页面操作')
}
</script>

<style scoped>
.refund-demo {
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.header h1 {
  font-size: 32px;
  margin-bottom: 10px;
}

.header p {
  font-size: 18px;
  opacity: 0.9;
}

.demo-sections {
  display: grid;
  gap: 30px;
}

.demo-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.demo-section h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 24px;
}

.order-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.mock-order {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #8a4af3;
}

.mock-order h3 {
  margin-bottom: 15px;
  color: #333;
}

.order-info p {
  margin-bottom: 8px;
  color: #666;
}

.demo-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
  margin-top: 15px;
}

.refund-btn {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
}

.refund-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.disabled-btn {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}

.disabled-btn:hover {
  transform: none;
  box-shadow: none;
}

.status-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  margin-right: 15px;
  min-width: 80px;
  text-align: center;
}

.status-success {
  background: #d4edda;
  color: #155724;
}

.status-processing {
  background: #cce7ff;
  color: #004085;
}

.status-refundclose {
  background: #f8d7da;
  color: #721c24;
}

.status-abnormal {
  background: #fff3cd;
  color: #856404;
}

.status-desc {
  color: #666;
  font-size: 14px;
}

.refund-records {
  display: grid;
  gap: 15px;
}

.refund-record {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  background: #f8f9fa;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.refund-no {
  font-weight: 600;
  color: #333;
}

.record-details p {
  margin-bottom: 5px;
  color: #666;
  font-size: 14px;
}

.api-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.api-item {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.api-item h4 {
  margin-bottom: 15px;
  color: #333;
}

.api-item pre {
  background: #2d3748;
  color: #e2e8f0;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .demo-section {
    padding: 20px;
  }

  .status-examples {
    grid-template-columns: 1fr;
  }

  .api-examples {
    grid-template-columns: 1fr;
  }

  .api-item {
    min-width: auto;
  }
}
</style>
