import request from '@/utils/request'
import type { ApiResponse } from './types'

export interface ReviewsDTO {
  id?: number
  buyerId: number
  productId: number
  storeName: string
  productName: string
  rating: number
  comment: string
  isRecommended?: boolean
}

export interface ReviewsVO {
  id: number
  buyerId: number
  buyerName: string
  storeName: string
  productName: string
  rating: number
  comment: string
  isRecommended: boolean
  commentTime: string
  status: number
  productId: number
}

export const reviewsApi = {
  // 添加评论
  async addReview(data: ReviewsDTO): Promise<ApiResponse<ReviewsVO>> {
    return request.post('/reviews/add', data)
  },

  // 获取商品评论列表
  async getProductReviews(productId: number): Promise<ApiResponse<ReviewsVO[]>> {
    return request.get(`/reviews/product/${productId}`)
  },

  // 删除评论
  async deleteReview(id: number): Promise<ApiResponse<null>> {
    return request.delete(`/reviews/delete/${id}`)
  },
}
