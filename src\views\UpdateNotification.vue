<template>
  <div class="min-h-screen bg-gradient-to-b from-violet-50 to-white">
    <div class="container mx-auto px-4 py-12">
      <!-- 顶部横幅 -->
      <div
        class="bg-violet-600 text-white px-6 py-3 rounded-lg mb-8 shadow-lg flex items-center justify-between"
      >
        <div class="flex items-center gap-3">
          <Bell class="h-5 w-5 animate-bounce" />
          <span>{{ $t('updateNotification.importantUpdate') }}</span>
        </div>
        <span class="text-sm">{{ currentDate }}</span>
      </div>

      <!-- 主要内容卡片 -->
      <div class="bg-white rounded-2xl shadow-xl p-8 mb-8">
        <div class="flex items-center gap-4 mb-6">
          <div class="bg-violet-100 text-violet-600 px-4 py-1 rounded-full text-sm font-medium">
            {{ $t('updateNotification.version') }}
          </div>
          <div class="flex items-center gap-2 text-gray-500">
            <Clock class="h-4 w-4" />
            <span class="text-sm"
              >{{ $t('updateNotification.releaseTime') }}：{{ releaseDate }}</span
            >
          </div>
        </div>

        <h1
          class="text-4xl font-bold mb-4 bg-gradient-to-r from-violet-600 to-indigo-600 bg-clip-text text-transparent"
        >
          {{ $t('updateNotification.systemUpgrade') }}
        </h1>

        <p class="text-gray-600 text-lg mb-8">
          {{ $t('updateNotification.description') }}
        </p>

        <!-- 更新内容标签页 -->
        <div class="mb-8">
          <div class="flex space-x-4 border-b mb-6">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="currentTab = tab.id"
              :class="[
                'px-4 py-2 font-medium transition-colors',
                currentTab === tab.id
                  ? 'text-violet-600 border-b-2 border-violet-600'
                  : 'text-gray-500 hover:text-violet-600',
              ]"
            >
              {{ $t(`updateNotification.tabs.${tab.id}`) }}
            </button>
          </div>

          <transition name="fade" mode="out-in">
            <div v-if="currentTab === 'features'" class="space-y-4">
              <div
                v-for="feature in features"
                :key="feature.key"
                class="flex items-start gap-4 p-4 rounded-lg hover:bg-violet-50 transition-colors"
                @mouseenter="feature.isHovered = true"
                @mouseleave="feature.isHovered = false"
              >
                <div class="bg-violet-100 p-3 rounded-lg">
                  <component
                    :is="feature.icon"
                    class="h-6 w-6 text-violet-600"
                    :class="{ 'animate-spin-slow': feature.isHovered }"
                  />
                </div>
                <div>
                  <h3 class="font-semibold text-lg mb-1">
                    {{ $t(`updateNotification.features.${feature.key}.title`) }}
                  </h3>
                  <p class="text-gray-600">
                    {{ $t(`updateNotification.features.${feature.key}.description`) }}
                  </p>
                </div>
              </div>
            </div>

            <div v-else-if="currentTab === 'guide'" class="space-y-6">
              <div class="bg-violet-50 p-6 rounded-lg">
                <h3 class="font-semibold text-lg mb-4 flex items-center gap-2">
                  <BookOpen class="h-5 w-5 text-violet-600" />
                  {{ $t('updateNotification.guide.title') }}
                </h3>
                <div class="space-y-4">
                  <div
                    v-for="(step, index) in $t('updateNotification.guide.steps')"
                    :key="index"
                    class="flex items-center gap-4"
                  >
                    <div
                      class="bg-violet-600 text-white w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0"
                    >
                      {{ index + 1 }}
                    </div>
                    <p class="text-gray-700">{{ step }}</p>
                  </div>
                </div>
              </div>
            </div>

            <div v-else-if="currentTab === 'feedback'" class="space-y-6">
              <div class="bg-violet-50 p-6 rounded-lg">
                <h3 class="font-semibold text-lg mb-4">
                  {{ $t('updateNotification.feedback.title') }}
                </h3>
                <textarea
                  v-model="feedback"
                  class="w-full p-3 rounded-lg border border-violet-200 focus:border-violet-600 focus:ring-2 focus:ring-violet-200 outline-none transition-all"
                  rows="4"
                  :placeholder="$t('updateNotification.feedback.placeholder')"
                ></textarea>
                <button
                  @click="submitFeedback"
                  class="mt-4 bg-violet-600 text-white px-6 py-2 rounded-lg hover:bg-violet-700 transition-colors flex items-center gap-2"
                >
                  <Send class="h-4 w-4" />
                  {{ $t('updateNotification.feedback.submit') }}
                </button>
              </div>
            </div>
          </transition>
        </div>

        <!-- 底部信息 -->
        <div class="border-t pt-6">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
              <button
                @click="toggleLike"
                class="flex items-center gap-2 text-gray-600 hover:text-violet-600 transition-colors"
              >
                <Heart :class="{ 'fill-current text-red-500': isLiked }" class="h-5 w-5" />
                <span>{{ likeCount }}</span>
              </button>
              <button
                @click="toggleBookmark"
                class="flex items-center gap-2 text-gray-600 hover:text-violet-600 transition-colors"
              >
                <Bookmark
                  :class="{ 'fill-current text-violet-600': isBookmarked }"
                  class="h-5 w-5"
                />
                <span>{{ $t('updateNotification.actions.bookmark') }}</span>
              </button>
            </div>
            <button
              @click="shareUpdate"
              class="flex items-center gap-2 text-violet-600 hover:text-violet-700 transition-colors"
            >
              <Share2 class="h-5 w-5" />
              <span>{{ $t('updateNotification.actions.share') }}</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 相关更新 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div
          v-for="update in relatedUpdates"
          :key="update.key"
          class="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow"
        >
          <div class="flex items-start justify-between mb-4">
            <div>
              <h3 class="font-semibold text-lg mb-2">
                {{ $t(`updateNotification.relatedUpdates.${update.key}.title`) }}
              </h3>
              <p class="text-gray-600">
                {{ $t(`updateNotification.relatedUpdates.${update.key}.description`) }}
              </p>
            </div>
            <div
              :class="`bg-${update.color}-100 text-${update.color}-600 px-3 py-1 rounded-full text-sm`"
            >
              {{ $t(`updateNotification.relatedUpdates.${update.key}.tag`) }}
            </div>
          </div>
          <div class="flex items-center justify-between text-sm text-gray-500">
            <span>{{ update.date }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Bell,
  Clock,
  Heart,
  Bookmark,
  Share2,
  BookOpen,
  Send,
  Zap,
  Layout,
  Shield,
  Cpu,
} from 'lucide-vue-next'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const currentTab = ref('features')
const feedback = ref('')
const isLiked = ref(false)
const isBookmarked = ref(false)
const likeCount = ref(128)

const tabs = [{ id: 'features' }, { id: 'guide' }, { id: 'feedback' }]

const features = ref([
  {
    key: 'performance',
    icon: Zap,
    isHovered: false,
  },
  {
    key: 'interface',
    icon: Layout,
    isHovered: false,
  },
  {
    key: 'security',
    icon: Shield,
    isHovered: false,
  },
  {
    key: 'ai',
    icon: Cpu,
    isHovered: false,
  },
])

const steps = [
  '更新到最新版本以体验全新功能',
  '查看新功能教程了解详细使用方法',
  '遇到问题及时反馈，我们将持续优化',
  '关注系统公告，获取最新更新信息',
]

const relatedUpdates = [
  {
    key: 'mobileOptimization',
    color: 'green',
    date: '2024年2月20日',
  },
  {
    key: 'dataAnalysis',
    color: 'blue',
    date: '2024年2月18日',
  },
]

const toggleLike = () => {
  isLiked.value = !isLiked.value
  likeCount.value += isLiked.value ? 1 : -1
}

const toggleBookmark = () => {
  isBookmarked.value = !isBookmarked.value
}

const shareUpdate = () => {
  ElMessage.info(t('updateNotification.actions.shareMessage'))
}

const submitFeedback = () => {
  if (feedback.value.trim()) {
    ElMessage.success(t('updateNotification.feedback.success'))
    feedback.value = ''
  } else {
    ElMessage.warning(t('updateNotification.feedback.empty'))
  }
}

const locale = ref('zh')

const currentDate = new Date().toLocaleDateString(locale.value === 'zh' ? 'zh-CN' : 'en-US', {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
})

const releaseDate = locale.value === 'zh' ? '2024年2月23日' : 'February 23, 2024'
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.animate-spin-slow {
  animation: spin 3s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
