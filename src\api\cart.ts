import request from '@/utils/request'
import type { ApiResponse } from './types'
import type { CartItem, AddToCartDTO } from '@/types/cart'

// 购物车商品DTO
export interface ShoppingCartDTO {
  id?: number
  buyerId: number
  productId: number
  productName: string
  number: number
  amount: number
  price: number
  image: string
}

// 购物车商品VO
export interface ShoppingCartVO extends ShoppingCartDTO {
  id: number
}

export const cartApi = {
  // 添加到购物车
  addToCart(data: AddToCartDTO) {
    return request.post('/cart/add', {
      buyerId: Number(data.buyerId),
      productId: Number(data.productId),
      productName: data.productName,
      number: data.number,
      price: data.price,
      amount: data.price * data.number,
      image: data.image,
      attrs:data.attrs
    })
  },

  // 获取购物车列表
  getCartList(buyerId: string) {
    return request.get(`/cart/list/${buyerId}`)
  },

  // 更新购物车商品数量
  updateCartItemNumber(id: number, number: number) {
    return request.put(`/cart/update/`, {
      id,
      number,
    })
  },

  // 删除购物车商品
  removeFromCart(id: number) {
    return request.delete(`/cart/delete/${id}`)
  },
}
