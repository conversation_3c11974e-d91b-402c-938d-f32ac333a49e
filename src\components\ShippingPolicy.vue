<template>
  <div class="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto bg-white rounded-xl shadow-md p-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">Shipping Policy</h1>
      <div class="prose prose-lg max-w-none">
        <div class="space-y-6">
          <section>
            <p>1. Shipping Instructions: you can choose the dropshipping method on ShareWharf. The shop of ShareWharf will fulfill the dropshipping.</p>

            <p>2. Shipping Cost Prepaid: when you place an order, The shop of ShareWharf will charge you the dropshipping cost. You can track shipping information through the other third-party tracking website.</p>

            <p>3. Price Change: The shop of ShareWharf will change shipping costs periodically or seasonally according to the third-party carriers' prices. Once shipping cost is changed, you will be notified by email or system notification.</p>

            <p>4. ShareWharf shall not be liable for any damage, loss, delay, or non-delivery of the packages due to the following reasons:</p>

            <div class="pl-6">
              <p>(1) Force Majeure: "Force Majeure" refers to the objective factors or accidents that are unpredictable, uncontrollable, or unavoidable, including but not limited to earthquake, fire, snow disaster, storm, heavy fog, and severe weather; strikes, terrorist incidents, traffic accidents, amendments to regulations and policies, acts, decisions or orders of the government or judicial authorities; violent crimes such as robbery, drive-by robbery, etc.</p>

              <p>(2) Damage caused by the natural characteristic, inherent defects or reasonable wear and tear of the goods.</p>

              <p>(3) Delays in receiving the goods or failure to deliver the goods due to the customer's own reasons.</p>
            </div>
          </section>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 不再需要props和emit，因为这不再是一个模态框组件
</script>

<style scoped>
.prose {
  color: #374151;
  line-height: 1.75;
}

.prose h2 {
  color: #1F2937;
  font-weight: 600;
  margin-top: 2em;
  margin-bottom: 1em;
  font-size: 1.5em;
}

.prose p {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose ul {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}

.prose li {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}
</style>
