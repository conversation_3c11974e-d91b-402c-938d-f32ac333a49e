<template>
  <div class="refund-policy-container">
    <div class="refund-policy-content">
      <div class="refund-policy-header">
        <h1>退款政策</h1>
      </div>
      <div class="refund-policy-body">
        <section>
          <h2>1. 退款条件</h2>
          <p>我们致力于为客户提供高质量的产品和服务。如果您对购买不满意，可以根据以下条件申请退款：</p>
          <ul>
            <li>商品未使用且包装完好</li>
            <li>在收到商品后7天内提出申请</li>
            <li>提供有效的订单号和购买凭证</li>
            <li>特定商品（如数字产品、定制商品等）可能不适用常规退款政策</li>
          </ul>
        </section>

        <section>
          <h2>2. 退款流程</h2>
          <p>退款申请将按照以下流程处理：</p>
          <ol>
            <li>在个人中心提交退款申请</li>
            <li>客服人员审核申请（1-3个工作日）</li>
            <li>审核通过后，安排退货或直接退款</li>
            <li>退款将在收到退回商品并确认状态后的7个工作日内处理</li>
            <li>退款将退回至原支付账户</li>
          </ol>
        </section>

        <section>
          <h2>3. 退款金额</h2>
          <p>退款金额将根据以下情况确定：</p>
          <ul>
            <li>全新未使用商品：全额退款（包括商品价格和标准配送费）</li>
            <li>已开封但未使用商品：商品价格的90%</li>
            <li>存在质量问题的商品：全额退款并可能提供额外补偿</li>
            <li>促销或折扣商品：按实际支付金额计算</li>
          </ul>
        </section>

        <section>
          <h2>4. 特殊情况</h2>
          <p>以下情况可能影响退款处理：</p>
          <ul>
            <li>季节性商品和特价商品可能有特殊的退款政策</li>
            <li>定制商品除非存在质量问题，否则不接受退款</li>
            <li>数字内容或服务一旦访问或下载，不予退款</li>
            <li>因不可抗力因素导致的交付延迟不属于退款范围</li>
          </ul>
        </section>

        <section>
          <h2>5. 联系我们</h2>
          <p>如有任何关于退款的问题，请通过以下方式联系我们的客户服务团队：</p>
          <ul>
            <li>客服电话：400-123-4567（工作日 9:00-18:00）</li>
            <li>电子邮件：<EMAIL></li>
            <li>在线客服：登录账户后访问"帮助中心"</li>
          </ul>
        </section>
      </div>
      <div class="refund-policy-footer">
        <p>最后更新日期：2023年12月28日</p>
        <p>本政策的最终解释权归本公司所有</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 不再需要props和emit，因为这不再是一个模态框组件
</script>

<style scoped>
.refund-policy-container {
  padding: 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.refund-policy-content {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.refund-policy-header {
  padding: 24px 32px;
  border-bottom: 1px solid #eaeaea;
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  color: white;
}

.refund-policy-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.refund-policy-body {
  padding: 32px;
  color: #333;
  line-height: 1.6;
}

section {
  margin-bottom: 30px;
}

h2 {
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
  font-weight: 500;
}

p {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 15px;
}

ul, ol {
  padding-left: 20px;
  margin-bottom: 16px;
}

li {
  margin-bottom: 8px;
  font-size: 15px;
}

.refund-policy-footer {
  padding: 20px 32px;
  border-top: 1px solid #eaeaea;
  background-color: #f9f9f9;
  font-size: 14px;
  color: #666;
  text-align: center;
}

.refund-policy-footer p {
  margin: 5px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .refund-policy-container {
    padding: 20px 10px;
  }

  .refund-policy-header {
    padding: 16px 20px;
  }

  .refund-policy-header h1 {
    font-size: 20px;
  }

  .refund-policy-body {
    padding: 20px;
  }

  h2 {
    font-size: 16px;
  }

  p, li {
    font-size: 14px;
  }
}
</style>
