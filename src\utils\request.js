import axios from 'axios'
import { ElMessage } from 'element-plus'

// 1. 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API || import.meta.env.VITE_BASE_API, // 从环境变量获取基础URL
  timeout: 15000, // 超时时间设置为15秒
  headers: {
    'Content-Type': 'application/json',
  },
})

// 2. 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `${token}`
    }
    return config
  },
  (error) => {
    console.error('请求错误：', error)
    return Promise.reject(error)
  },
)

// 3. 响应拦截器
request.interceptors.response.use(
  (response) => {
    const res = response.data
    console.log('response:', response)
    // 根据后端接口规范处理响应
    if (response.status === 200) {
      return response
    } else {
      ElMessage.error(res.message || '请求失败')
      return Promise.reject(new Error(res.message))
    }
    return response
  },
  (error) => {
    // 处理HTTP错误
    if (error.response?.status === 401) {
      // 处理未授权错误
      localStorage.removeItem('token')
      window.location.href = '/login'
    }

    ElMessage.error(error.message || '网络错误')
    return Promise.reject(error)
  },
)

export default request
