import request from '@/utils/request'
import type { ApiResponse } from './types'

export interface FavoritesDTO {
  id?: number
  buyerId: number
  storeName: string
  productId: number
}

export interface FavoritesVO {
  id: number
  storeName: string
  productId: number
  productName: string
  collectTime: string
}

export const favoritesApi = {
  // 添加收藏
  async addFavorite(data: FavoritesDTO): Promise<ApiResponse<FavoritesVO>> {
    return request.post('/favorites/add', data)
  },

  // 获取收藏列表
  async getFavoritesList(buyerId: number): Promise<ApiResponse<FavoritesVO[]>> {
    return request.get('/favorites/list', { params: { buyerId } })
  },

  // 取消收藏
  async deleteFavorite(id: number): Promise<ApiResponse<null>> {
    return request.delete(`/favorites/delete/${id}`)
  },
}
