<template>
  <div class="products-container">
    <!-- 头部导航 -->
    <header class="header">
      <div class="header-left">
        <img src="/api/placeholder/40/40" alt="logo" class="logo" />
        <h1 class="header-title">商城</h1>
      </div>
      <div class="header-right">
        <div class="search-box">
          <input type="text" placeholder="搜索商品" v-model="searchQuery" />
          <button class="search-btn">
            <i class="icon-search">🔍</i>
          </button>
        </div>
        <div class="user-actions">
          <button class="cart-btn">
            <i class="icon-cart">🛒</i>
            <span class="cart-count" v-if="cartCount > 0">{{ cartCount }}</span>
          </button>
          <button class="user-btn">
            <i class="icon-user">👤</i>
          </button>
        </div>
      </div>
    </header>

    <!-- 分类筛选 -->
    <div class="filters">
      <div class="category-tabs">
        <button
          v-for="category in categories"
          :key="category.id"
          :class="['category-tab', selectedCategory === category.id ? 'active' : '']"
          @click="selectedCategory = category.id"
        >
          {{ category.name }}
        </button>
      </div>
      <div class="sort-options">
        <select v-model="sortOption">
          <option value="popular">热门</option>
          <option value="newest">最新</option>
          <option value="priceAsc">价格 ↑</option>
          <option value="priceDesc">价格 ↓</option>
        </select>
      </div>
    </div>

    <!-- 商品列表 -->
    <div class="products-wrapper">
      <h2 class="section-title">全部商品</h2>

      <div class="products-grid">
        <div
          v-for="product in filteredProducts"
          :key="product.id"
          class="product-card"
          @click="viewProduct(product.id)"
        >
          <div class="product-image">
            <img :src="product.image" :alt="product.name" />
            <div class="quick-action">
              <button class="add-to-cart-btn" @click.stop="addToCart(product.id)">
                <i class="icon-add">+</i>
              </button>
              <button class="favorite-btn" @click.stop="toggleFavorite(product.id)">
                <i class="icon-heart" :class="{ favorited: product.isFavorite }">❤</i>
              </button>
            </div>
          </div>
          <div class="product-info">
            <h3 class="product-name">{{ product.name }}</h3>
            <p class="product-description">{{ product.description }}</p>
            <div class="product-price-row">
              <span class="product-price">¥{{ product.price.toFixed(2) }}</span>
              <span class="product-sales">已售 {{ product.sales }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <button
          v-for="page in totalPages"
          :key="page"
          :class="['page-btn', currentPage === page ? 'active' : '']"
          @click="currentPage = page"
        >
          {{ page }}
        </button>
      </div>
    </div>

    <!-- 底部 -->
    <footer class="footer">
      <div class="footer-links">
        <a href="#">关于我们</a>
        <a href="#">服务条款</a>
        <a href="#">隐私政策</a>
        <a href="#">联系客服</a>
      </div>
      <div class="copyright">
        © 2025 商城. 保留所有权利
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

import { productApi } from '@/api/product';
import { categoryApi } from '@/api/category';
import { cartApi } from '@/api/cart';
import { ElMessage } from 'element-plus';

const cartCount = ref(0);
const searchQuery = ref('');
const selectedCategory = ref('all');
const sortOption = ref('popular');
const currentPage = ref(1);
const pageSize = 12;

const categories = ref([]);
const products = ref([]);

// 获取分类列表
const fetchCategories = async () => {
  try {
    const response = await categoryApi.getCategoryList();
    if (response.code === 1) {
      categories.value = [{ id: 'all', name: '全部' }, ...response.data];
    }
  } catch (error) {
    console.error('获取分类列表失败:', error);
    ElMessage.error('获取分类列表失败');
  }
};

// 获取商品列表
const fetchProducts = async () => {
  try {
    const response = await productApi.getProductPage({
      page: currentPage.value,
      pageSize: pageSize,
      categoryId: selectedCategory.value === 'all' ? null : selectedCategory.value,
      sortBy: sortOption.value
    });
    if (response.code === 1) {
      products.value = response.data.records.map(product => ({
        ...product,
        isFavorite: false // 这里可以通过收藏API获取实际状态
      }));
    }
  } catch (error) {
    console.error('获取商品列表失败:', error);
    ElMessage.error('获取商品列表失败');
  }
};

// 获取购物车数量
const fetchCartCount = async () => {
  const userStr = localStorage.getItem('user');
  const user = userStr ? JSON.parse(userStr) : null;
  if (user?.id) {
    try {
      const response = await cartApi.getCartList(user.id);
      if (response.data.code === 1) {
        cartCount.value = response.data.data.length;
      }
    } catch (error) {
      console.error('获取购物车数量失败:', error);
    }
  }
};

// 初始化数据
onMounted(() => {
  fetchCategories();
  fetchProducts();
  fetchCartCount();
});

// 监听筛选条件变化
watch([selectedCategory, sortOption, currentPage], () => {
  fetchProducts();
});

// 搜索商品
const debouncedSearch = debounce(() => {
  fetchProducts();
}, 300);

watch(searchQuery, () => {
  debouncedSearch();
});
// 根据筛选条件过滤产品
const filteredProducts = computed(() => products.value);

// 分页数据
const paginatedProducts = computed(() => products.value);

const totalPages = computed(() => {
  return Math.ceil(products.value.length / pageSize);
});

// 查看商品详情
const viewProduct = (productId) => {
  router.push(`/product/${productId}`);
};

// 添加到购物车
const addToCart = async (productId) => {
  const userStr = localStorage.getItem('user');
  const user = userStr ? JSON.parse(userStr) : null;
  if (!user?.id) {
    ElMessage.warning('请先登录');
    return;
  }

  const product = products.value.find(p => p.id === productId);
  if (!product) return;

  try {
    const response = await cartApi.addToCart({
      buyerId: user.id,
      productId: product.id,
      productName: product.name,
      number: 1,
      price: product.price,
      image: product.image
    });

    if (response.data.code === 1) {
      ElMessage.success('添加成功');
      await fetchCartCount();
    } else {
      ElMessage.error(response.data.msg || '添加失败');
    }
  } catch (error) {
    console.error('添加购物车失败:', error);
    ElMessage.error('添加失败');
  }
};

// 切换收藏状态
const toggleFavorite = async (productId) => {
  const userStr = localStorage.getItem('user');
  const user = userStr ? JSON.parse(userStr) : null;
  if (!user?.id) {
    ElMessage.warning('请先登录');
    return;
  }

  const product = products.value.find(p => p.id === productId);
  if (!product) return;

  try {
    // 这里需要调用收藏API，暂时模拟
    product.isFavorite = !product.isFavorite;
    ElMessage.success(product.isFavorite ? '收藏成功' : '取消收藏成功');
  } catch (error) {
    console.error('操作收藏失败:', error);
    ElMessage.error('操作失败');
  }
};
</script>

<style scoped>
.products-container {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f8f9fa;
  color: #333;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
}

.header-title {
  font-size: 24px;
  font-weight: 600;
  color: #8a2be2;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 8px;
  padding: 8px 16px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.search-box input {
  border: none;
  outline: none;
  width: 200px;
  font-size: 14px;
}

.search-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.user-actions {
  display: flex;
  gap: 15px;
}

.cart-btn, .user-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 20px;
  position: relative;
}

.cart-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #8a2be2;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background-color: white;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.category-tabs {
  display: flex;
  gap: 15px;
  overflow-x: auto;
  padding-bottom: 5px;
}

.category-tab {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px 12px;
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.category-tab.active {
  color: #8a2be2;
  font-weight: 600;
  border-bottom: 2px solid #8a2be2;
}

.sort-options select {
  padding: 6px 12px;
  border-radius: 6px;
  border: 1px solid #ddd;
  font-size: 14px;
  outline: none;
}

.products-wrapper {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  padding: 20px;
}

.section-title {
  font-size: 20px;
  margin-bottom: 20px;
  color: #333;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.product-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-image {
  height: 200px;
  position: relative;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.quick-action {
  position: absolute;
  bottom: 10px;
  right: 10px;
  display: flex;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.2s;
}

.product-card:hover .quick-action {
  opacity: 1;
}

.add-to-cart-btn, .favorite-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  background-color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.add-to-cart-btn {
  background-color: #8a2be2;
  color: white;
  font-size: 18px;
}

.icon-heart {
  color: #999;
  font-size: 16px;
}

.icon-heart.favorited {
  color: #ff4757;
}

.product-info {
  padding: 15px;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.product-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 18px;
  font-weight: 600;
  color: #ff4757;
}

.product-sales {
  font-size: 12px;
  color: #999;
}

.pagination {
  display: flex;
  justify-content: center;
  gap: 5px;
  margin-top: 30px;
}

.page-btn {
  min-width: 36px;
  height: 36px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  color: #666;
}

.page-btn.active {
  background-color: #8a2be2;
  color: white;
  border-color: #8a2be2;
}

.footer {
  margin-top: 40px;
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #eee;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 10px;
}

.footer-links a {
  color: #666;
  text-decoration: none;
  font-size: 14px;
}

.footer-links a:hover {
  color: #8a2be2;
}

.copyright {
  font-size: 14px;
  color: #999;
}
</style>
