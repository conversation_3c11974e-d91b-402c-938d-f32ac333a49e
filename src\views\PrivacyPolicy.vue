<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
    <!-- 装饰性背景 -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
      <div
        class="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full opacity-20 blur-3xl"
      ></div>
      <div
        class="absolute top-1/3 -left-40 w-80 h-80 bg-purple-100 rounded-full opacity-20 blur-3xl"
      ></div>
    </div>

    <!-- 进度条 -->
    <div
      class="fixed top-0 left-0 h-1 bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-300 z-50"
      :style="{ width: `${scrollProgress}%` }"
    ></div>

    <!-- 顶部横幅 -->
    <header class="bg-white/80 backdrop-blur-sm fixed w-full z-40 border-b border-gray-100">
      <div class="max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <h1 class="text-2xl font-semibold text-gray-900">
              <span class="text-blue-600">用户协议</span>
              与隐私政策
            </h1>
            <div class="hidden sm:flex items-center space-x-2 text-sm text-gray-500">
              <i-lucide-clock class="w-4 h-4" />
              <span>阅读时间约 {{ readingTime }} 分钟</span>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <button
              @click="toggleFontSize"
              class="p-2 text-gray-500 hover:text-blue-600 transition-colors"
              :title="isLargeFont ? '减小字体' : '增大字体'"
            >
              <i-lucide-type class="w-5 h-5" />
            </button>
            <button
              @click="toggleTheme"
              class="p-2 text-gray-500 hover:text-blue-600 transition-colors"
            >
              <i-lucide-sun v-if="isDark" class="w-5 h-5" />
              <i-lucide-moon v-else class="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main
      :class="{ 'text-lg': isLargeFont }"
      class="max-w-7xl mx-auto px-4 pt-24 pb-16 sm:px-6 lg:px-8 relative z-10"
    >
      <!-- 顶部统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <div
          v-for="stat in statistics"
          :key="stat.title"
          class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300"
        >
          <div class="flex items-center justify-between">
            <component :is="stat.icon" class="w-8 h-8 text-blue-600" />
            <span class="text-sm text-gray-500">{{ stat.period }}</span>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mt-4">{{ stat.value }}</h3>
          <p class="text-gray-600 mt-1">{{ stat.title }}</p>
        </div>
      </div>

      <div class="lg:grid lg:grid-cols-12 lg:gap-8">
        <!-- 左侧固定导航 -->
        <nav class="hidden lg:block lg:col-span-3">
          <div class="sticky top-24">
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-medium text-gray-900 flex items-center">
                  <i-lucide-list class="mr-2 text-blue-600" />
                  目录
                </h2>
                <span class="text-sm text-gray-500">{{
                  currentSection ? `${getCurrentSectionNumber()}/${sections.length}` : ''
                }}</span>
              </div>

              <!-- 搜索框 -->
              <div class="relative mb-4">
                <input
                  type="text"
                  v-model="searchQuery"
                  placeholder="搜索内容..."
                  class="w-full px-4 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                />
                <i-lucide-search class="absolute right-3 top-2.5 text-gray-400 w-4 h-4" />
              </div>

              <ul class="space-y-3">
                <li v-for="(section, index) in filteredSections" :key="index">
                  <a
                    :href="`#${section.id}`"
                    class="flex items-center text-gray-600 hover:text-blue-600 transition-colors duration-200 p-2 rounded-lg"
                    :class="{
                      'bg-blue-50 text-blue-600 font-medium': currentSection === section.id,
                    }"
                    @click.prevent="scrollToSection(section.id)"
                  >
                    <component :is="section.icon" class="w-4 h-4 mr-2" />
                    {{ section.title }}
                  </a>
                </li>
              </ul>
            </div>

            <!-- 分享卡片 -->
            <div
              class="mt-6 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl p-6 text-white"
            >
              <h3 class="font-medium mb-3 flex items-center">
                <i-lucide-share-2 class="mr-2" />
                分享文档
              </h3>
              <div class="flex space-x-4">
                <button
                  v-for="social in socials"
                  :key="social.name"
                  class="p-2 hover:bg-white/20 rounded-lg transition-colors"
                  :title="social.name"
                >
                  <component :is="social.icon" class="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </nav>

        <!-- 右侧内容区域 -->
        <div class="lg:col-span-9 mt-8 lg:mt-0">
          <!-- 更新时间轴 -->
          <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-8 mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-6 flex items-center">
              <i-lucide-history class="mr-2 text-blue-600" />
              更新历史
            </h3>
            <div class="relative">
              <div class="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>
              <div class="space-y-6">
                <div v-for="update in updates" :key="update.date" class="relative pl-10">
                  <div
                    class="absolute left-0 top-1.5 w-8 h-8 bg-white border-2 border-blue-600 rounded-full flex items-center justify-center"
                  >
                    <component :is="update.icon" class="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <div class="flex items-center text-sm text-gray-500 mb-1">
                      <i-lucide-calendar class="w-4 h-4 mr-1" />
                      {{ update.date }}
                    </div>
                    <h4 class="font-medium text-gray-900">{{ update.title }}</h4>
                    <p class="text-gray-600 mt-1">{{ update.description }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 主要内容区域 -->
          <div class="space-y-8">
            <section
              v-for="(section, index) in sections"
              :key="index"
              :id="section.id"
              class="bg-white rounded-xl shadow-sm border border-gray-100 p-8 hover:shadow-md transition-shadow duration-300"
            >
              <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                  <component :is="section.icon" class="w-8 h-8 text-blue-600 mr-3" />
                  <h2 class="text-2xl font-semibold text-gray-900">
                    {{ section.title }}
                  </h2>
                </div>
                <button
                  @click="copySection(section.id)"
                  class="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                  :title="'复制内容'"
                >
                  <i-lucide-copy class="w-5 h-5" />
                </button>
              </div>
              <div class="prose max-w-none text-gray-600">
                <p class="leading-relaxed">{{ section.content }}</p>
                <ul v-if="section.bullets" class="mt-4 space-y-2">
                  <li v-for="(bullet, idx) in section.bullets" :key="idx" class="flex items-start">
                    <i-lucide-check-circle
                      class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0"
                    />
                    <span>{{ bullet }}</span>
                  </li>
                </ul>
              </div>
            </section>
          </div>

          <!-- FAQ部分 -->
          <div class="mt-12 bg-white rounded-xl shadow-sm border border-gray-100 p-8">
            <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
              <i-lucide-help-circle class="mr-2 text-blue-600" />
              常见问题
            </h3>
            <div class="space-y-4">
              <div
                v-for="(faq, index) in faqs"
                :key="index"
                class="border-b border-gray-100 last:border-0 pb-4 last:pb-0"
              >
                <button
                  class="w-full text-left flex items-center justify-between py-2"
                  @click="faq.isOpen = !faq.isOpen"
                >
                  <span class="font-medium text-gray-900">{{ faq.question }}</span>
                  <i-lucide-chevron-down
                    class="w-5 h-5 text-gray-500 transition-transform duration-200"
                    :class="{ 'transform rotate-180': faq.isOpen }"
                  />
                </button>
                <div v-show="faq.isOpen" class="text-gray-600 pt-2">
                  {{ faq.answer }}
                </div>
              </div>
            </div>
          </div>

          <!-- 底部确认区域 -->
          <div class="mt-12">
            <div class="bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl p-8 text-white">
              <div class="flex flex-col sm:flex-row items-center justify-between gap-6">
                <div class="flex items-center">
                  <i-lucide-shield-check class="w-10 h-10 mr-4" />
                  <div>
                    <h3 class="text-xl font-medium">确认同意</h3>
                    <p class="text-blue-100 mt-1">请仔细阅读并确认同意我们的条款</p>
                  </div>
                </div>
                <button
                  @click="handleAccept"
                  class="px-6 py-3 bg-white text-blue-600 rounded-lg hover:bg-blue-50 transition-colors duration-200 flex items-center group font-medium"
                >
                  <span>我已阅读并同意</span>
                  <i-lucide-arrow-right
                    class="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform"
                  />
                </button>
              </div>
            </div>
          </div>

          <!-- 联系方式 -->
          <div class="mt-12 grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div
              v-for="contact in contacts"
              :key="contact.title"
              class="bg-white rounded-xl p-6 border border-gray-100 hover:shadow-md transition-shadow duration-300"
            >
              <div class="flex items-center mb-4">
                <component :is="contact.icon" class="w-6 h-6 text-blue-600 mr-2" />
                <h4 class="font-medium text-gray-900">{{ contact.title }}</h4>
              </div>
              <p class="text-gray-600">{{ contact.content }}</p>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 返回顶部按钮 -->
    <button
      v-show="showBackToTop"
      @click="scrollToTop"
      class="fixed bottom-8 right-8 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors duration-200 z-20"
    >
      <i-lucide-chevron-up class="w-6 h-6" />
    </button>

    <!-- 移动端菜单按钮 -->
    <button
      v-show="isMobileMenuOpen"
      @click="toggleMobileMenu"
      class="lg:hidden fixed bottom-8 left-8 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors duration-200 z-20"
    >
      <i-lucide-menu class="w-6 h-6" />
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const type = ref(route.query.type || 'privacy')

const lastUpdated = ref('2024年2月23日')
const showBackToTop = ref(false)
const scrollProgress = ref(0)
const currentSection = ref('introduction')
const searchQuery = ref('')
const isLargeFont = ref(false)
const isDark = ref(false)
const isMobileMenuOpen = ref(false)
const readingTime = ref('8')

// 统计数据
const statistics = [
  {
    title: '累计用户同意',
    value: '2.3万+',
    period: '截至目前',
    icon: 'i-lucide-users',
  },
  {
    title: '最近更新条款',
    value: '12条',
    period: '本月',
    icon: 'i-lucide-file-text',
  },
  {
    title: '平均阅读时长',
    value: '6.5分钟',
    period: '统计',
    icon: 'i-lucide-clock',
  },
]

// 更新历史
const updates = [
  {
    date: '2024-02-23',
    title: '隐私政策更新',
    description: '更新了数据处理方式和用户权限相关条款',
    icon: 'i-lucide-shield',
  },
  {
    date: '2024-01-15',
    title: '新增数据保护条款',
    description: '增加了关于用户数据跨境传输的相关规定',
    icon: 'i-lucide-lock',
  },
  {
    date: '2023-12-20',
    title: '条款简化',
    description: '优化了协议文本，使其更易理解',
    icon: 'i-lucide-file-text',
  },
]

// 社交媒体分享
const socials = [
  { name: '微信', icon: 'i-lucide-wechat' },
  { name: '微博', icon: 'i-lucide-send' },
  { name: '链接', icon: 'i-lucide-link' },
]

// 联系方式
const contacts = [
  {
    title: '客户支持',
    content: '工作日 9:00-18:00\n邮箱：<EMAIL>',
    icon: 'i-lucide-headphones',
  },
  {
    title: '隐私咨询',
    content: '如有隐私相关问题，请联系：<EMAIL>',
    icon: 'i-lucide-mail',
  },
]

// FAQ
const faqs = ref([
  {
    question: '如何删除我的账户？',
    answer: '您可以在账户设置中找到"删除账户"选项，按照提示完成操作。账户删除后将无法恢复。',
    isOpen: false,
  },
  {
    question: '数据保留多长时间？',
    answer: '我们会在您使用服务期间保留您的数据，当您删除账户后，数据将在30天内完全删除。',
    isOpen: false,
  },
  {
    question: '如何修改我的个人信息？',
    answer: '登录后访问"个人中心"，您可以修改和更新您的个人信息。某些信息可能需要额外验证。',
    isOpen: false,
  },
])

const sections = ref([
  {
    id: 'introduction',
    title: '1. 简介',
    icon: 'i-lucide-book-open',
    content:
      '欢迎使用我们的服务。本协议旨在说明我们如何收集、使用和保护您的个人信息，以及您使用我们服务时的权利和义务。',
    bullets: [
      '本协议适用于我们提供的所有服务',
      '使用我们的服务即表示您同意本协议',
      '我们可能会定期更新本协议内容',
    ],
  },
  {
    id: 'data-collection',
    title: '2. 信息收集',
    icon: 'i-lucide-database',
    content:
      '我们收集的信息包括但不限于：您的基本账户信息、设备信息、使用记录等。我们承诺只收集必要的信息，并采取严格的保护措施。',
    bullets: ['基本账户信息（姓名、邮箱等）', '设备信息和日志数据', '用户行为和偏好数据'],
  },
  {
    id: 'data-usage',
    title: '3. 信息使用',
    icon: 'i-lucide-settings',
    content:
      '我们使用收集的信息来提供、维护和改进我们的服务，开发新的服务，以及保护我们的用户和公司。我们不会将您的个人信息出售给第三方。',
    bullets: ['服务优化和个性化推荐', '安全防护和风险控制', '产品开发和用户体验改进'],
  },
  {
    id: 'user-rights',
    title: '4. 用户权利',
    icon: 'i-lucide-user',
    content:
      '您有权访问、更正或删除您的个人信息，也可以选择退出某些数据收集项目。我们尊重您的选择，并将及时响应您的请求。',
    bullets: ['查询、更正个人信息的权利', '删除账户的权利', '退出数据收集的选择权'],
  },
  {
    id: 'security',
    title: '5. 安全保护',
    icon: 'i-lucide-shield',
    content:
      '我们采用业界标准的安全措施保护您的个人信息，包括数据加密、访问控制、安全审计等多重保护机制。',
    bullets: ['全程数据加密传输', '严格的访问权限控制', '定期安全审计和评估'],
  },
])

// 过滤后的章节
const filteredSections = computed(() => {
  if (!searchQuery.value) return sections.value
  return sections.value.filter(
    (section) =>
      section.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      section.content.toLowerCase().includes(searchQuery.value.toLowerCase()),
  )
})

// 获取当前章节编号
const getCurrentSectionNumber = () => {
  const index = sections.value.findIndex((section) => section.id === currentSection.value)
  return index + 1
}

const scrollToSection = (sectionId) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

const handleAccept = () => {
  alert('感谢您的确认！')
}

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth',
  })
}

const toggleFontSize = () => {
  isLargeFont.value = !isLargeFont.value
}

const toggleTheme = () => {
  isDark.value = !isDark.value
  document.documentElement.classList.toggle('dark')
}

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const copySection = async (sectionId) => {
  const section = sections.value.find((s) => s.id === sectionId)
  if (section) {
    try {
      await navigator.clipboard.writeText(section.content)
      alert('内容已复制到剪贴板')
    } catch (err) {
      alert('复制失败，请手动复制')
    }
  }
}

const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
  const winScroll = document.documentElement.scrollTop
  const height = document.documentElement.scrollHeight - document.documentElement.clientHeight
  scrollProgress.value = (winScroll / height) * 100

  sections.value.forEach((section) => {
    const element = document.getElementById(section.id)
    if (element) {
      const rect = element.getBoundingClientRect()
      if (rect.top <= 100 && rect.bottom >= 100) {
        currentSection.value = section.id
      }
    }
  })
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.prose p {
  line-height: 1.75;
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 暗色模式 */
:root.dark {
  --bg-color: #1a1a1a;
  --text-color: #ffffff;
}

.dark {
  background-color: var(--bg-color);
  color: var(--text-color);
}
</style>
