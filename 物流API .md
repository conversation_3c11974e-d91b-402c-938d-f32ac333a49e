# API 快速入门

51Tracking帮助电子商务企业高效地更新和管理他们的货物。

您可以使用51Tracking的查询API无缝管理物流轨迹，并从管理门户使用51Tracking的购买服务。或者您可以通过API和webhook存储物流轨迹数据，并自行定制购买服务。

本快速入门指南引导你完成 3 种常见方案，让你了解如何使用 51Tracking 来创建和管理物流轨迹。

------

## [1. 什么是物流追踪？](https://www.51tracking.com/v4/api-index/ze8gi70a568gu-#1-什么是物流追踪)

追踪是指与货件相关的信息，包括快递员的姓名、快递员提供的追踪号码和物流节点。

对于包含“跟踪”的所有字段，请参阅跟踪对象。

## [2. 获取 API 密钥](https://www.51tracking.com/v4/api-index/ze8gi70a568gu-#2-获取-api-密钥)

51Tracking 通过在标头中添加`Tracking-Api-Key`来验证用户的请求。

要获取您的 API 密钥，请注册51Tracking账户，注册成功后自动生成属于您的API Key，同时也可在账户后台生成新的API Key。

[获取 API Key](https://user.51tracking.com/developer/apikey)

## [3. API 端点](https://www.51tracking.com/v4/api-index/ze8gi70a568gu-#3-api-端点)

https://api.51Tracking.com/v4



所有端点只能通过`HTTPS`访问，并位于`api.51Tracking.com`

## [4. 常见场景](https://www.51tracking.com/v4/api-index/ze8gi70a568gu-#4-常见场景)

### [场景 1. 创建物流单号进行查询](https://www.51tracking.com/v4/api-index/ze8gi70a568gu-#场景-1-创建物流单号进行查询)

使用 POST API [https://api.51Tracking.com/v4/trackings/create](https://api.51tracking.com/v4/trackings/create)

API document

```
curl --location --request POST 'https://api.51Tracking.com/v4/trackings/create' \

--header 'Tracking-Api-Key: your 51Tracking api key' \

--header 'Content-Type: application/json' \

--data'{

        "tracking_number": "9261290312833844954982",

        "courier_code": "usps"

}'
```

创建物流单号追踪事件时，我们建议您为我们提供快递单号（必填）、物流商简码（必填）和其他快递信息（选填）。

**以下是提供快递员信息的 2 种方法**

1 . 如果您知道具体的快递公司，请填写该字段（例如，ups-mi） `courier_code`

2 . 对于某些物流商，必须填写一些额外的字段进行查询追踪，否则您的跟踪将无法创建。

请查看我们的**特殊物流商字段** 以获取必填字段。

### [场景 2.更新物流单号信息](https://www.51tracking.com/v4/api-index/ze8gi70a568gu-#场景-2更新物流单号信息)

使用 API [https://api.51Tracking.com/v4/trackings/update/id](https://api.51tracking.com/v4/trackings/update/id)

如果您要更新订单信息和物流单号添加额外信息，可以**使用该API接口进行更新**。

### [场景 3.获取有关物流轨迹的更新](https://www.51tracking.com/v4/api-index/ze8gi70a568gu-#场景-3获取有关物流轨迹的更新)

**1 . 通过 API 获取物流轨迹**

若要获取最新的物流轨迹节点，请使用 GET API 接口 [https://api.51Tracking.com/v4/trackings/get?tracking_numbers=9261290312833844954982](https://api.51tracking.com/v4/trackings/get?tracking_numbers=9261290312833844954982)

**注意**: GET API 每秒限制 2 个请求，如果超出，将显示错误。

**请求示例**

```
curl --location --request GET 'https://api.51tracking.com/v4/trackings/get?tracking_numbers=9261290312833844954982' \

--header 'Tracking-Api-Key: your 51Tracking api key'

--header 'Content-Type: application/json'
```

**响应示例**

```
{

    "meta": {

        "code": 200,

        "message": "Request response is successful"

    },

    "data": {

      "success": [

      {

        "id": "string",

        "tracking_number": "string",

        "courier_code": "string",

        "order_number": "string",

        "shipping_date": "string",

        "order_date": "string",

        "create_at": "string",

        "update_at": "string",

        "delivery_status": "string",

        "archived": "tracking",

        "updating": "true",

        "destination_country": "string",

        "destination_state": "string",

        "destination_city": "string",

        "origin_country": "string",

        "origin_state": "string",

        "origin_city": "string",

        "tracking_postal_code": "string",

        "tracking_ship_date": "string",

        "tracking_destination_country": "string",

        "tracking_origin_country": "string",

        "tracking_key": "string",

        "tracking_courier_account": "string",

        "customer_name": "string",

        "customer_email": "string",

        "customer_sms": "string",

        "title": "string",

        "order_id": "string",

        "logistics_channel": "string",

        "note": "string",

        "signed_by": "string",

        "service_code": "string",

        "weight": "string",

        "weight_kg": "string",

        "product_type": "string",

        "pieces": "string",

        "dimension": "string",

        "destination_track_number": "string",

        "exchange_number": "string",

        "scheduled_delivery_date": "string",

        "scheduled_address": "string",

        "substatus": "string",

        "status_info": "string",

        "latest_event": "string",

        "latest_checkpoint_time": "string",

        "transit_time": "string",

        "origin_info": {

          "courier_code": "string",

          "courier_phone": "string",

          "weblink": "string",

          "reference_number": "string",

          "pickup_date": "string",

          "departed_airport_date": "string",

          "arrived_abroad_date": "string",

          "customs_received_date": "string",

          "trackinfo": [

            {

              "checkpoint_date": "string",

              "checkpoint_delivery_status": "string",

              "checkpoint_delivery_substatus": "string",

              "tracking_detail": "string",

              "location": "string",

              "country_iso2": "string",

              "state": "string",

              "city": "string",

              "zip": "string",

              "raw_status": "string"

            }

          ]

        },

        "destination_info": {

          "courier_code": "string",

          "courier_phone": "string",

          "weblink": "string",

          "reference_number": "string",

          "pickup_date": "string",

          "departed_airport_date": "string",

          "arrived_abroad_date": "string",

          "customs_received_date": "string",

          "trackinfo": [

            {

              "checkpoint_date": "string",

              "checkpoint_delivery_status": "string",

              "checkpoint_delivery_substatus": "string",

              "tracking_detail": "string",

              "location": "string",

              "country_iso2": "string",

              "state": "string",

              "city": "string",

              "zip": "string",

              "raw_status": "string"

            }

          ]

        }

      }

    ],

    "rejected": [

      {

        "tracking_number": "string",

        "rejectedCode": 4102,

        "rejectedMessage": "string"

      }

    ]

  }

}
```