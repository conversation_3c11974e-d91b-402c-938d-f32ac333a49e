<template>
  <div class="h-full p-8">
    <!-- 页面标题和说明 -->
    <div class="mb-10">
      <h2
        class="text-3xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent"
      >
        账户安全中心
      </h2>
      <p class="mt-2 text-gray-600">请定期检查并更新您的安全设置，保护账户安全</p>
    </div>

    <!-- 主要内容区域 - 使用网格布局 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- 左侧安全设置区域 -->
      <div class="space-y-8">
        <h3 class="text-xl font-semibold text-gray-800 mb-8">基础安全设置</h3>

        <!-- 密码安全 -->
        <div class="transform hover:scale-[1.01] transition-all duration-300">
          <div
            class="flex justify-between items-center p-8 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl border border-purple-100 shadow-sm hover:shadow-md"
          >
            <div class="flex items-center gap-4">
              <div class="p-3 bg-purple-100 rounded-lg">
                <el-icon class="text-2xl text-purple-600"><Lock /></el-icon>
              </div>
              <div>
                <h3 class="font-semibold text-lg">登录密码</h3>
                <p class="text-gray-600 mt-1">建议使用高强度的密码组合</p>
                <p class="text-xs text-gray-500 mt-1">上次修改时间：2024-02-25</p>
              </div>
            </div>
            <el-button
              type="primary"
              class="!bg-purple-600 hover:!bg-purple-700 !border-none"
              @click="showPasswordDialog = true"
            >
              修改密码
            </el-button>
          </div>
        </div>

        <!-- 手机绑定 -->
        <div class="transform hover:scale-[1.01] transition-all duration-300">
          <div
            class="flex justify-between items-center p-8 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl border border-blue-100 shadow-sm hover:shadow-md"
          >
            <div class="flex items-center gap-4">
              <div class="p-3 bg-blue-100 rounded-lg">
                <el-icon class="text-2xl text-blue-600"><Phone /></el-icon>
              </div>
              <div>
                <h3 class="font-semibold text-lg">手机绑定</h3>
                <p class="text-gray-600 mt-1">已绑定</p>
                <p class="text-xs text-gray-500 mt-1">可用于登录、找回密码等操作</p>
              </div>
            </div>
            <el-button
              type="primary"
              class="!bg-blue-600 hover:!bg-blue-700 !border-none"
              @click="handleChangeClick('phone')"
            >
              更换手机
            </el-button>
          </div>
        </div>

        <!-- 邮箱绑定 -->
        <div class="transform hover:scale-[1.01] transition-all duration-300">
          <div
            class="flex justify-between items-center p-8 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100 shadow-sm hover:shadow-md"
          >
            <div class="flex items-center gap-4">
              <div class="p-3 bg-green-100 rounded-lg">
                <el-icon class="text-2xl text-green-600"><Message /></el-icon>
              </div>
              <div>
                <h3 class="font-semibold text-lg">邮箱绑定</h3>
                <p class="text-gray-600 mt-1">已绑定</p>
                <p class="text-xs text-gray-500 mt-1">可用于接收重要通知</p>
              </div>
            </div>
            <el-button
              type="primary"
              class="!bg-green-600 hover:!bg-green-700 !border-none"
              @click="handleChangeClick('email')"
            >
              更换邮箱
            </el-button>
          </div>
        </div>

        <!-- 安全提示卡片 -->
        <div
          class="mt-8 p-6 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl border border-purple-100 shadow-sm"
        >
          <div class="flex items-start gap-4">
            <div class="p-3 bg-purple-100 rounded-lg shrink-0">
              <el-icon class="text-2xl text-purple-600"><Warning /></el-icon>
            </div>
            <div>
              <h3 class="font-semibold text-lg text-purple-800">安全小贴士</h3>
              <p class="text-gray-600 mt-2">
                定期更新您的安全信息，避免使用简单密码，不要在不信任的设备上保存登录状态。
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧安全信息区域 -->
      <div class="space-y-8">
        <h3 class="text-xl font-semibold text-gray-800 mb-8">安全信息</h3>

        <!-- 登录信息 -->
        <div class="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl p-8">
          <div class="flex items-center gap-4 mb-6">
            <div class="p-3 bg-orange-100 rounded-lg">
              <el-icon class="text-2xl text-orange-600"><Timer /></el-icon>
            </div>
            <div>
              <h4 class="font-semibold text-lg">登录信息</h4>
              <p class="text-gray-600 text-sm">
                上次登录时间：{{ formatDate(userInfo.lastLoginTime) }}
              </p>
            </div>
          </div>

          <!-- 安全提示 -->
          <div class="space-y-4 mt-6">
            <div class="p-4 bg-red-50 rounded-lg border border-red-100">
              <h5 class="font-medium text-red-700 mb-2">安全提醒</h5>
              <p class="text-red-600 text-sm">请勿在不安全的环境下登录账户</p>
            </div>
            <div class="p-4 bg-blue-50 rounded-lg border border-blue-100">
              <h5 class="font-medium text-blue-700 mb-2">账户保护</h5>
              <p class="text-blue-600 text-sm">建议定期更换密码，确保账户安全</p>
            </div>
          </div>
        </div>

        <!-- 安全操作指南 -->
        <div class="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl p-8">
          <h4 class="font-semibold text-lg mb-6">安全操作指南</h4>
          <div class="space-y-4">
            <div class="flex items-center gap-3 text-gray-700">
              <el-icon class="text-purple-600"><Check /></el-icon>
              <span>定期更换登录密码</span>
            </div>
            <div class="flex items-center gap-3 text-gray-700">
              <el-icon class="text-purple-600"><Check /></el-icon>
              <span>开启手机号验证登录</span>
            </div>
            <div class="flex items-center gap-3 text-gray-700">
              <el-icon class="text-purple-600"><Check /></el-icon>
              <span>不要在公共场所保存登录信息</span>
            </div>
            <div class="flex items-center gap-3 text-gray-700">
              <el-icon class="text-purple-600"><Check /></el-icon>
              <span>及时处理账户安全提醒</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 紧急联系部分 - 移到最下方 -->
    <div class="mt-12 pt-8 border-t border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <h4 class="text-xl font-semibold text-gray-800">遇到问题？</h4>
          <p class="mt-2 text-gray-600">如果发现账户异常，请立即联系客服处理</p>
        </div>
        <div class="flex items-center gap-4">
          <el-button
            type="danger"
            plain
            class="min-w-[120px]"
            @click="handleEmergencyClick('appeal')"
          >
            <el-icon class="mr-1"><Warning /></el-icon>
            账户申诉
          </el-button>
          <el-button
            type="info"
            plain
            class="min-w-[120px]"
            @click="handleEmergencyClick('service')"
          >
            <el-icon class="mr-1"><Service /></el-icon>
            联系客服
          </el-button>
        </div>
      </div>
    </div>

    <!-- 修改密码弹窗 -->
    <el-dialog v-model="showPasswordDialog" title="修改密码" width="500px" class="rounded-xl">
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" class="space-y-6">
        <el-form-item prop="phone" label="手机号">
          <el-input
            v-model="passwordForm.phone"
            placeholder="请输入注册时手机号"
            class="rounded-lg"
          />
        </el-form-item>
        <el-form-item prop="oldPassword" label="当前密码">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            show-password
            class="rounded-lg"
          />
        </el-form-item>
        <el-form-item prop="newPassword" label="新密码">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
            class="rounded-lg"
            @input="checkPasswordStrength"
          />
          <!-- 密码强度指示器 -->
          <div class="mt-2">
            <div class="flex items-center gap-2">
              <div class="text-sm text-gray-600">密码强度：</div>
              <div class="flex gap-1">
                <div
                  class="h-1.5 w-8 rounded transition-all duration-300"
                  :class="[passwordStrength >= 1 ? 'bg-red-500' : 'bg-gray-200']"
                ></div>
                <div
                  class="h-1.5 w-8 rounded transition-all duration-300"
                  :class="[passwordStrength >= 2 ? 'bg-yellow-500' : 'bg-gray-200']"
                ></div>
                <div
                  class="h-1.5 w-8 rounded transition-all duration-300"
                  :class="[passwordStrength >= 3 ? 'bg-green-500' : 'bg-gray-200']"
                ></div>
              </div>
              <span class="text-sm" :class="strengthColorClass">{{ strengthText }}</span>
            </div>
            <div class="mt-1 text-xs text-gray-500">密码必须包含字母和数字，长度至少6位</div>
          </div>
        </el-form-item>
        <el-form-item prop="confirmPassword" label="确认新密码">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
            class="rounded-lg"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="showPasswordDialog = false">取消</el-button>
          <el-button
            type="primary"
            class="!bg-purple-600 hover:!bg-purple-700 !border-none"
            @click="handleChangePassword"
            :disabled="passwordStrength < 2"
          >
            确认修改
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 二维码弹窗 -->
    <el-dialog v-model="showQrCode" title="联系客服" width="360px" class="rounded-xl">
      <div class="flex flex-col items-center">
        <div v-if="qrCodeUrl" class="mb-4">
          <img :src="qrCodeUrl" alt="客服二维码" class="w-64 h-64" />
        </div>
        <p class="text-center text-gray-600 mb-4">扫描二维码联系客服</p>
        <div class="flex items-center justify-center">
          <a
            href="https://work.weixin.qq.com/kfid/kfcbce26add2a69c670"
            target="_blank"
            class="text-purple-600 hover:text-purple-800"
          >
            点击此处直接访问客服
          </a>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import {
  Lock,
  Phone,
  Message,
  Timer,
  Check,
  Warning,
  Service,
  UserFilled,
} from '@element-plus/icons-vue'
import { buyerApi } from '@/api/buyer'
import type { PasswordChangeDTO, PasswordChangeResponse } from '@/api/types'
import QRCode from 'qrcode'

const showPasswordDialog = ref(false)
const showPhoneDialog = ref(false)
const showEmailDialog = ref(false)
const showQrCode = ref(false)
const qrCodeUrl = ref('')

// 用户信息
const userInfo = ref({
  phone: localStorage.getItem('userPhone') || '13800138000',
  email: localStorage.getItem('userEmail') || '<EMAIL>',
  lastLoginTime: localStorage.getItem('lastLoginTime') || new Date().toISOString(),
})

// 密码表单
const passwordForm = reactive({
  phone: '',
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
})

// 添加密码强度相关的响应式变量
const passwordStrength = ref(0)
const strengthText = ref('')
const strengthColorClass = ref('')

// 添加 passwordFormRef 的声明
const passwordFormRef = ref<FormInstance>()

// 检查密码强度
const checkPasswordStrength = () => {
  const password = passwordForm.newPassword
  let strength = 0

  // 检查长度
  if (password.length >= 6) strength++

  // 检查是否包含数字
  if (/\d/.test(password)) strength++

  // 检查是否包含字母
  if (/[a-zA-Z]/.test(password)) strength++

  // 更新密码强度
  passwordStrength.value = strength

  // 更新强度文本和颜色
  switch (strength) {
    case 0:
    case 1:
      strengthText.value = '弱'
      strengthColorClass.value = 'text-red-500'
      break
    case 2:
      strengthText.value = '中'
      strengthColorClass.value = 'text-yellow-500'
      break
    case 3:
      strengthText.value = '强'
      strengthColorClass.value = 'text-green-500'
      break
  }
}

// 修改密码验证规则
const passwordRules = {
  phone: [
    { required: true, message: '请输入注册手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
  ],
  oldPassword: [{ required: true, message: '请输入当前密码', trigger: 'blur' }],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' },
    {
      validator: (rule: unknown, value: string, callback: (error?: Error) => void) => {
        if (!/\d/.test(value) || !/[a-zA-Z]/.test(value)) {
          callback(new Error('密码必须包含字母和数字'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: unknown, value: string, callback: (error?: Error) => void) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString()
}

// 手机号码脱敏
const maskPhone = (phone: string) => {
  if (!phone) return '-'
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 邮箱脱敏
const maskEmail = (email: string) => {
  if (!email) return '-'
  const [username, domain] = email.split('@')
  return `${username.charAt(0)}****@${domain}`
}

// 修改密码处理函数
const handleChangePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()

    // 构造请求数据
    const requestData = {
      phone: passwordForm.phone,
      oldPassword: passwordForm.oldPassword,
      newPassword: passwordForm.newPassword,
    }
    console.log('Request data:', requestData)

    // 调用修改密码API
    const response = await buyerApi.changePassword(requestData)
    console.log('Response:', response)

    // 根据后端返回的 code 显示对应消息
    if (response.data.code === 0) {
      ElMessage({
        message: response.data.msg || '修改失败',
        type: 'error',
        duration: 3000,
        showClose: true,
      })
    } else if (response.data.code === 1) {
      ElMessage({
        message: response.data.msg || '密码修改成功',
        type: 'success',
        duration: 3000,
        showClose: true,
      })
      showPasswordDialog.value = false
      // 重置表单
      passwordForm.phone = ''
      passwordForm.oldPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
    }
  } catch (error: any) {
    console.error('Password change error:', error)
    ElMessage({
      message: error.response?.data?.msg || '修改失败，请重试',
      type: 'error',
      duration: 3000,
      showClose: true,
    })
  }
}

// 生成随机二维码的函数
const generateQrCode = async () => {
  try {
    // 企业微信客服链接
    const url = 'https://work.weixin.qq.com/kfid/kfcbce26add2a69c670'

    // 生成随机参数，确保每次扫码都是新的
    const randomParam = `?t=${Date.now()}-${Math.floor(Math.random() * 1000)}`

    // 生成二维码
    const qrDataUrl = await QRCode.toDataURL(url + randomParam, {
      errorCorrectionLevel: 'H',
      margin: 1,
      width: 256,
      color: {
        dark: '#10B981', // 绿色
        light: '#FFFFFF', // 白色
      },
    })

    qrCodeUrl.value = qrDataUrl
  } catch (error) {
    console.error('生成二维码失败:', error)
    ElMessage.error('生成二维码失败，请重试')
  }
}

// 修改处理紧急联系点击的方法
const handleEmergencyClick = (type: 'appeal' | 'service') => {
  if (type === 'appeal') {
    ElMessage({
      message: '账户申诉功能暂未开放，请稍后尝试',
      type: 'info',
      duration: 3000,
      showClose: true,
    })
  } else {
    // 显示二维码弹窗
    showQrCode.value = true
    // 生成二维码
    generateQrCode()
  }
}

// 监听弹窗显示状态，当弹窗打开时生成新的二维码
watch(showQrCode, (newVal) => {
  if (newVal) {
    generateQrCode()
  }
})

// 处理更换手机和更换邮箱的点击事件
const handleChangeClick = (type: 'phone' | 'email') => {
  ElMessage({
    message: `暂不支持${type === 'phone' ? '更换手机号' : '更换邮箱'}，请稍后再试`,
    type: 'info',
    duration: 3000,
    showClose: true,
  })
  if (type === 'phone') {
    showPhoneDialog.value = false
  } else {
    showEmailDialog.value = false
  }
}
</script>

<style scoped>
.el-button {
  @apply transition-all duration-300;
}

:deep(.el-dialog) {
  @apply rounded-2xl overflow-hidden;
  min-height: 400px;
}

:deep(.el-dialog__header) {
  @apply bg-gradient-to-r from-purple-50 to-indigo-50 px-6 py-4;
}

:deep(.el-dialog__title) {
  @apply text-lg font-semibold text-gray-800;
}

:deep(.el-dialog__body) {
  @apply p-8;
}

:deep(.el-dialog__footer) {
  @apply px-6 py-4 bg-gray-50;
}

:deep(.el-input__wrapper) {
  @apply rounded-lg shadow-sm;
}

:deep(.el-form-item__label) {
  @apply font-medium text-gray-700 mb-2;
}

/* 添加按钮悬停效果 */
.el-button.el-button--danger.is-plain:hover {
  @apply bg-red-50;
}

.el-button.el-button--info.is-plain:hover {
  @apply bg-gray-50;
}
</style>
