export interface Product {
  id: number
  name: string
  price: number
  image: string
  description?: string
  quantity?: number
}

export interface Order {
  id: number
  items: Array<{
    id: number
    name: string
    price: number
    quantity: number
    image: string
    description?: string
  }>
  totalAmount: number
  status: string
  createTime: string
}

export interface StoreSettings {
  invoice: boolean
  autoConfirm: boolean
  sms: boolean
  inventory: boolean
}
