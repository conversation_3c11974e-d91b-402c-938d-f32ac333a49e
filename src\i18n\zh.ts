export default {
  header: {
    language: '语言',
    userGuide: '更新公告',
    // ... 其他翻译
  },
  auth: {
    login: '登录',
    welcomeBack: '欢迎回来！请输入您的账号信息',
    accountOrEmail: '账号或邮箱',
    accountOrEmailPlaceholder: '请输入账号或邮箱',
    password: '密码',
    loggingIn: '登录中...',
    noAccount: '还没有账号？',
    userRegister: '用户注册',
    sellerRegister: '商家注册',
    forgotUsernameTip: '忘记用户名或邮箱？',
    forgotUsername: '找回用户名',
    forgotPasswordTip: '忘记密码？',
    forgotPassword: '找回密码',
    adminLoginTip: '是否是管理员？',
    adminLogin: '管理员登录入口',
    terms: {
      agreement: '继续即表示您同意',
      userAgreement: '用户协议',
      and: '和',
      privacyPolicy: '隐私政策',
    },
  },
  // ... 其他翻译

  // 隐私政策页面
  privacyPolicy: {
    title: '隐私政策',
    content: '本隐私政策描述了我们如何收集、使用和共享您的个人信息...',
    // ... 其他内容
  },

  // 用户协议页面
  userAgreement: {
    title: '用户协议',
    content: '使用我们的服务，即表示您同意以下条款...',
    // ... 其他内容
  },

  // 找回用户名页面
  forgotUsername: {
    title: '找回用户名',
    subtitle: '请填写以下信息验证身份',
    emailLabel: '邮箱地址',
    emailPlaceholder: '请输入您注册时使用的邮箱',
    verificationCodeLabel: '验证码',
    verificationCodePlaceholder: '请输入验证码',
    sendCode: '发送验证码',
    resendCode: '{seconds}秒后重试',
    codeTip: '验证码将发送至您的邮箱，请注意查收',
    submitButton: '验证身份',
    verifying: '验证中...',
    rememberUsername: '已想起用户名？',
    backToLogin: '返回登录',
    success: '验证成功',
    usernameInfo: '您的用户名是:',
    emailInfo: '您的邮箱是:',
    updateSuccess: '用户名修改成功!',
    setSuccess: '用户名设置成功!',
  },

  // 找回密码页面
  forgotPassword: {
    title: '找回密码',
    subtitle: '请填写以下信息验证身份',
    usernameLabel: '用户名',
    usernamePlaceholder: '请输入您的用户名',
    emailLabel: '邮箱地址',
    emailPlaceholder: '请输入您的邮箱地址',
    verificationCodeLabel: '验证码',
    verificationCodePlaceholder: '请输入验证码',
    sendCode: '发送验证码',
    resendCode: '{seconds}秒后重试',
    codeTip: '验证码将发送至您的邮箱，请注意查收',
    submitButton: '验证身份',
    verifying: '验证中...',
    rememberAccount: '已想起账号？',
    backToLogin: '返回登录',
  },

  // 管理员登录页面
  adminLogin: {
    title: '管理员登录',
    // ... 其他内容
  },

  // 更新公告页面
  updateNotification: {
    title: '系统更新公告',
    importantUpdate: '重要更新提醒',
    version: '版本 1.0.1',
    releaseTime: '发布时间',
    systemUpgrade: '系统功能全面升级公告',
    description: '我们很高兴地宣布，系统已重新升级，带来更多新功能与更好的优化。',

    // 标签页
    tabs: {
      features: '更新内容',
      guide: '使用指南',
      feedback: '问题反馈',
    },

    // 功能特性
    features: {
      performance: {
        title: '性能优化',
        description: '系统响应速度大幅提升，页面加载更快',
      },
      interface: {
        title: '界面升级',
        description: '全新的用户界面设计，提供更好的视觉体验',
      },
      security: {
        title: '安全增强',
        description: '新增多重安全防护机制，保护用户数据安全',
      },
      ai: {
        title: '智能推荐',
        description: '基于AI的智能推荐系统，提供个性化服务',
      },
    },

    // 使用指南
    guide: {
      title: '使用指南',
      steps: [
        '更新到最新版本以体验全新功能',
        '查看新功能教程了解详细使用方法',
        '遇到问题及时反馈，我们将持续优化',
        '关注系统公告，获取最新更新信息',
      ],
    },

    // 问题反馈
    feedback: {
      title: '问题反馈',
      placeholder: '请输入您的反馈...',
      submit: '提交反馈',
      success: '感谢您的反馈！',
      empty: '请输入反馈内容',
    },

    // 底部操作
    actions: {
      like: '点赞',
      bookmark: '收藏',
      share: '分享更新',
      shareMessage: '分享功能开发中...',
    },

    // 相关更新
    relatedUpdates: {
      mobileOptimization: {
        title: '移动端适配优化',
        description: '优化移动端显示效果，提升用户体验',
        tag: '性能优化',
      },
      dataAnalysis: {
        title: '新增数据分析功能',
        description: '支持更多数据维度的分析与可视化',
        tag: '功能更新',
      },
    },
  },

  // 用户注册页面
  register: {
    title: '用户注册',
    subtitle: '创建您的账户，开启全新体验',
    accountNamePlaceholder: '请输入账号名',
    passwordPlaceholder: '请输入密码',
    confirmPasswordPlaceholder: '请确认密码',
    passwordStrength: '密码强度',
    strength: {
      weak: '弱',
      medium: '中',
      strong: '强',
    },
    passwordRules: {
      minLength: '至少8个字符',
      upperCase: '至少包含一个大写字母',
      lowerCase: '至少包含一个小写字母',
      number: '至少包含一个数字',
    },
    gender: {
      male: '男',
      female: '女',
    },
    phonePlaceholder: '请输入手机号',
    emailPlaceholder: '请输入邮箱，用于接收验证码',
    emailTip: '请填写真实邮箱，验证码将通过邮箱发送',
    verificationCodePlaceholder: '请输入邮箱验证码',
    sendCode: '发送邮箱验证码',
    resendCode: '{seconds}秒后重试',
    sendingCode: '正在发送验证码...',
    codeTip: '验证码将发送至您的邮箱',
    checkSpamFolder: '如果您没有收到验证码邮件，请检查您的垃圾邮箱',
    inviteCodePlaceholder: '请输入邀请码（选填）',
    inviteCodeTip: '如果您有邀请码，可以在此处填写',
    registerButton: '注册',
    registering: '注册中...',
    haveAccount: '已有账号？',
    loginNow: '立即登录',
  },

  // 商家注册页面
  sellerRegister: {
    title: '商家注册',
    subtitle: '创建您的商家账户，开启经营之旅',
    shopNamePlaceholder: '请输入店铺名称',
    mobilePlaceholder: '请输入联系电话',
    contactNamePlaceholder: '请输入联系人姓名',
    provincePlaceholder: '请输入省份',
    cityPlaceholder: '请输入城市',
    detailAddressPlaceholder: '请输入详细地址',
    submitButton: '提交申请',
    submitting: '申请中...',
    haveAccount: '已有商家账号？',
    loginNow: '立即登录',
  },
}
