<template>
  <div class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-2xl font-bold text-gray-900">订单物流追踪</h1>
        <p class="mt-2 text-sm text-gray-600">查看您的订单物流状态和配送进度</p>
      </div>

      <!-- 订单信息卡片 -->
      <div v-if="orderInfo" class="bg-white rounded-lg shadow-md p-6 mb-8">
        <div class="flex justify-between items-start">
          <div>
            <h2 class="text-lg font-semibold text-gray-800">订单信息</h2>
            <div class="mt-4 space-y-2">
              <p class="text-sm text-gray-600">
                订单编号: <span class="font-medium text-gray-900">{{ orderInfo.orderNumber }}</span>
              </p>
              <p class="text-sm text-gray-600">
                下单时间:
                <span class="font-medium text-gray-900">{{
                  formatDate(orderInfo.createTime)
                }}</span>
              </p>
              <p class="text-sm text-gray-600">
                收货地址: <span class="font-medium text-gray-900">{{ orderInfo.address }}</span>
              </p>
            </div>
          </div>

          <div class="text-right">
            <span
              :class="[
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                getOrderStatusClass(orderInfo.orderStatus),
              ]"
            >
              {{ getOrderStatusText(orderInfo.orderStatus) }}
            </span>
            <p class="mt-2 text-sm text-gray-600">
              订单金额:
              <span class="font-medium text-gray-900">¥{{ orderInfo.totalAmount.toFixed(2) }}</span>
            </p>
          </div>
        </div>
      </div>

      <!-- 物流追踪组件 -->
      <div v-if="orderInfo && orderInfo.trackingNumber">
        <TrackingInfo :order-id="orderId" :tracking-number="orderInfo.trackingNumber" />
      </div>

      <!-- 添加物流信息表单 -->
      <div
        v-else-if="orderInfo && !orderInfo.trackingNumber"
        class="bg-white rounded-lg shadow-md p-6"
      >
        <h2 class="text-lg font-semibold text-gray-800 mb-4">添加物流信息</h2>

        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div>
            <label for="tracking_number" class="block text-sm font-medium text-gray-700"
              >物流单号</label
            >
            <input
              v-model="trackingForm.tracking_number"
              id="tracking_number"
              type="text"
              class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500"
              placeholder="请输入物流单号"
              required
            />
          </div>

          <div>
            <label for="courier_code" class="block text-sm font-medium text-gray-700"
              >物流公司</label
            >
            <select
              v-model="trackingForm.courier_code"
              id="courier_code"
              class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500"
              required
            >
              <option value="" disabled>请选择物流公司</option>
              <option value="usps">USPS</option>
              <option value="ups">UPS</option>
              <option value="fedex">FedEx</option>
              <option value="dhl">DHL</option>
              <option value="sf-express">顺丰速运</option>
              <option value="china-post">中国邮政</option>
              <option value="ems">EMS</option>
              <option value="yto">圆通速递</option>
              <option value="sto">申通快递</option>
              <option value="zto">中通快递</option>
              <option value="yunda">韵达快递</option>
              <option value="jd">京东物流</option>
            </select>
          </div>

          <div class="flex justify-end">
            <button
              type="submit"
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              :disabled="submitting"
            >
              <svg
                v-if="submitting"
                class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              {{ submitting ? '提交中...' : '添加物流信息' }}
            </button>
          </div>
        </form>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"></div>
      </div>

      <!-- 错误提示 -->
      <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 my-4">
        <div class="flex">
          <svg class="h-5 w-5 text-red-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
            <path
              fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clip-rule="evenodd"
            ></path>
          </svg>
          <div>
            <h3 class="text-sm font-medium text-red-800">出错了</h3>
            <p class="mt-1 text-sm text-red-700">{{ error }}</p>
          </div>
        </div>
      </div>

      <!-- 返回按钮 -->
      <div class="mt-8 flex justify-center">
        <button
          @click="goBack"
          class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
        >
          <svg
            class="-ml-1 mr-2 h-5 w-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            ></path>
          </svg>
          返回订单列表
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import TrackingInfo from '@/components/TrackingInfo.vue'
import { trackingService } from '@/api/tracking'
import type { TrackingCreateDTO } from '@/api/tracking'

// 假设有一个订单API
// import { orderApi } from '@/api/order'

const route = useRoute()
const router = useRouter()
const orderId = Number(route.params.id)

const orderInfo = ref(null)
const loading = ref(true)
const error = ref(null)
const submitting = ref(false)

// 物流表单
const trackingForm = reactive<TrackingCreateDTO>({
  tracking_number: '',
  courier_code: '',
})

// 获取订单信息
const fetchOrderInfo = async () => {
  loading.value = true
  error.value = null

  try {
    // 实际项目中应该调用API获取订单信息
    // const response = await orderApi.getOrderDetail(orderId)

    // 模拟API响应
    setTimeout(() => {
      // 模拟数据，实际项目中应该使用API返回的数据
      orderInfo.value = {
        id: orderId,
        orderNumber: 'ORD' + String(orderId).padStart(8, '0'),
        createTime: new Date().toISOString(),
        orderStatus: 2, // 1: 待付款, 2: 待发货, 3: 已发货, 4: 已完成, 5: 已取消
        totalAmount: 299.99,
        address: '北京市朝阳区三里屯街道10号楼501室',
        trackingNumber: '', // 如果有物流单号，这里会有值
      }
      loading.value = false
    }, 1000)
  } catch (err) {
    console.error('获取订单信息出错:', err)
    error.value = '获取订单信息失败，请稍后重试'
    loading.value = false
  }
}

// 提交物流信息
const handleSubmit = async () => {
  if (submitting.value) return

  submitting.value = true

  try {
    // 添加订单编号
    trackingForm.order_number = orderInfo.value.orderNumber

    // 调用API创建物流追踪
    const response = await trackingService.createOrderTracking(orderId, trackingForm)

    if (response.code === 1) {
      ElMessage.success('物流信息添加成功')

      // 更新订单信息，添加物流单号
      orderInfo.value.trackingNumber = trackingForm.tracking_number
    } else {
      ElMessage.error(response.msg || '添加物流信息失败')
    }
  } catch (err) {
    console.error('添加物流信息出错:', err)
    ElMessage.error('添加物流信息失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''

  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 获取订单状态对应的样式类
const getOrderStatusClass = (status) => {
  switch (status) {
    case 1: // 待付款
      return 'bg-yellow-100 text-yellow-800'
    case 2: // 待发货
      return 'bg-blue-100 text-blue-800'
    case 3: // 已发货
      return 'bg-purple-100 text-purple-800'
    case 4: // 已完成
      return 'bg-green-100 text-green-800'
    case 5: // 已取消
      return 'bg-gray-100 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取订单状态对应的文本
const getOrderStatusText = (status) => {
  switch (status) {
    case 1:
      return '待付款'
    case 2:
      return '待发货'
    case 3:
      return '已发货'
    case 4:
      return '已完成'
    case 5:
      return '已取消'
    default:
      return '未知状态'
  }
}

// 返回订单列表
const goBack = () => {
  router.push('/dashboard/orders')
}

// 组件挂载时获取订单信息
onMounted(() => {
  fetchOrderInfo()
})
</script>
