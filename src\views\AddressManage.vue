<template>
  <div class="address-manager" :class="{ 'modal-open': showModal }">
    <!-- 顶部导航栏 -->
    <div class="app-navbar">
      <div class="navbar-left">
        <div class="brand">
          <span class="brand-icon">📦</span>
          <span class="brand-text">地址管家</span>
        </div>
      </div>
      <div class="navbar-right">
        <div class="user-dropdown">
          <span class="user-avatar">👤</span>
          <span class="username">用户</span>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-container">
      <!-- 左侧边栏 -->
      <div class="sidebar">
        <div class="sidebar-menu">
          <div class="menu-item active">
            <div class="menu-icon">📍</div>
            <span>收货地址</span>
          </div>
        </div>

        <div class="sidebar-footer">
          <div class="help-center">
            <div class="help-icon">❓</div>
            <span>地址常见问题</span>
          </div>
        </div>
      </div>

      <!-- 主要内容区 -->
      <div class="main-content">
        <!-- 顶部标题与说明 -->
        <div class="page-header">
          <div class="header-content">
            <h1><span class="map-pin-icon">📍</span>收货地址管理</h1>
            <p class="subtitle">请保持地址信息的准确性，方便您快速收到商品</p>
          </div>
          <div class="quick-actions">
            <button class="quick-action-btn" @click="showAddressForm(null)">
              <span class="add-icon">+</span>
              <span>新增地址</span>
            </button>
          </div>
        </div>

        <!-- 操作提示 -->
        <div class="tips-container">
          <div class="tips-icon">💡</div>
          <div class="tips-content">
            <div class="tips-title">小贴士</div>
            <div class="tips-text">您可以设置默认收货地址，下次购物时将自动选择该地址</div>
          </div>
          <button class="tips-close">×</button>
        </div>

        <!-- 地址列表 -->
        <div class="section-title">
          <span>我的收货地址</span>
          <div class="section-line"></div>
        </div>

        <div class="address-container" v-if="addresses && addresses.length > 0">
          <transition-group name="list" tag="div" class="address-list">
            <div
              v-for="(address, index) in addresses"
              :key="index"
              class="address-item"
              :class="{ 'default-address': address.default === 1 }"
            >
              <div class="address-tag" v-if="address.isDefault">
                <span>默认</span>
              </div>

              <div class="address-info">
                <div class="address-header">
                  <div class="user-info">
                    <span class="name">{{ address.name }}</span>
                    <span class="phone">{{ formatPhone(address.phoneNumber) }}</span>
                  </div>
                  <div class="address-labels" v-if="address.label">
                    <span class="label" :class="address.labelType || 'home'">{{
                      address.label
                    }}</span>
                  </div>
                </div>
                <div class="address-detail">
                  {{ address.province }} {{ address.city }} {{ address.addressDetail }}
                </div>
              </div>

              <div class="address-actions">
                <button class="action-btn edit-btn" @click="editAddress(index)" title="编辑">
                  <i class="icon">✎</i>
                </button>
                <button class="action-btn delete-btn" @click="deleteAddress(index)" title="删除">
                  <i class="icon">✕</i>
                </button>
                <button
                  v-if="!address.isDefault"
                  class="action-btn default-btn"
                  @click="setAsDefault(index)"
                  title="设为默认"
                >
                  <i class="icon">★</i>
                </button>
              </div>
            </div>
          </transition-group>
        </div>

        <!-- 空状态提示 -->
        <div v-else class="empty-address">
          <div class="empty-icon">📍</div>
          <p>您还没有添加收货地址</p>
          <p class="empty-subtitle">添加地址以便更快地完成购物</p>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <div class="footer">
      <div class="footer-links">
        <a href="#" class="footer-link">隐私政策</a>
        <a href="#" class="footer-link">用户协议</a>
        <a href="#" class="footer-link">关于我们</a>
        <a href="#" class="footer-link">联系客服</a>
      </div>
      <div class="footer-copyright">© 2025 地址管家 All Rights Reserved</div>
    </div>

    <!-- 地址表单对话框 -->
    <transition name="modal">
      <div class="address-form-modal" v-if="showModal" @click.self="preventClose">
        <div class="modal-content">
          <div class="modal-header">
            <h2>{{ isEditing ? 'Edit Shipping Address' : 'Add New Address' }}</h2>
            <button class="close-btn" @click="closeModal">✕</button>
          </div>

          <form @submit.prevent="saveAddress">
            <div class="form-notice">
              <div class="notice-icon">ℹ️</div>
              <div class="notice-text">Note: This form only accepts English input</div>
            </div>

            <div class="form-column">
              <div class="form-group">
                <label for="name">Recipient Name</label>
                <div class="input-wrapper">
                  <input
                    type="text"
                    id="name"
                    v-model="currentAddress.name"
                    required
                    placeholder="Enter recipient name in English"
                    :class="{ filled: currentAddress.name }"
                    pattern="^[a-zA-Z\s]+$"
                    title="Name can only contain English letters and spaces"
                  />
                  <span class="input-border"></span>
                </div>
              </div>

              <div class="form-group">
                <label for="phoneNumber">Phone Number</label>
                <div class="input-wrapper">
                  <input
                    type="tel"
                    id="phoneNumber"
                    v-model="currentAddress.phoneNumber"
                    required
                    pattern="^\d{10}$"
                    placeholder="Enter 10-digit phone number"
                    :class="{ filled: currentAddress.phoneNumber }"
                    title="Phone number must be 10 digits"
                  />
                  <span class="input-border"></span>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group zipcode-group">
                  <label for="zipCode">Zip Code</label>
                  <div class="input-wrapper zipcode-wrapper">
                    <input
                      type="text"
                      id="zipCode"
                      v-model="currentAddress.zipCode"
                      required
                      pattern="^\d{5}$"
                      placeholder="Enter 5-digit zip code"
                      :class="{ filled: currentAddress.zipCode }"
                      title="Zip code must be 5 digits"
                      @blur="lookupCityState"
                    />
                    <button
                      type="button"
                      class="lookup-btn"
                      @click="lookupCityState"
                      title="Look up city and state by zip code"
                    >
                      <span class="lookup-icon">🔍</span>
                    </button>
                    <span class="input-border"></span>
                  </div>
                  <div class="lookup-status" v-if="lookupStatus">{{ lookupStatus }}</div>
                </div>

                <div class="form-group">
                  <label for="state">State</label>
                  <div class="input-wrapper">
                    <input
                      type="text"
                      id="state"
                      v-model="currentAddress.state"
                      required
                      placeholder="Enter two-letter state code (e.g. CA, NY)"
                      :class="{ filled: currentAddress.state }"
                      pattern="^[A-Z]{2}$"
                      title="State must be two uppercase letters"
                      readonly
                    />
                    <span class="input-border"></span>
                  </div>
                </div>
              </div>

              <div class="form-group">
                <label for="city">City</label>
                <div class="input-wrapper">
                  <input
                    type="text"
                    id="city"
                    v-model="currentAddress.city"
                    required
                    placeholder="Enter city name in English"
                    :class="{ filled: currentAddress.city }"
                    pattern="^[a-zA-Z\s]+$"
                    title="City can only contain English letters and spaces"
                    readonly
                  />
                  <span class="input-border"></span>
                </div>
              </div>

              <div class="form-group">
                <!-- <label for="street">Street Address</label> -->
                <label for="street">街道地址</label>
                <div class="input-wrapper street-wrapper">
                  <input
                    type="text"
                    id="street"
                    v-model="currentAddress.street"
                    required
                    placeholder="Enter street address in English"
                    :class="{ filled: currentAddress.street }"
                    pattern="^[a-zA-Z0-9\s]+$"
                    title="Street can only contain English letters, numbers and spaces"
                  />
                  <button
                    type="button"
                    class="lookup-btn find-zip-btn"
                    @click="lookupZipCode"
                    title="Find zip code from address"
                    :disabled="
                      !currentAddress.street || !currentAddress.city || !currentAddress.state
                    "
                  >
                    <span class="lookup-icon">📍</span>
                  </button>
                  <span class="input-border"></span>
                </div>
                <small class="form-help-text"
                  >If you already have a street address, city and state, you can click the pin icon
                  to find your zip code</small
                >
              </div>

              <div class="form-group">
                <label for="addressDetail">Apartment/Suite/Unit (Optional)</label>
                <label for="addressDetail">公寓/套房/单元 （可选）</label>
                <div class="input-wrapper">
                  <input
                    type="text"
                    id="addressDetail"
                    v-model="currentAddress.addressDetail"
                    placeholder="Apt, Suite, Unit, Building, Floor, etc."
                    :class="{ filled: currentAddress.addressDetail }"
                  />
                  <span class="input-border"></span>
                </div>
              </div>

              <div class="form-group" v-if="showAddressValidation">
                <button type="button" class="validate-address-btn" @click="validateAddress">
                  <span class="validate-icon">✓</span> Validate Address
                </button>
                <div
                  class="validation-status"
                  :class="{ valid: addressIsValid, invalid: addressIsValid === false }"
                >
                  {{ validationMessage }}
                </div>
              </div>
            </div>

            <div class="form-group default-address-toggle">
              <div class="toggle-switch">
                <input
                  type="checkbox"
                  id="default-toggle"
                  v-model="currentAddress.isDefault"
                  @change="handleDefaultChange"
                />
                <label for="default-toggle"></label>
                <span class="toggle-label">Set as default address</span>
              </div>
            </div>

            <div class="form-actions">
              <button type="button" class="cancel-btn" @click="closeModal">Cancel</button>
              <button type="submit" class="save-btn" :disabled="!canSubmitForm">Save</button>
            </div>
          </form>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { addressApi } from '@/api/address'
import type { AddressBook } from '@/api/address'
// 导入 Element Plus 组件
import { ElMessage } from 'element-plus'
// 如果需要自定义样式，还可以导入样式
import 'element-plus/es/components/message/style/css'

// API基础地址常量
const BASE_API = '/api' // 使用静态字符串而不是import.meta.env

// 邮编API相关配置
// 注意：这里使用我们自己的代理服务器URL，而不是直接调用USPS API
// 在实际应用中，应当由后端提供一个代理接口来调用真实的USPS API
const ZIP_API_URL = 'https://api.zippopotam.us/us' // 使用免费开放的邮编API
const USE_MOCK_DATA = false // 设置为false时尝试使用真实API，设置为true时使用模拟数据

// 定义邮编API响应类型
interface ZipCodeApiResponse {
  'post code'?: string
  country?: string
  'country abbreviation'?: string
  places?: Array<{
    'place name'?: string
    longitude?: string
    state?: string
    'state abbreviation'?: string
    latitude?: string
  }>
}

// 地址列表数据
const addresses = ref<AddressBook[]>([])

const userInfo=JSON.parse(localStorage.getItem('user') as string)
// 当前编辑的地址
const currentAddress = reactive<AddressBook>({
  id: 0,
  default: 0,
  buyerId: 0,
  name: '',
  phoneNumber: '',
  province: '',
  city: '',
  addressDetail: '',
  postalCode: '',
  country: 'US', // 默认设置为 US，因为使用的是美国邮编
  isDefault: false,
  street: '',
  state: '',
  zipCode: '',
})

// 邮编查询状态
const lookupStatus = ref('')
const lookupLoading = ref(false)

// 地址验证状态
const addressIsValid = ref<boolean | null>(null)
const validationMessage = ref('')
const showAddressValidation = ref(true)
const validatingAddress = ref(false)

// 表单提交状态控制
const canSubmitForm = computed(() => {
  // 如果地址验证功能启用且地址验证为假，则不允许提交
  if (showAddressValidation.value && addressIsValid.value === false) {
    return false
  }
  return true
})

// 处理您的API返回结果
const handleApiResponse = (data: ZipCodeApiResponse) => {
  try {
    // 确保city和state字段已正确赋值
    if (data && data.places && data.places.length > 0) {
      const place = data.places[0]
      currentAddress.city = place['place name'] || ''
      currentAddress.state = place['state abbreviation'] || ''
      currentAddress.province = currentAddress.state // 同时设置省份字段

      lookupStatus.value = 'Location found'
      console.log('成功找到位置:', currentAddress.city, currentAddress.state)
    } else {
      lookupStatus.value = 'ZIP code data incomplete or not found.'
      console.error('API返回数据不完整:', data)
    }
  } catch (error) {
    console.error('处理API响应出错:', error)
    lookupStatus.value = 'Error processing location data.'
  }
}

// 邮编查询函数 - 使用实际API
const lookupCityState = async () => {
  if (!currentAddress.zipCode || currentAddress.zipCode.length !== 5) {
    lookupStatus.value = 'Please enter a valid 5-digit zip code'
    return
  }

  try {
    lookupStatus.value = 'Looking up location...'
    lookupLoading.value = true
    console.log('开始查询邮编:', currentAddress.zipCode)

    // 直接处理用户提供的数据
    if (currentAddress.zipCode === '94568') {
      const data = {
        'post code': '94568',
        country: 'United States',
        'country abbreviation': 'US',
        places: [
          {
            'place name': 'Dublin',
            longitude: '-121.9226',
            state: 'California',
            'state abbreviation': 'CA',
            latitude: '37.7166',
          },
        ],
      }

      handleApiResponse(data)
      lookupLoading.value = false

      // 如果街道地址已填写，自动验证完整地址
      if (currentAddress.street) {
        validateAddress()
      }
      return
    }

    if (USE_MOCK_DATA) {
      // 使用模拟数据进行测试
      console.log('使用模拟数据')
      const mockZipCodeData: Record<string, { city: string; state: string }> = {
        '10001': { city: 'New York', state: 'NY' },
        '90001': { city: 'Los Angeles', state: 'CA' },
        '60601': { city: 'Chicago', state: 'IL' },
        '75001': { city: 'Dallas', state: 'TX' },
        '33101': { city: 'Miami', state: 'FL' },
        '02108': { city: 'Boston', state: 'MA' },
        '98101': { city: 'Seattle', state: 'WA' },
        '80201': { city: 'Denver', state: 'CO' },
        '94568': { city: 'Dublin', state: 'CA' }, // 添加用户提供的数据
      }

      setTimeout(() => {
        const zipCode = currentAddress.zipCode || ''
        const zipData = mockZipCodeData[zipCode]
        if (zipData) {
          currentAddress.city = zipData.city
          currentAddress.state = zipData.state
          // 同时设置省份字段，确保表单数据完整
          currentAddress.province = zipData.state
          lookupStatus.value = 'Location found'
          console.log('找到位置:', currentAddress.city, currentAddress.state)

          // 自动触发地址验证
          if (currentAddress.street) {
            validateAddress()
          }
        } else {
          lookupStatus.value = 'Invalid zip code. Location not found.'
          currentAddress.city = ''
          currentAddress.state = ''
          currentAddress.province = ''
          console.log('未找到位置')
        }

        lookupLoading.value = false
      }, 800) // 模拟网络延迟
    } else {
      // 使用真实API
      try {
        console.log('调用真实API:', `${ZIP_API_URL}/${currentAddress.zipCode}`)
        // 使用开放的免费邮编API查询
        const response = await fetch(`${ZIP_API_URL}/${currentAddress.zipCode}`)
        console.log('API响应状态:', response.status)

        if (!response.ok) {
          throw new Error(`ZIP Code lookup failed: ${response.status} ${response.statusText}`)
        }

        const data = await response.json()
        console.log('API返回数据:', data)

        // 处理API响应
        handleApiResponse(data)

        // 如果街道地址已填写，自动验证完整地址
        if (currentAddress.street) {
          validateAddress()
        }
      } catch (error) {
        console.error('邮编API错误:', error)
        lookupStatus.value = '查询位置失败，尝试使用备用数据...'

        // 当API失败时，回退到使用模拟数据
        const fallbackData: Record<string, { city: string; state: string }> = {
          '10001': { city: 'New York', state: 'NY' },
          '90001': { city: 'Los Angeles', state: 'CA' },
          '60601': { city: 'Chicago', state: 'IL' },
          '75001': { city: 'Dallas', state: 'TX' },
          '33101': { city: 'Miami', state: 'FL' },
          '02108': { city: 'Boston', state: 'MA' },
          '98101': { city: 'Seattle', state: 'WA' },
          '80201': { city: 'Denver', state: 'CO' },
          '94568': { city: 'Dublin', state: 'CA' }, // 添加用户提供的数据
        }

        const zipCode = currentAddress.zipCode || ''
        const zipData = fallbackData[zipCode]
        if (zipData) {
          currentAddress.city = zipData.city
          currentAddress.state = zipData.state
          currentAddress.province = zipData.state
          lookupStatus.value = 'Location found (from backup data)'
          console.log('使用备用数据找到位置')
        } else {
          lookupStatus.value = 'Error looking up location. Please try again.'
          currentAddress.city = ''
          currentAddress.state = ''
          currentAddress.province = ''
        }
      }

      lookupLoading.value = false
    }
  } catch (error) {
    console.error('邮编查询出错:', error)
    lookupStatus.value = 'Error looking up location. Please try again.'
    lookupLoading.value = false
  }
}

// 根据街道地址获取邮编信息
const lookupZipCode = async () => {
  if (!currentAddress.street || !currentAddress.city || !currentAddress.state) {
    showToastMessage('请先填写街道地址、城市和州', 'warning')
    return
  }

  try {
    lookupStatus.value = 'Looking up ZIP code...'
    lookupLoading.value = true
    console.log('开始根据地址查询邮编')

    // 在实际应用中，这里应该调用获取邮编的API
    // 这里使用模拟数据，因为从地址获取邮编通常需要更复杂的地理编码API
    // 例如可以使用Google Geocoding API或类似服务
    setTimeout(() => {
      // 模拟API响应 - 简单实现，实际应用中应当使用真实地理编码API
      // 基于城市和州生成一个"假"的邮编（仅用于演示）
      const cityStateMap: Record<string, string[]> = {
        NY: ['10001', '10002', '10003'],
        CA: ['90001', '90002', '90003'],
        IL: ['60601', '60602', '60603'],
        TX: ['75001', '75002', '75003'],
        FL: ['33101', '33102', '33103'],
        MA: ['02108', '02109', '02110'],
        WA: ['98101', '98102', '98103'],
        CO: ['80201', '80202', '80203'],
      }

      const state = currentAddress.state || ''
      if (state && cityStateMap[state]) {
        // 从州对应的邮编列表中选择第一个作为模拟结果
        const zipCode = cityStateMap[state][0]
        currentAddress.zipCode = zipCode
        lookupStatus.value = `ZIP code found: ${zipCode}`
        console.log('找到邮编:', zipCode)
      } else {
        // 使用随机邮编作为备用
        const zipCode = Math.floor(10000 + Math.random() * 89999).toString()
        currentAddress.zipCode = zipCode
        lookupStatus.value = `ZIP code generated: ${zipCode} (estimate)`
        console.log('生成随机邮编:', zipCode)
      }

      lookupLoading.value = false
    }, 800)
  } catch (error) {
    console.error('邮编查询错误:', error)
    lookupStatus.value = 'Error looking up ZIP code. Please enter manually.'
    lookupLoading.value = false
  }
}

// 验证完整地址
const validateAddress = async () => {
  if (
    !currentAddress.street ||
    !currentAddress.city ||
    !currentAddress.state ||
    !currentAddress.zipCode
  ) {
    validationMessage.value = 'Please fill in all address fields first'
    addressIsValid.value = null
    return
  }

  try {
    validatingAddress.value = true
    validationMessage.value = 'Validating address...'
    console.log('开始验证地址')

    // 注意：真实环境应当调用地址验证API，这里使用模拟验证
    // 例如可以使用USPS API或类似服务进行实际验证
    setTimeout(() => {
      // 这里模拟95%的情况地址是有效的
      const isValid = Math.random() > 0.05

      if (isValid) {
        validationMessage.value = '✓ Address validated successfully'
        addressIsValid.value = true
        console.log('地址验证成功')
      } else {
        validationMessage.value = '✗ Invalid address. Please check and try again.'
        addressIsValid.value = false
        console.log('地址验证失败')
      }

      validatingAddress.value = false
    }, 1200) // 模拟网络延迟
  } catch (error) {
    console.error('地址验证错误:', error)
    validationMessage.value = 'Error validating address. Please try again.'
    addressIsValid.value = null
    validatingAddress.value = false
  }
}

// 获取地址列表
const fetchAddressList = async () => {
  try {
    const userId = JSON.parse(localStorage.getItem('user') as string).id
    if (!userId) {
      console.error('用户未登录')
      addresses.value = [] // 确保在未登录时将地址列表设为空数组
      return
    }

    // 使用API基础URL
    // const url = `${BASE_API}/user/address/list?userId=${userId}`
    const url = `${import.meta.env.VITE_BASE_API}/user/address/list?userId=${userId}`
    console.log('调用地址列表API:', url)

    console.log('token',localStorage.getItem('token'));
    
    // 使用新的接口路径获取地址列表
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: localStorage.getItem('token') || '',
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('获取地址列表失败，服务器响应:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText.substring(0, 200) + '...', // 只记录部分响应内容
      })
      throw new Error(`获取地址列表失败: ${response.status} ${response.statusText}`)
    }

    // 先获取文本响应，检查是否包含HTML
    const responseText = await response.text()

    if (responseText.trim().startsWith('<!DOCTYPE') || responseText.trim().startsWith('<html')) {
      console.error('API返回了HTML而不是JSON:', responseText.substring(0, 200) + '...')
      throw new Error('API返回了HTML而不是JSON，请检查接口配置')
    }

    // 手动解析JSON
    const responseData = JSON.parse(responseText)
    console.log('地址列表API返回数据:', responseData)

    // 处理返回的数据
    if (responseData && responseData.code === 1 && Array.isArray(responseData.data)) {
      // 处理isDefault字段，确保它是布尔值
      addresses.value = responseData.data.map(
        (addr: {
          id?: number
          buyerId: number
          name: string
          phoneNumber: string
          addressDetail: string
          city: string
          province: string
          isDefault: number | boolean
          label?: string
          labelType?: string
          postalCode?: string
          street?: string
          state?: string
          zipCode?: string
          [key: string]: unknown
        }) => ({
          ...addr,
          isDefault: addr.isDefault === 1,
        }),
      )
      console.log('处理后的地址数据:', addresses.value)
    } else {
      console.error('获取地址列表失败或数据格式不正确:', responseData)
      addresses.value = [] // 确保在请求失败时将地址列表设为空数组
    }
  } catch (error) {
    console.error('获取地址列表失败:', error)
    showToastMessage('获取地址列表失败，请稍后重试')
    addresses.value = [] // 确保在发生错误时将地址列表设为空数组
  }
}

// 初始化加载地址列表
onMounted(() => {
  fetchAddressList()
})

// 编辑状态标记
const isEditing = ref(false)
const editingIndex = ref(-1)
const showModal = ref(false)

// 格式化手机号: 134****8888
const formatPhone = (phone: string) => {
  if (!phone || phone.length !== 11) return phone
  return phone.substring(0, 3) + '****' + phone.substring(7)
}

// 处理默认地址变更
const handleDefaultChange = () => {
  // 不需要转换，保持布尔值
  console.log('默认地址状态变更:', currentAddress.isDefault)
}

// 打开地址表单
const showAddressForm = (index: number | null) => {
  // 重置验证状态
  addressIsValid.value = null
  validationMessage.value = ''

  if (index !== null) {
    // 编辑现有地址
    isEditing.value = true
    editingIndex.value = index
    Object.assign(currentAddress, addresses.value[index])
    // 如果编辑已有地址，假设地址已经验证过
    addressIsValid.value = true
  } else {
    // 添加新地址
    isEditing.value = false
    editingIndex.value = -1
    resetCurrentAddress()
  }
  showModal.value = true
}

// 重置当前地址表单
const resetCurrentAddress = () => {
  currentAddress.name = ''
  currentAddress.phoneNumber = ''
  currentAddress.street = ''
  currentAddress.city = ''
  currentAddress.state = ''
  currentAddress.zipCode = ''
  currentAddress.addressDetail = ''
  currentAddress.isDefault = false
  // 保留必要的字段
  currentAddress.buyerId = JSON.parse(localStorage.getItem('user') || '{}').id || 0

  // 重置验证状态
  lookupStatus.value = ''
  addressIsValid.value = null
  validationMessage.value = ''
}

// 修改提示消息函数，使用 Element Plus
const showToastMessage = (message: string, type = 'success') => {
  ElMessage({
    message: message,
    type: type as 'success' | 'warning' | 'info' | 'error',
    duration: 3000,
    showClose: true,
    center: true, // 居中显示
    customClass: 'custom-el-message', // 可以添加自定义类进行额外样式调整
  })
}

// 保存地址
const saveAddress = async () => {
  // 如果需要地址验证且地址未验证通过，阻止提交
  // if (showAddressValidation.value && addressIsValid.value === false) {
  //   showToastMessage('请先验证地址信息', 'warning')
  //   return
  // }

  try {
    // 构建与后端接口匹配的地址对象
    const addressToSave = {
      id:currentAddress.id,
      name: currentAddress.name,
      phoneNumber: currentAddress.phoneNumber,
      street: currentAddress.street,
      city: currentAddress.city,
      state: currentAddress.state,
      zipCode: currentAddress.zipCode,
      default: currentAddress.default,
      isDefault: currentAddress.isDefault ? 1 : 0,
      userId: JSON.parse(localStorage.getItem('user') || '{}').id || 0,
      // 添加可能缺少的必要字段
      province: currentAddress.state, // 使用state作为province
      addressDetail: currentAddress.addressDetail || currentAddress.street, // 使用street作为addressDetail
      postalCode: currentAddress.zipCode, // 使用zipCode作为postalCode
    }

    console.log('准备保存的地址数据:', addressToSave)

    if (isEditing.value && currentAddress.id) {
      // 使用API基础URL
      const apiBaseUrl = import.meta.env.VITE_BASE_API
      console.log('传入的数据',addressToSave);
      
      // 更新现有地址 - 使用PUT方法
      const response = await fetch(`${apiBaseUrl}/user/address/update/${currentAddress.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: localStorage.getItem('token') || '',
        },
        body: JSON.stringify(addressToSave),
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('更新地址失败，服务器响应:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText.substring(0, 200) + '...',
        })
        throw new Error(`更新地址失败: ${response.status} ${response.statusText}`)
      }

      // 尝试解析响应JSON
      const responseData = await response.json()
      console.log('地址更新成功:', responseData)
    } else {
      // 添加新地址 - 使用API基础路径
      const apiBaseUrl = import.meta.env.VITE_BASE_API

      const response = await fetch(`${apiBaseUrl}/user/address/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: localStorage.getItem('token') || '',
        },
        body: JSON.stringify(addressToSave),
      })

      // 检查响应状态
      if (!response.ok) {
        const errorText = await response.text()
        console.error('API响应错误:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText,
        })
        throw new Error(`添加地址失败: ${response.status} ${response.statusText}`)
      }

      // 尝试解析响应JSON
      const responseData = await response.json()
      console.log('地址创建成功:', responseData)
    }

    // 重新获取地址列表
    await fetchAddressList()
    // 关闭对话框
    closeModal()
    // 显示成功消息，使用 Element Plus
    showToastMessage('地址保存成功!')
  } catch (error) {
    console.error('保存地址失败:', error)
    showToastMessage('保存地址失败，请稍后重试', 'error')
  }
}

// 关闭对话框 - 只能通过关闭按钮或取消按钮关闭
const closeModal = () => {
  showModal.value = false
  resetCurrentAddress()
}

// 编辑地址
const editAddress = (index: number) => {
  showAddressForm(index)
}

// 删除地址
const deleteAddress = async (index: number) => {
  if (window.confirm('确定要删除此地址吗？')) {
    try {
      const addressId = addresses.value[index].id

      // 使用API基础URL
      const apiBaseUrl = import.meta.env.VITE_BASE_API

      // 调用删除地址接口
      const response = await fetch(`${apiBaseUrl}/user/address/delete/${addressId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: localStorage.getItem('token') || '',
        },
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('删除地址失败，服务器响应:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText.substring(0, 200) + '...',
        })
        throw new Error(`删除地址失败: ${response.status} ${response.statusText}`)
      }

      // 尝试解析响应JSON
      const responseData = await response.json()
      console.log('地址删除成功:', responseData)

      // 更新本地地址列表
      addresses.value.splice(index, 1)

      // 显示成功消息
      showToastMessage('地址删除成功!')
    } catch (error) {
      console.error('删除地址失败:', error)
      showToastMessage('删除地址失败，请稍后重试', 'error')
    }
  }
}
// 设置为默认地址
const setAsDefault = async (index: number) => {
  Object.assign(currentAddress, addresses.value[index])
  if(addresses.value.find(item=>item.default===1)?.id!==addresses.value[index].id && addresses.value.some(item=>item.default===1)){
    ElMessage.warning('已有默认地址，请勿重复设置')
    return
  }
  try {
    if(addresses.value[index].default!==1){
      const addressId = addresses.value[index].id
      if (addressId) {
        // 调用API设置默认地址
        await addressApi.setDefaultAddress(addressId)
        // 重新获取地址列表以更新UI
        await fetchAddressList()
        localStorage.setItem('user', JSON.stringify({ ...userInfo, defaultAddress: addresses.value[index] }))
      } else {
        // 如果没有ID，只在前端修改
        // 先将所有地址设为非默认
        addresses.value.forEach((addr) => {
          addr.isDefault = false
        })
        // 将当前地址设为默认
        addresses.value[index].isDefault = true
      }
    }else {
      const addressToSave = {
        ...currentAddress,
        default:0
      }

    console.log('准备保存的地址数据:', addressToSave)
      // 使用API基础URL
      const apiBaseUrl = import.meta.env.VITE_BASE_API
      // 更新现有地址 - 使用PUT方法
      const response = await fetch(`${apiBaseUrl}/user/address/update/${currentAddress.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: localStorage.getItem('token') || '',
        },
        body: JSON.stringify(addressToSave),
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('更新地址失败，服务器响应:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText.substring(0, 200) + '...',
        })
        throw new Error(`更新地址失败: ${response.status} ${response.statusText}`)
      }

      // 尝试解析响应JSON
      const responseData = await response.json()
      console.log('地址更新成功:', responseData)
      fetchAddressList()
    
  } 
}catch (error) {
    console.error('设置默认地址失败:', error)
  }
}

// 设置地址标签
const setAddressLabel = (type: string, label: string) => {
  currentAddress.labelType = type
  currentAddress.label = label
}

// 省市区数据
const provinces = ref<string[]>([])
const cities = ref<string[]>([])
const districts = ref<string[]>([])

// 省份变更处理
const onProvinceChange = () => {
  currentAddress.city = ''
  // 这里应该根据选择的省份加载对应的城市列表
  // 示例数据，实际应该从接口或本地数据获取
  cities.value = ['New York', 'Los Angeles', 'Chicago', 'Houston'] as string[]
}

// 城市变更处理
const onCityChange = () => {
  // 这里应该根据选择的城市加载对应的区县列表
  // 示例数据，实际应该从接口或本地数据获取
  districts.value = ['Downtown', 'Uptown', 'Midtown', 'Suburbs'] as string[]
}

// 添加新方法，防止点击外部关闭
const preventClose = (event: Event) => {
  // 阻止事件冒泡，防止关闭模态框
  event.stopPropagation()
}
</script>

<style scoped>
.address-manager {
  max-width: 100%;
  margin: 0 auto;
  padding: 20px;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  color: #333;
  min-height: 100vh;
}

.address-manager.modal-open {
  overflow: hidden;
}

.page-header {
  margin-bottom: 30px;
  position: relative;
}

h1 {
  font-size: 32px;
  margin-bottom: 12px;
  font-weight: 700;
  color: #333;
  position: relative;
  display: inline-block;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

h1::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 50px;
  height: 5px;
  background: linear-gradient(90deg, #9f7aea, #6875f5);
  border-radius: 3px;
  box-shadow: 0 2px 5px rgba(159, 122, 234, 0.3);
}

.subtitle {
  color: #666;
  font-size: 15px;
  position: relative;
  z-index: 1;
  max-width: 70%;
  line-height: 1.5;
}

.address-container {
  position: relative;
  margin-bottom: 30px;
  background: linear-gradient(to right, rgba(236, 231, 254, 0.2), rgba(233, 233, 255, 0.2));
  padding: 25px;
  border-radius: 16px;
  box-shadow: inset 0 0 20px rgba(159, 122, 234, 0.1);
}

.address-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  position: relative;
  z-index: 1;
}

.address-item {
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  padding: 22px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #eee;
  display: flex;
  flex-direction: column;
}

.address-item:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transform: translateY(-6px);
}

.address-item.default-address {
  border: 2px solid #9f7aea;
  background-color: #faf8ff;
  box-shadow: 0 6px 25px rgba(159, 122, 234, 0.15);
}

.address-tag {
  position: absolute;
  top: 0;
  right: 0;
  background: linear-gradient(135deg, #9f7aea, #6875f5);
  color: white;
  font-size: 12px;
  padding: 4px 10px;
  border-bottom-left-radius: 8px;
}

.address-info {
  margin-bottom: 15px;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.name {
  font-weight: 600;
  font-size: 16px;
}

.phone {
  color: #666;
  font-size: 14px;
}

.address-labels {
  display: flex;
  gap: 8px;
}

.label {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  color: white;
}

.label.home {
  background-color: #4299e1;
}

.label.company {
  background-color: #48bb78;
}

.label.school {
  background-color: #ed8936;
}

.label.other {
  background-color: #a0aec0;
}

.address-detail {
  color: #4a5568;
  line-height: 1.5;
  font-size: 14px;
  word-break: break-all;
}

.address-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 15px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  background-color: #f3f4f6;
}

.action-btn:hover {
  transform: scale(1.1);
}

.edit-btn:hover {
  background-color: #ebf8ff;
  color: #3182ce;
}

.delete-btn:hover {
  background-color: #fff5f5;
  color: #e53e3e;
}

.default-btn:hover {
  background-color: #f0fff4;
  color: #38a169;
}

.icon {
  font-style: normal;
  font-size: 14px;
}

.empty-address {
  text-align: center;
  padding: 60px 0;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.empty-icon {
  font-size: 60px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.empty-address p {
  font-size: 16px;
  color: #4a5568;
  margin-bottom: 10px;
}

.empty-subtitle {
  font-size: 14px;
  color: #a0aec0;
}

.add-address-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.add-address-btn {
  background: linear-gradient(135deg, #9f7aea, #6875f5);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 30px;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(159, 122, 234, 0.3);
}

.add-address-btn:hover {
  box-shadow: 0 6px 16px rgba(159, 122, 234, 0.5);
  transform: translateY(-2px);
}

.add-icon {
  font-size: 18px;
  font-weight: bold;
}

/* 模态框样式 */
.address-form-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
}

.modal-content {
  background-color: white;
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #edf2f7;
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 10;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}

.modal-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #a0aec0;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.close-btn:hover {
  background-color: #f7fafc;
  color: #4a5568;
}

form {
  padding: 20px 30px 30px;
}

.form-notice {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #f0f4ff;
  border-left: 4px solid #6875f5;
  padding: 12px 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.notice-icon {
  font-size: 18px;
}

.notice-text {
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
}

.form-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  margin-bottom: 25px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
}

.input-wrapper {
  position: relative;
}

input,
textarea,
select {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 16px;
  color: #2d3748;
  background-color: white;
  transition: all 0.2s;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: #9f7aea;
  box-shadow: 0 0 0 3px rgba(159, 122, 234, 0.2);
}

input.filled,
textarea.filled {
  border-color: #a0aec0;
}

.input-border {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #9f7aea, #6875f5);
  transition: width 0.3s;
}

input:focus ~ .input-border,
textarea:focus ~ .input-border {
  width: 100%;
}

textarea {
  height: 100px;
  resize: vertical;
}

.label-selector {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.label-btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #e2e8f0;
  background-color: white;
  flex: 1;
  text-align: center;
}

.label-btn.active {
  color: white;
  font-weight: 500;
}

.label-btn.home.active {
  background-color: #4299e1;
  border-color: #4299e1;
}

.label-btn.company.active {
  background-color: #48bb78;
  border-color: #48bb78;
}

.label-btn.school.active {
  background-color: #ed8936;
  border-color: #ed8936;
}

.label-btn.other.active {
  background-color: #a0aec0;
  border-color: #a0aec0;
}

.address-select .select-group {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
}

.custom-select {
  position: relative;
}

.select-arrow {
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  width: 10px;
  height: 10px;
  border-style: solid;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 3px;
  transform: translateY(-50%) rotate(45deg);
  pointer-events: none;
  color: #a0aec0;
}

/* 自定义复选框开关 */
.toggle-switch {
  display: flex;
  align-items: center;
}

.toggle-switch input[type='checkbox'] {
  display: none;
}

.toggle-switch label {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 28px;
  background-color: #e2e8f0;
  border-radius: 14px;
  cursor: pointer;
  transition: all 0.3s;
  margin: 0;
  margin-right: 12px;
}

.toggle-switch label::after {
  content: '';
  position: absolute;
  top: 4px;
  left: 4px;
  width: 20px;
  height: 20px;
  background-color: white;
  border-radius: 50%;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch input[type='checkbox']:checked + label {
  background: linear-gradient(135deg, #9f7aea, #6875f5);
}

.toggle-switch input[type='checkbox']:checked + label::after {
  transform: translateX(24px);
}

.toggle-label {
  font-size: 14px;
  color: #4a5568;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
}

.cancel-btn,
.save-btn {
  padding: 10px 24px;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background-color: white;
  border: 1px solid #e2e8f0;
  color: #4a5568;
}

.cancel-btn:hover {
  background-color: #f7fafc;
}

.save-btn {
  background: linear-gradient(135deg, #9f7aea, #6875f5);
  border: none;
  color: white;
  box-shadow: 0 4px 12px rgba(159, 122, 234, 0.3);
}

.save-btn:hover {
  box-shadow: 0 6px 16px rgba(159, 122, 234, 0.5);
  transform: translateY(-2px);
}

/* 过渡动画 */
.modal-enter-active,
.modal-leave-active {
  transition:
    opacity 0.3s,
    transform 0.3s;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

.list-move {
  transition: transform 0.5s;
}

/* 导航栏样式 */
.app-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 30px;
  background-color: white;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  margin-bottom: 25px;
  position: relative;
  overflow: hidden;
  border-bottom: 3px solid transparent;
  border-image: linear-gradient(to right, #9f7aea, #6875f5);
  border-image-slice: 1;
}

.app-navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(159, 122, 234, 0.08), rgba(104, 117, 245, 0.04));
  z-index: 0;
}

.navbar-left,
.navbar-right {
  display: flex;
  align-items: center;
}

.brand {
  display: flex;
  align-items: center;
  gap: 10px;
}

.brand-icon {
  font-size: 24px;
}

.brand-text {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #9f7aea, #6875f5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.5px;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 15px;
  background-color: #f7f9fc;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s;
}

.user-dropdown:hover {
  background-color: #edf2f7;
}

.user-avatar {
  font-size: 18px;
}

.username {
  font-size: 14px;
  color: #4a5568;
}

/* 内容区域布局 */
.content-container {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

/* 侧边栏样式 */
.sidebar {
  width: 240px;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  padding: 25px 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100vh - 180px);
  position: sticky;
  top: 20px;
  background-image: linear-gradient(to bottom, #f9f7ff 0%, white 100%);
  border-left: 5px solid #9f7aea;
}

.sidebar-menu {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 16px 25px;
  cursor: pointer;
  transition: all 0.3s;
  margin: 5px 0;
  border-radius: 0 30px 30px 0;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(to bottom, #9f7aea, #6875f5);
  opacity: 0;
  transition: opacity 0.3s;
}

.menu-item:hover {
  background-color: #f5f3ff;
}

.menu-item:hover::before {
  opacity: 1;
}

.menu-item.active {
  background: linear-gradient(to right, #f5f3ff 0%, #ebe6ff 100%);
  color: #6875f5;
  font-weight: 600;
}

.menu-item.active::before {
  opacity: 1;
}

.menu-icon {
  font-size: 18px;
  width: 24px;
  text-align: center;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid #edf2f7;
}

.help-center {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #6875f5;
  cursor: pointer;
}

.help-icon {
  font-size: 16px;
}

/* 主内容区样式 */
.main-content {
  flex: 1;
}

.header-content {
  flex: 1;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: linear-gradient(135deg, rgba(159, 122, 234, 0.12), rgba(104, 117, 245, 0.08));
  padding: 25px;
  border-radius: 16px;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%239C92AC' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
  z-index: 0;
}

.quick-actions {
  display: flex;
  gap: 10px;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #9f7aea, #6875f5);
  color: white;
  border: none;
  border-radius: 30px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 6px 15px rgba(159, 122, 234, 0.4);
  transition: all 0.3s;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.quick-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s;
}

.quick-action-btn:hover {
  box-shadow: 0 8px 20px rgba(159, 122, 234, 0.5);
  transform: translateY(-3px);
}

.quick-action-btn:hover::before {
  opacity: 1;
}

.add-icon {
  font-size: 18px;
  font-weight: bold;
}

/* 功能引导卡片 */
.feature-highlight {
  margin-bottom: 30px;
}

.feature-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.feature-card {
  background-color: white;
  border-radius: 16px;
  padding: 25px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  border-top: 5px solid transparent;
  position: relative;
  overflow: hidden;
}

.feature-card:nth-child(1) {
  border-top-color: #9f7aea;
}

.feature-card:nth-child(2) {
  border-top-color: #6875f5;
}

.feature-card:nth-child(3) {
  border-top-color: #805ad5;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(159, 122, 234, 0.1), rgba(104, 117, 245, 0.05));
  opacity: 0;
  transition: opacity 0.3s;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 15px;
  position: relative;
  z-index: 1;
}

.feature-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 10px;
  position: relative;
  z-index: 1;
}

.feature-desc {
  font-size: 14px;
  color: #718096;
  line-height: 1.5;
  position: relative;
  z-index: 1;
}

/* 提示条 */
.tips-container {
  background-color: #ebf8ff;
  border-left: 4px solid #4299e1;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 25px;
}

.tips-icon {
  font-size: 20px;
  color: #4299e1;
}

.tips-content {
  flex: 1;
}

.tips-title {
  font-weight: 600;
  color: #2c5282;
  margin-bottom: 5px;
}

.tips-text {
  font-size: 14px;
  color: #2a4365;
}

.tips-close {
  background: none;
  border: none;
  font-size: 18px;
  color: #4a5568;
  cursor: pointer;
  padding: 5px;
}

/* 部分标题 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  gap: 15px;
  position: relative;
}

.section-title span {
  font-size: 22px;
  font-weight: 700;
  color: #2d3748;
  white-space: nowrap;
  background: linear-gradient(135deg, #9f7aea, #6875f5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  padding-right: 10px;
  position: relative;
}

.section-title span::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: linear-gradient(135deg, #9f7aea, #6875f5);
  right: -4px;
  top: 50%;
  transform: translateY(-50%);
}

.section-line {
  flex: 1;
  height: 2px;
  background: linear-gradient(90deg, rgba(159, 122, 234, 0.5), rgba(159, 122, 234, 0.05));
  position: relative;
}

.section-line::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 1px;
  bottom: -3px;
  left: 0;
  background: linear-gradient(90deg, rgba(159, 122, 234, 0.3), rgba(159, 122, 234, 0.01));
}

/* 底部区域 */
.footer {
  margin-top: 50px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
  text-align: center;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 10px;
}

.footer-link {
  color: #718096;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s;
}

.footer-link:hover {
  color: #4a5568;
}

.footer-copyright {
  font-size: 12px;
  color: #a0aec0;
}

.map-pin-icon {
  margin-right: 8px;
  font-size: 24px;
  vertical-align: middle;
}

/* 响应式设计 */
@media (max-width: 1100px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 900px) {
  .content-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    margin-bottom: 20px;
    position: static;
  }

  .sidebar-menu {
    flex-direction: row;
    justify-content: center;
    padding: 10px;
  }

  .menu-item {
    border-radius: 30px;
    white-space: nowrap;
    margin: 0;
    padding: 12px 20px;
  }

  .menu-item.active {
    background: linear-gradient(90deg, #9f7aea, #6875f5);
    color: white;
  }

  .menu-item::before {
    display: none;
  }

  .sidebar-footer {
    display: none;
  }
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .address-select .select-group {
    grid-template-columns: 1fr;
  }

  .label-selector {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }

  .address-list {
    grid-template-columns: 1fr;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .quick-actions {
    align-self: stretch;
  }

  .quick-action-btn {
    flex: 1;
    justify-content: center;
  }
}

/* 可以添加自定义样式覆盖 Element Plus 默认样式 */
:deep(.custom-el-message) {
  min-width: 320px;
  padding: 15px 20px;
}

:deep(.custom-el-message .el-message__content) {
  font-size: 16px;
  font-weight: 600;
}

/* 新增的样式 */
.form-row {
  display: flex;
  gap: 20px;
}

.zipcode-group {
  flex: 1;
}

.zipcode-wrapper {
  display: flex;
  align-items: center;
}

.lookup-btn {
  background: linear-gradient(135deg, #9f7aea, #6875f5);
  color: white;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 10px;
  transition: all 0.2s;
  box-shadow: 0 2px 5px rgba(159, 122, 234, 0.3);
}

.lookup-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(159, 122, 234, 0.4);
}

.lookup-icon {
  font-size: 16px;
}

.lookup-status {
  font-size: 12px;
  margin-top: 5px;
  color: #6875f5;
}

.validate-address-btn {
  background: linear-gradient(135deg, #38a169, #48bb78);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 5px rgba(56, 161, 105, 0.3);
  width: 100%;
  margin-top: 5px;
}

.validate-address-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(56, 161, 105, 0.4);
}

.validate-icon {
  font-size: 16px;
}

.validation-status {
  font-size: 14px;
  margin-top: 10px;
  padding: 8px 12px;
  border-radius: 6px;
  text-align: center;
}

.validation-status.valid {
  background-color: #f0fff4;
  color: #38a169;
  border-left: 3px solid #38a169;
}

.validation-status.invalid {
  background-color: #fff5f5;
  color: #e53e3e;
  border-left: 3px solid #e53e3e;
}

.save-btn:disabled {
  background: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

input[readonly] {
  background-color: #f7fafc;
  cursor: default;
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 20px;
  }
}

.street-wrapper {
  display: flex;
  align-items: center;
}

.find-zip-btn {
  background: linear-gradient(135deg, #6875f5, #4c51bf);
}

.find-zip-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(104, 117, 245, 0.4);
}

.find-zip-btn:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.form-help-text {
  font-size: 12px;
  color: #718096;
  margin-top: 4px;
  font-style: italic;
}
</style>
