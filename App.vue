<script setup>
import { onMounted, provide } from 'vue'

// 全局初始化语言
onMounted(() => {
  // 判断是否已经加载过translate.js
  if (!document.getElementById('translate-js')) {
    const script = document.createElement('script')
    script.id = 'translate-js'
    script.src = 'https://cdn.staticfile.net/translate.js/3.12.0/translate.js'
    script.onload = () => {
      // 读取保存的语言设置
      const savedLanguage = localStorage.getItem('selectedLanguage') || 'chinese_simplified'

      // 初始化translate.js
      window.translate.selectLanguageTag.show = false
      window.translate.service.use('client.edge')

      // 应用保存的语言设置
      window.translate.changeLanguage(savedLanguage)
      window.translate.execute()

      // 提供一个全局方法来切换语言
      provide('changeLanguage', (lang) => {
        localStorage.setItem('selectedLanguage', lang)
        if (window.translate) {
          window.translate.changeLanguage(lang)
        }
      })
    }
    document.head.appendChild(script)
  }
})
</script>
