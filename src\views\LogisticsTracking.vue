<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-white to-purple-50 p-6">
    <div class="max-w-5xl mx-auto">
      <!-- 顶部搜索栏 -->
      <div
        class="bg-white backdrop-blur-lg bg-opacity-90 rounded-2xl shadow-lg p-6 mb-6 transform hover:scale-[1.02] transition-all duration-300"
      >
        <div class="flex flex-col md:flex-row gap-4">
          <div class="flex-1 relative">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="输入运单号查询"
              class="w-full pl-12 pr-4 py-3 bg-gray-50/50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-300"
            />
            <Search
              class="w-5 h-5 text-gray-400 absolute left-4 top-1/2 transform -translate-y-1/2"
            />
            <span class="absolute right-4 top-1/2 transform -translate-y-1/2 text-sm text-gray-400">
              当前单号: {{ trackingNumber }}
            </span>
          </div>
          <button
            @click="searchTracking"
            class="px-6 py-3 bg-purple-600 text-white rounded-xl hover:bg-purple-700 active:scale-95 transition-all duration-200 flex items-center justify-center gap-2 min-w-[120px] shadow-lg shadow-purple-200"
          >
            <Search class="w-5 h-5" />
            查询
          </button>
        </div>
      </div>

      <!-- 快捷操作栏 -->
      <div class="flex gap-4 mb-6 overflow-x-auto pb-2 scrollbar-hide">
        <button
          v-for="action in quickActions"
          :key="action.label"
          @click="action.onClick"
          class="flex items-center gap-2 px-4 py-2 bg-white rounded-full shadow-md hover:shadow-lg transition-all duration-200 whitespace-nowrap text-gray-600 hover:text-purple-600"
        >
          <component :is="action.icon" class="w-4 h-4" />
          {{ action.label }}
        </button>
      </div>

      <!-- 物流概览 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <!-- 包裹状态卡片 -->
        <div
          class="bg-white rounded-2xl shadow-lg p-6 relative overflow-hidden group hover:shadow-xl transition-all duration-300"
        >
          <div
            class="absolute right-0 top-0 w-32 h-32 bg-purple-100 rounded-full -mr-16 -mt-16 transition-transform group-hover:scale-110"
          ></div>
          <div class="relative z-10">
            <h3 class="text-lg font-semibold mb-2">包裹状态</h3>
            <div class="flex items-center gap-2 mb-4">
              <span
                :class="[
                  'px-3 py-1 rounded-full text-sm',
                  logisticsInfo?.logisticsStatus === LogisticsStatus.SHIPPING
                    ? 'bg-yellow-100 text-yellow-600 animate-pulse'
                    : 'bg-green-100 text-green-600',
                ]"
              >
                {{
                  logisticsInfo?.logisticsStatus === LogisticsStatus.SHIPPING ? '运输中' : '已到达'
                }}
              </span>
              <span class="text-gray-500 text-sm"> 预计今天送达 </span>
            </div>
            <div class="space-y-2">
              <div class="flex justify-between items-center">
                <span class="text-gray-500">配送进度</span>
                <span class="text-purple-600 font-medium">65%</span>
              </div>
              <div class="w-full bg-gray-100 rounded-full h-2 overflow-hidden">
                <div
                  class="bg-gradient-to-r from-purple-500 to-purple-600 h-2 rounded-full transition-all duration-1000 ease-out"
                  :style="{ width: '65%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 配送时效卡片 -->
        <div class="bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">配送时效</h3>
            <div class="relative group">
              <Clock class="w-6 h-6 text-purple-600" />
              <div
                class="absolute top-full right-0 mt-2 bg-white rounded-lg shadow-lg p-3 hidden group-hover:block w-48 text-sm"
              >
                <p class="text-gray-600">预计送达时间可能会受天气、交通等因素影响</p>
              </div>
            </div>
          </div>
          <div class="space-y-4">
            <div
              class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              <div class="flex items-center gap-2">
                <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                  <Clock class="w-4 h-4 text-purple-600" />
                </div>
                <span class="text-gray-600">已用时间</span>
              </div>
              <span class="font-medium">16小时</span>
            </div>
            <div
              class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              <div class="flex items-center gap-2">
                <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                  <Timer class="w-4 h-4 text-purple-600" />
                </div>
                <span class="text-gray-600">预计时效</span>
              </div>
              <span class="font-medium">48小时</span>
            </div>
          </div>
        </div>

        <!-- 配送信息卡片 -->
        <div class="bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">配送信息</h3>
            <button @click="toggleNotification" class="relative group">
              <Bell class="w-6 h-6 text-purple-600" />
              <span
                v-if="hasNewNotification"
                class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"
              ></span>
            </button>
          </div>
          <div class="space-y-4">
            <div class="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center gap-2">
                  <Truck class="w-5 h-5 text-purple-600" />
                  <span class="text-gray-600">承运商</span>
                </div>
                <span class="font-medium">{{ logisticsInfo?.logisticsCompany }}</span>
              </div>
              <div class="text-xs text-gray-500">全程温控 | 安全保障 | 准时达</div>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div
                class="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
              >
                <div class="flex items-center gap-2 mb-1">
                  <Package class="w-4 h-4 text-purple-600" />
                  <span class="text-gray-600 text-sm">包裹重量</span>
                </div>
                <span class="font-medium">{{ packageInfo.weight }}kg</span>
              </div>
              <div
                class="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
              >
                <div class="flex items-center gap-2 mb-1">
                  <Box class="w-4 h-4 text-purple-600" />
                  <span class="text-gray-600 text-sm">包裹数量</span>
                </div>
                <span class="font-medium">1件</span>
              </div>
            </div>
            <div class="text-sm text-gray-500">发货时间：{{ logisticsInfo?.shippingDate }}</div>
          </div>
        </div>
      </div>

      <!-- 收发货信息 -->
      <div
        class="bg-white rounded-2xl shadow-lg p-6 mb-6 hover:shadow-xl transition-all duration-300"
      >
        <div class="flex flex-col md:flex-row gap-6">
          <div class="flex-1">
            <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
              <SendHorizontal class="w-5 h-5 text-purple-600" />
              发货信息
            </h3>
            <div
              class="space-y-3 p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-all duration-200"
            >
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center">
                  <Building2 class="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <p class="font-medium text-gray-900">{{ shipment.from.name }}</p>
                  <p class="text-sm text-gray-500">官方认证商家</p>
                </div>
              </div>
              <div class="pl-13">
                <p class="text-gray-600 flex items-center gap-2 mb-2">
                  <Phone class="w-4 h-4" />
                  {{ shipment.from.phone }}
                </p>
                <p class="text-gray-600 flex items-center gap-2">
                  <MapPin class="w-4 h-4" />
                  {{ shipment.from.address }}
                </p>
              </div>
            </div>
          </div>
          <div class="hidden md:flex items-center justify-center">
            <div class="w-20 h-20 relative">
              <div class="absolute inset-0 flex items-center justify-center">
                <div class="w-full border-t-2 border-dashed border-purple-200"></div>
              </div>
              <div class="absolute inset-0 flex items-center justify-center">
                <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center">
                  <Truck class="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </div>
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
              <Home class="w-5 h-5 text-purple-600" />
              收货信息
            </h3>
            <div
              class="space-y-3 p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-all duration-200"
            >
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                  <User class="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p class="font-medium text-gray-900">{{ shipment.to.name }}</p>
                  <p class="text-sm text-gray-500">收货人</p>
                </div>
              </div>
              <div class="pl-13">
                <p class="text-gray-600 flex items-center gap-2 mb-2">
                  <Phone class="w-4 h-4" />
                  {{ shipment.to.phone }}
                </p>
                <p class="text-gray-600 flex items-center gap-2">
                  <MapPin class="w-4 h-4" />
                  {{ shipment.to.address }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 物流轨迹 -->
      <div class="bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold">物流轨迹</h3>
          <div class="flex items-center gap-4">
            <button
              @click="toggleMap"
              class="flex items-center gap-2 text-gray-600 hover:text-purple-600 transition-colors duration-200"
            >
              <Map class="w-5 h-5" />
              {{ showMap ? '隐藏地图' : '查看地图' }}
            </button>
            <button
              @click="refreshTracking"
              class="flex items-center gap-2 text-purple-600 hover:text-purple-700 transition-colors duration-200"
            >
              <RefreshCw class="w-5 h-5" :class="{ 'animate-spin': isRefreshing }" />
              刷新
            </button>
          </div>
        </div>

        <div v-if="showMap" class="mb-6 rounded-xl overflow-hidden h-[200px] relative">
          <!-- 这里可以集成实际的地图组件 -->
          <div class="absolute inset-0 bg-gray-100 flex items-center justify-center">
            <Map class="w-12 h-12 text-gray-400" />
          </div>
        </div>

        <div class="space-y-6">
          <TransitionGroup name="list" tag="div" class="relative">
            <div v-for="(event, index) in trackingEvents" :key="event.time" class="relative pl-8">
              <div
                class="absolute left-0 top-2 w-4 h-4 rounded-full border-2 transition-colors duration-300"
                :class="[
                  index === 0 ? 'border-purple-600 bg-purple-100' : 'border-gray-300 bg-gray-50',
                ]"
              ></div>
              <div
                v-if="index !== trackingEvents.length - 1"
                class="absolute left-2 top-6 w-0.5 h-full -ml-px bg-gray-200"
              ></div>
              <div class="group hover:bg-gray-50 p-2 rounded-lg transition-colors duration-200">
                <div class="mb-1 flex items-center gap-2">
                  <span class="text-sm text-gray-500">{{ event.time }}</span>
                  <span
                    v-if="index === 0"
                    class="px-2 py-0.5 bg-purple-100 text-purple-600 rounded-full text-xs"
                  >
                    最新
                  </span>
                </div>
                <div
                  class="font-medium mb-1"
                  :class="index === 0 ? 'text-purple-600' : 'text-gray-900'"
                >
                  {{ event.status }}
                </div>
                <div class="flex items-center gap-2 text-gray-500">
                  <MapPin class="w-4 h-4" />
                  {{ event.location }}
                </div>
              </div>
            </div>
          </TransitionGroup>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 md:hidden">
        <div class="flex justify-around max-w-md mx-auto">
          <button
            v-for="action in mobileActions"
            :key="action.label"
            @click="action.onClick"
            class="flex flex-col items-center gap-1 text-gray-500 hover:text-purple-600 transition-colors duration-200"
          >
            <component :is="action.icon" class="w-6 h-6" />
            <span class="text-xs">{{ action.label }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 通知弹窗 -->
    <Transition name="notification">
      <div
        v-if="showNotification"
        class="fixed top-4 right-4 bg-white rounded-lg shadow-lg p-4 max-w-sm"
      >
        <div class="flex items-start gap-3">
          <div
            class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0"
          >
            <Bell class="w-4 h-4 text-purple-600" />
          </div>
          <div class="flex-1">
            <h4 class="font-medium text-gray-900 mb-1">物流更新提醒</h4>
            <p class="text-sm text-gray-500">您的包裹已到达【北京朝阳集散中心】</p>
          </div>
          <button @click="closeNotification" class="text-gray-400 hover:text-gray-600">
            <X class="w-5 h-5" />
          </button>
        </div>
      </div>
    </Transition>

    <!-- 添加状态更新按钮（仅当有权限时显示） -->
    <div v-if="canUpdateStatus" class="mt-4">
      <el-button
        type="primary"
        @click="updateLogisticsStatus(logisticsInfo.id, LogisticsStatus.ARRIVED)"
        :disabled="logisticsInfo?.logisticsStatus === LogisticsStatus.ARRIVED"
      >
        标记为已到达
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { logisticsApi, LogisticsStatus } from '@/api/logistics'
import type { LogisticsInfoDTO } from '@/api/logistics'
import { ElMessage } from 'element-plus'
import {
  Search,
  Package,
  Clock,
  Timer,
  Truck,
  Box,
  SendHorizontal,
  Home,
  Phone,
  MapPin,
  RefreshCw,
  Bell,
  Share2,
  MessageSquare,
  Building2,
  User,
  Map,
  X,
  History,
  FileText,
  HelpCircle,
} from 'lucide-vue-next'

const route = useRoute()
const searchQuery = ref('')
const trackingNumber = ref('SF1234567890123')
const showMap = ref(false)
const isRefreshing = ref(false)
const showNotification = ref(false)
const hasNewNotification = ref(true)
const logisticsInfo = ref<LogisticsInfoDTO>()

// 获取物流信息
const getLogisticsInfo = async (id: number) => {
  try {
    const response = await logisticsApi.getLogisticsInfo(id)
    if (response.code === 1) {
      logisticsInfo.value = response.data
      trackingNumber.value = response.data.logisticsNumber

      // 更新页面显示的其他信息
      packageInfo.value = {
        ...packageInfo.value,
        carrier: response.data.logisticsCompany,
        shippedAt: response.data.shippingDate,
      }
    } else {
      ElMessage.error(response?.msg || '获取物流信息失败')
    }
  } catch (error) {
    console.error('Get logistics info error:', error)
    ElMessage.error('获取物流信息失败')
  }
}

// 更新物流状态
const updateLogisticsStatus = async (id: number, status: LogisticsStatus) => {
  try {
    const response = await logisticsApi.updateLogisticsStatus(id, status)
    if (response.code === 1) {
      ElMessage.success('更新物流状态成功')
      await getLogisticsInfo(id) // 更新后重新获取物流信息
    } else {
      ElMessage.error(response?.msg || '更新物流状态失败')
    }
  } catch (error) {
    console.error('Update logistics status error:', error)
    ElMessage.error('更新物流状态失败')
  }
}

// 创建物流信息
const createLogistics = async (data: LogisticsInfoDTO) => {
  try {
    const response = await logisticsApi.createLogistics(data)
    if (response.code === 1) {
      ElMessage.success('创建物流信息成功')
      return true
    } else {
      ElMessage.error(response?.msg || '创建物流信息失败')
      return false
    }
  } catch (error) {
    console.error('Create logistics error:', error)
    ElMessage.error('创建物流信息失败')
    return false
  }
}

// 刷新物��信息
const refreshTracking = async () => {
  isRefreshing.value = true
  if (logisticsInfo.value?.id) {
    await getLogisticsInfo(logisticsInfo.value.id)
  }
  isRefreshing.value = false
}

onMounted(() => {
  const logisticsId = Number(route.params.id)
  if (logisticsId) {
    getLogisticsInfo(logisticsId)
  }
})

// 快捷操作按钮
const quickActions = [
  {
    label: '历史记录',
    icon: History,
    onClick: () => console.log('查看历史记录'),
  },
  {
    label: '电子面单',
    icon: FileText,
    onClick: () => console.log('查看电子面单'),
  },
  {
    label: '在线客服',
    icon: MessageSquare,
    onClick: () => console.log('联系客服'),
  },
  {
    label: '常见问题',
    icon: HelpCircle,
    onClick: () => console.log('查看常见问题'),
  },
]

const packageInfo = ref({
  weight: '2.5',
  estimatedDelivery: '2024-12-18 18:00',
  carrier: '顺丰速运',
  shippedAt: '2024-12-16 10:30',
})

const shipment = ref({
  from: {
    name: '深圳总仓',
    phone: '0755-88888888',
    address: '广东省深圳市南山区科技园科技中一路123号',
  },
  to: {
    name: '张三',
    phone: '13800138000',
    address: '北京市朝阳区建国路88号',
  },
})

const trackingEvents = ref([
  {
    time: '2024-12-16 16:30',
    status: '当前快递在【北京朝阳集散中心】已装车,准备发往【北京朝阳区建国路营业部】',
    location: '北京市朝阳区',
  },
  {
    time: '2024-12-16 14:20',
    status: '快件到达【北京朝阳集散中心】',
    location: '北京市朝阳区',
  },
  {
    time: '2024-12-16 10:30',
    status: '快件已发车',
    location: '深圳市南山区',
  },
  {
    time: '2024-12-16 09:50',
    status: '【深圳总仓】已收件',
    location: '深圳市南山区',
  },
])

const mobileActions = [
  {
    label: '分享',
    icon: Share2,
    onClick: () => console.log('分享物流信息'),
  },
  {
    label: '客服',
    icon: MessageSquare,
    onClick: () => console.log('联系客服'),
  },
  {
    label: '通知',
    icon: Bell,
    onClick: () => toggleNotification(),
  },
]

const searchTracking = () => {
  console.log('查询物流信息:', searchQuery.value)
}

const toggleMap = () => {
  showMap.value = !showMap.value
}

const toggleNotification = () => {
  showNotification.value = !showNotification.value
  hasNewNotification.value = false
}

const closeNotification = () => {
  showNotification.value = false
}

// 添加物流状态更新的权限检查
const canUpdateStatus = computed(() => {
  // 这里添加权限检查逻辑
  return true // 根据实际需���返回布尔值
})

// 验证物流信息
const validateLogisticsInfo = (data: LogisticsInfoDTO): boolean => {
  if (!data.logisticsNumber) {
    ElMessage.error('物流单号不能为空')
    return false
  }
  if (!data.orderId) {
    ElMessage.error('订单ID不能为空')
    return false
  }
  if (!data.logisticsCompany) {
    ElMessage.error('物流公司不能为空')
    return false
  }
  if (!data.logisticsStatus) {
    ElMessage.error('物流状态不能为空')
    return false
  }
  return true
}

// 在创建物流信息前进行验证
const handleCreateLogistics = async (data: LogisticsInfoDTO) => {
  if (!validateLogisticsInfo(data)) {
    return false
  }
  return await createLogistics(data)
}
</script>

<style scoped>
.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}
.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}
.notification-enter-from,
.notification-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* 渐变背景动画 */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
</style>
