export interface ApiResponse<T> {
  code: number
  msg: string
  data: T
}

export interface PageResult<T> {
  records: T[]
  total: number
  size: number
  current: number
}

export interface BaseEntity {
  id: number
  createTime?: string
  updateTime?: string
}

export enum ApiErrorCode {
  SUCCESS = 1,
  FAIL = 0,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  SERVER_ERROR = 500,
}

// 产品相关类型定义
export interface ProductPageQueryDTO {
  page: number
  pageSize: number
  productName?: string
  categoryId?: number
  productStatus?: number
}

export interface ProductVO {
  id: number
  productNumber: string
  storeId: number
  productName: string
  price: number
  productIntroduction: string
  productImage: string
  productVideo: string
  productStatus: string
  type: string
  categoryId: number
}

export interface PageResult<T> {
  total: number
  records: T[]
}

// 商家注册响应
export interface MerchantRegisterResponse extends ApiResponse<null> {
  code: number
  msg: string
  data: null
}

// 发送验证码响应
export interface SendCodeResponse extends ApiResponse<null> {
  code: number
  msg: string
  data: null
}

// 买家登录响应
export interface BuyerLoginResponse
  extends ApiResponse<{
    token: string
    id: number
  }> {
  code: number
  msg: string
  data: {
    token: string
    id: number
  }
}

// 修改 MerchantDTO 为 MerchantRegisterDTO
export interface MerchantRegisterDTO {
  shopName: string // 店铺名称
  mobile: string // 联系手机号
  contactName: string // 联系人姓名
  province: string // 所在省份
  city: string // 所在城市
  detailAddress: string // 详细地址
}

// 修改密码 DTO
export interface PasswordChangeDTO {
  phone: string
  oldPassword: string
  newPassword: string
}

// 修改密码响应类型
export interface PasswordChangeResponse {
  code: number
  msg: string
  data: null
}
