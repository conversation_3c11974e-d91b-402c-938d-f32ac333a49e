<template>
  <div class="order-page">
    <div class="background-circle bg-circle-1"></div>
    <div class="background-circle bg-circle-2"></div>
    <div class="background-circle bg-circle-3"></div>

    <!-- 订单列表页面 -->
    <div class="container">
      <div class="header">
        <h1>订单管理中心</h1>
        <p>查看和管理您的所有订单信息</p>
      </div>

      <div class="card order-tabs">
        <div
          v-for="(tab, index) in tabs"
          :key="index"
          :class="['tab-item', { active: activeTab === index }]"
          @click="activeTab = index"
        >
          {{ tab.name }}
          <span class="count">{{ tab.count }}</span>
        </div>
      </div>

      <div class="card search-section">
        <div class="search-input">
          <input type="text" v-model="searchQuery" placeholder="搜索订单号/商品名称" />
          <button class="search-btn">
            <i class="icon-search"></i>
          </button>
        </div>
        <div class="filter-options">
          <div class="date-filter">
            <span>日期：</span>
            <select v-model="dateFilter">
              <option value="all">全部时间</option>
              <option value="today">今天</option>
              <option value="week">本周</option>
              <option value="month">本月</option>
            </select>
          </div>
          <div class="status-filter">
            <span>状态：</span>
            <select v-model="statusFilter">
              <option value="all">全部状态</option>
              <option value="1">待付款</option>
              <option value="2">处理中</option>
              <option value="3">已发货</option>
              <option value="4">已完成</option>
              <option value="5">已取消</option>
            </select>
          </div>
        </div>
      </div>

      <div class="card order-list">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">
            <div class="spinner-ring">
              <div></div>
              <div></div>
              <div></div>
              <div></div>
            </div>
            <div class="loading-text">
              <div class="loading-title">正在获取订单数据</div>
              <div class="loading-subtitle">请稍候...</div>
            </div>
          </div>
          <!-- 骨架屏效果 -->
          <div class="skeleton-list">
            <div v-for="i in 3" :key="i" class="skeleton-item">
              <div class="skeleton-header">
                <div class="skeleton-line skeleton-order-id"></div>
                <div class="skeleton-line skeleton-date"></div>
                <div class="skeleton-line skeleton-status"></div>
              </div>
              <div class="skeleton-content">
                <div class="skeleton-product">
                  <div class="skeleton-image"></div>
                  <div class="skeleton-info">
                    <div class="skeleton-line skeleton-name"></div>
                    <div class="skeleton-line skeleton-spec"></div>
                    <div class="skeleton-line skeleton-price"></div>
                  </div>
                </div>
              </div>
              <div class="skeleton-footer">
                <div class="skeleton-line skeleton-total"></div>
                <div class="skeleton-actions">
                  <div class="skeleton-btn"></div>
                  <div class="skeleton-btn"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else-if="!loading && filteredOrders.length === 0" class="empty-state">
          <div class="empty-icon">📦</div>
          <p>暂无订单数据</p>
        </div>

        <div
          v-for="order in filteredOrders"
          :key="order.id"
          class="order-item"
          :class="{ 'highlight-order': isHighlightedOrder(order) }"
        >
          <div class="order-header">
            <div class="order-id">订单号：{{ order.number }}</div>
            <div class="order-date">{{ order.orderTime }}</div>
            <div :class="['order-status', `status-${order.status}`]">
              {{ getStatusText(order.status) }}
            </div>
          </div>

          <div class="order-products">
            <div v-for="(product, idx) in order.orderDetails" :key="idx" class="product-item">
              <div class="product-image">
                <img :src="product.productImage" :alt="product.productName" />
              </div>
              <div class="product-info">
                <div class="product-name">{{ product.productName }}</div>
                <div class="product-specs">{{ product.productSpec || '默认规格' }}</div>
                <div class="product-price">¥{{ product.unitPrice.toFixed(2) }}</div>
              </div>
              <div class="product-quantity">x{{ product.quantity }}</div>
            </div>
          </div>

          <div class="order-footer">
            <div class="order-total">
              共{{ getTotalItems(order) }}件商品，合计：
              <span class="total-price">¥{{ getTotalPrice(order).toFixed(2) }}</span>
            </div>
            <div class="order-actions">
              <button
                v-if="order.status === OrderStatus.PENDING_PAYMENT"
                class="action-btn pay-btn"
                @click="payOrder(order.id)"
              >
                立即付款
              </button>
              <button
                v-if="order.status === OrderStatus.SHIPPED"
                class="action-btn track-btn"
                @click="trackOrder(order.id)"
              >
                查看物流
              </button>
              <button
                v-if="order.status === OrderStatus.SHIPPED"
                class="action-btn confirm-btn"
                @click="confirmOrder(order.id)"
              >
                确认收货
              </button>
              <button
                v-if="order.status === OrderStatus.COMPLETED"
                class="action-btn review-btn"
                @click="reviewOrder(order.id)"
              >
                评价订单
              </button>
              <button
                v-if="canRefund(order)"
                class="action-btn refund-btn"
                @click="applyRefund(order)"
              >
                申请退款
              </button>
              <span v-else-if="getOrderRefundStatus(order.id)" class="refund-status-text">
                {{ getRefundStatusText(order.id) }}
              </span>
              <button
                v-if="order.status === OrderStatus.PENDING_PAYMENT"
                class="action-btn cancel-btn"
                @click="cancelOrder(order.id)"
              >
                取消订单
              </button>
              <button class="action-btn detail-btn" @click="viewOrderDetail(order.id)">
                订单详情
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="pagination">
        <button class="page-btn prev" :disabled="currentPage === 1" @click="currentPage--">
          上一页
        </button>
        <div class="page-numbers">
          <!-- 显示页码，最多显示5个页码 -->
          <template v-if="totalPages <= 5">
            <span
              v-for="page in totalPages"
              :key="page"
              :class="['page-number', { active: currentPage === page }]"
              @click="currentPage = page"
            >
              {{ page }}
            </span>
          </template>
          <template v-else>
            <!-- 当前页靠前时 -->
            <template v-if="currentPage <= 3">
              <span
                v-for="page in 5"
                :key="page"
                :class="['page-number', { active: currentPage === page }]"
                @click="currentPage = page"
              >
                {{ page }}
              </span>
            </template>
            <!-- 当前页靠后时 -->
            <template v-else-if="currentPage >= totalPages - 2">
              <span
                v-for="page in 5"
                :key="totalPages - 5 + page"
                :class="['page-number', { active: currentPage === totalPages - 5 + page }]"
                @click="currentPage = totalPages - 5 + page"
              >
                {{ totalPages - 5 + page }}
              </span>
            </template>
            <!-- 当前页在中间时 -->
            <template v-else>
              <span
                v-for="offset in 5"
                :key="currentPage - 3 + offset"
                :class="['page-number', { active: offset === 3 }]"
                @click="currentPage = currentPage - 3 + offset"
              >
                {{ currentPage - 3 + offset }}
              </span>
            </template>
          </template>
        </div>
        <button class="page-btn next" :disabled="currentPage === totalPages" @click="currentPage++">
          下一页
        </button>
        <span class="pagination-info">共 {{ total }} 条，{{ totalPages }} 页</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'
import { useRouter, useRoute } from 'vue-router'
import {
  orderApi,
  OrderStatus,
  orderDetailApi,
  refundApi,
  type RefundApplicationDTO,
  type RefundCheckResult,
  RefundType,
  RefundMethod,
} from '@/api/order'

const router = useRouter()
const route = useRoute()

// 判断是否为详情页面
const isDetailView = computed(() => {
  return false // 不再需要判断是否为详情页面，因为详情页面已经独立出来
})

defineOptions({
  name: 'OrderList',
})

// 定义订单相关的接口
interface OrderDetail {
  id: number
  orderId: number
  productId: number
  productName: string
  productImage: string
  productSpec?: string
  unitPrice: number
  quantity: number
  discount: number
  subtotal: number
  createTime: string
  updateTime: string
}

interface AddressVO {
  id: number
  userId: number
  consignee: string
  phone: string
  sex: string
  provinceCode: string
  provinceName: string
  cityCode: string
  cityName: string
  districtCode: string
  districtName: string
  detail: string
  label: string
  isDefault: number
}

interface LogisticsVO {
  id: number
  logisticsNumber: string
  orderId: number
  logisticsCompany: string
  logisticsStatus: string
  logisticsStatusDesc: string
  shippingDate: string
  createTime: string
}

interface OrderVO {
  id: number
  number: string
  status: number
  statusDesc: string
  amount: number
  orderTime: string
  payTime: string
  payMethod: number
  payMethodDesc: string
  address: AddressVO
  orderRemark: string
  orderDetails: OrderDetail[]
  logistics?: LogisticsVO
}

interface RefundStatus {
  applicationStatus: number
  // 可以根据需要添加其他属性
}

// 状态映射 - 确保与后端一致
const ORDER_STATUS = {
  [OrderStatus.PENDING_PAYMENT]: 'pending', // 待付款
  [OrderStatus.PAID]: 'processing', // 已付款
  [OrderStatus.PROCESSING]: 'processing', // 处理中
  [OrderStatus.SHIPPED]: 'shipped', // 已发货
  [OrderStatus.COMPLETED]: 'completed', // 已完成
  [OrderStatus.CANCELLED]: 'cancelled', // 已取消
  [OrderStatus.REFUNDED]: 'refunded', // 已退款
} as const

// 标签页数据 - 与订单状态对应
const tabs = [
  { name: '全部订单', count: 0 },
  { name: '待付款', count: 0, status: OrderStatus.PENDING_PAYMENT },
  { name: '待发货', count: 0, status: OrderStatus.PAID },
  { name: '待收货', count: 0, status: OrderStatus.SHIPPED },
  { name: '已完成', count: 0, status: OrderStatus.COMPLETED },
  { name: '已退款', count: 0, status: OrderStatus.REFUNDED },
]

const activeTab = ref(0)
const searchQuery = ref('')
const dateFilter = ref('all')
const statusFilter = ref('all')
const currentPage = ref(1)
const pageSize = ref(10) // 每页显示数量
const total = ref(0) // 总记录数
const loading = ref(false)
const orders = ref<OrderVO[]>([])
const orderRefundStatus = ref<Map<number, RefundStatus>>(new Map()) // 存储订单退款状态

// 获取当前用户订单列表（简单模式，不分页）
const fetchOrders = async () => {
  loading.value = true
  try {
    // 使用orderApi.getUserOrders获取当前用户的订单
    const status = activeTab.value > 0 ? activeTab.value : undefined
    const response = await orderApi.getUserOrders(status)

    if (response.data.code === 1) {
      // 确保返回的数据是数组
      orders.value = Array.isArray(response.data.data) ? response.data.data : [response.data.data]

      // 更新标签页计数
      updateTabCounts()

      // 检查每个订单的退款状态
      for (const order of orders.value) {
        await checkOrderRefundStatus(order.id)
      }
    } else {
      ElMessage.error(response.data.msg || '获取订单列表失败')
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 分页查询订单列表
const fetchOrdersByPage = async () => {
  loading.value = true
  try {
    // 获取当前状态过滤条件
    const status = statusFilter.value !== 'all' ? parseInt(statusFilter.value) : undefined

    // 使用getUserOrders接口获取当前用户的订单
    const response = await orderApi.getUserOrders(status)

    if (response.data.code === 1) {
      // 处理返回的数据
      const ordersList = response.data.data || []

      // 应用日期过滤
      let filteredOrders = ordersList
      if (dateFilter.value !== 'all') {
        const now = new Date()
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

        filteredOrders = ordersList.filter((order) => {
          const orderDate = new Date(order.orderTime)
          if (dateFilter.value === 'today') {
            return orderDate >= today
          } else if (dateFilter.value === 'week') {
            const weekStart = new Date(today)
            weekStart.setDate(today.getDate() - today.getDay())
            return orderDate >= weekStart
          } else if (dateFilter.value === 'month') {
            const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
            return orderDate >= monthStart
          }
          return true
        })
      }

      // 应用搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filteredOrders = filteredOrders.filter((order) => {
          return (
            (order.number || '').toLowerCase().includes(query) ||
            (Array.isArray(order.orderDetails) &&
              order.orderDetails.some((detail) =>
                (detail?.productName || '').toLowerCase().includes(query),
              ))
          )
        })
      }

      // 计算总数据量
      total.value = filteredOrders.length

      // 手动分页
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      orders.value = filteredOrders.slice(start, end)

      // 更新标签页计数
      updateTabCounts(ordersList)
    } else {
      ElMessage.error(response.data.msg || '获取订单列表失败')
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 更新标签页计数
const updateTabCounts = (ordersList: OrderVO[] = []) => {
  if (!Array.isArray(ordersList) || ordersList.length === 0) {
    console.error('订单数据不是数组格式或为空')
    return
  }

  tabs[0].count = ordersList.length
  tabs[1].count = ordersList.filter((order) => order?.status === OrderStatus.PENDING_PAYMENT).length
  tabs[2].count = ordersList.filter((order) => order?.status === OrderStatus.PAID).length
  tabs[3].count = ordersList.filter((order) => order?.status === OrderStatus.SHIPPED).length
  tabs[4].count = ordersList.filter((order) => order?.status === OrderStatus.COMPLETED).length
  tabs[5].count = ordersList.filter((order) => order?.status === OrderStatus.REFUNDED).length
}

// 根据状态获取文本
const getStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    [OrderStatus.PENDING_PAYMENT]: '待付款',
    [OrderStatus.PAID]: '已付款',
    [OrderStatus.PROCESSING]: '处理中',
    [OrderStatus.SHIPPED]: '已发货',
    [OrderStatus.COMPLETED]: '已完成',
    [OrderStatus.CANCELLED]: '已取消',
    [OrderStatus.REFUNDED]: '已退款',
  }
  return statusMap[status] || '未知状态'
}

// 计算订单总价
const getTotalPrice = (order: OrderVO): number => {
  if (!order) return 0
  return order.amount || 0
}

// 计算订单商品总数
const getTotalItems = (order: OrderVO): number => {
  if (!order || !Array.isArray(order.orderDetails)) return 0
  return order.orderDetails.reduce((total, detail) => total + (detail?.quantity || 0), 0)
}

// 过滤订单
const filteredOrders = computed(() => {
  if (!Array.isArray(orders.value)) return []

  let result = orders.value

  // 当使用后端分页和筛选时，不需要前端再次过滤
  // 只有在本地搜索时才需要过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter((order) => {
      if (!order) return false
      return (
        (order.number || '').toLowerCase().includes(query) ||
        (Array.isArray(order.orderDetails) &&
          order.orderDetails.some((detail) =>
            (detail?.productName || '').toLowerCase().includes(query),
          ))
      )
    })
  }

  return result
})

// 计算总页数 - 使用后端返回的总记录数计算
const totalPages = computed(() => {
  return Math.ceil(total.value / pageSize.value)
})

// 分页后的订单 - 不再需要前端分页，直接使用后端分页结果
const paginatedOrders = computed(() => {
  return orders.value
})

// 处理图片加载错误
const handleImageError = (e: Event) => {
  const target = e.target as HTMLImageElement
  target.src = '/placeholder.svg'
}

// 获取URL参数中的高亮订单ID
const highlightOrderId = ref<string | null>(null)

// 判断是否为高亮显示的订单
const isHighlightedOrder = (order: OrderVO): boolean => {
  if (!highlightOrderId.value) {
    // 检查localStorage中是否有最近创建的订单ID
    const tempOrderData = localStorage.getItem('tempOrderData')
    if (tempOrderData) {
      try {
        const orderData = JSON.parse(tempOrderData)
        if (orderData.orderId) {
          return (
            order.id.toString() === orderData.orderId.toString() ||
            order.number === orderData.orderId.toString()
          )
        }
      } catch (e) {
        console.error('解析临时订单数据失败:', e)
      }
    }
    return false
  }
  return order.id.toString() === highlightOrderId.value || order.number === highlightOrderId.value
}

// 监听分页和筛选条件变化
watch([currentPage, statusFilter, dateFilter], () => {
  fetchOrdersByPage()
})

// 监听标签页变化
watch(activeTab, (newTab) => {
  // 切换标签页时，重置分页和状态过滤
  currentPage.value = 1

  // 根据标签页设置状态过滤
  if (newTab === 0) {
    statusFilter.value = 'all'
  } else if (newTab > 0 && newTab < tabs.length && tabs[newTab].status !== undefined) {
    statusFilter.value = tabs[newTab].status.toString()
  }

  // 重新加载数据
  fetchOrdersByPage()
})

// 监听搜索关键词变化
watch(
  searchQuery,
  debounce(() => {
    currentPage.value = 1 // 重置页码
    fetchOrdersByPage()
  }, 500),
)

// 防抖函数
function debounce<T extends (...args: unknown[]) => void>(
  fn: T,
  delay: number,
): (...args: Parameters<T>) => void {
  let timer: number | null = null
  return function (this: unknown, ...args: Parameters<T>) {
    if (timer) clearTimeout(timer)
    timer = window.setTimeout(() => {
      fn.apply(this, args)
      timer = null
    }, delay)
  }
}

onMounted(() => {
  // 获取URL中的高亮订单ID
  highlightOrderId.value = (route.query.highlight as string) || null

  // 使用分页查询替代简单查询
  fetchOrdersByPage()

  // 如果有高亮订单ID或者从临时订单详情页跳转过来，滚动到对应订单
  nextTick(() => {
    setTimeout(() => {
      const highlightedOrder = document.querySelector('.highlight-order')
      if (highlightedOrder) {
        highlightedOrder.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
    }, 500)
  })
})

// 组件卸载时清除临时订单数据
onUnmounted(() => {
  // 清除临时订单数据
  localStorage.removeItem('tempOrderData')
})

// 订单操作方法
const payOrder = (orderId: number): void => {
  // 跳转到支付页面
  router.push({
    path: '/payment',
    query: { orderId: orderId.toString() },
  })
}

const trackOrder = (orderId: number): void => {
  // 跳转到物流跟踪页面
  router.push(`/logistics/tracking/${orderId}`)
}

const reviewOrder = (orderId: number): void => {
  // 跳转到评价页面
  router.push(`/review/order/${orderId}`)
}

// 取消订单
const cancelOrder = async (orderId: number): Promise<void> => {
  try {
    // 弹出对话框让用户输入取消原因
    const { value: cancelReason } = await ElMessageBox.prompt('请输入取消订单的原因', '取消订单', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      inputPlaceholder: '例如：商品不符合预期',
    })

    // 调用API取消订单
    const response = await orderApi.cancelOrder(orderId, cancelReason)

    if (response.data.code === 1) {
      ElMessage.success('订单已取消')
      // 刷新订单列表
      fetchOrdersByPage()
    } else {
      ElMessage.error(response.data.msg || '取消订单失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订单失败:', error)
      ElMessage.error('取消订单失败，请稍后重试')
    }
  }
}

// 确认收货
const confirmOrder = async (orderId: number): Promise<void> => {
  try {
    // 弹出确认对话框
    await ElMessageBox.confirm('确认已收到商品吗？', '确认收货', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 调用API确认收货
    const response = await orderApi.confirmOrder(orderId)
    if (response.data.code === 1) {
      ElMessage.success('订单已确认收货')
      fetchOrdersByPage() // 刷新订单列表
    } else {
      ElMessage.error(response.data.msg || '确认收货失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认收货失败:', error)
      ElMessage.error('确认收货失败，请稍后重试')
    }
  }
}

const viewOrderDetail = (orderId: number): void => {
  // 使用正确的路由路径查看订单详情
  router.push(`/orders/${orderId}`)
}

// 检查订单退款状态 - 暂时禁用，因为后端没有提供此接口
const checkOrderRefundStatus = async (orderId: number) => {
  try {
    // TODO: 实现检查订单退款状态的逻辑
    // 可以通过查询退款列表来判断订单是否有退款申请
    console.log('检查订单退款状态:', orderId)
  } catch (error) {
    console.error('检查订单退款状态失败:', error)
  }
}

// 获取订单退款状态
const getOrderRefundStatus = (orderId: number) => {
  return orderRefundStatus.value.get(orderId)
}

// 判断订单是否可以退款
const canRefund = (order: OrderVO): boolean => {
  const refundStatus = getOrderRefundStatus(order.id)
  // 如果已有退款申请，则不能再申请
  if (refundStatus) {
    return false
  }
  // 已付款、待发货、已发货、已完成的订单都可以申请退款
  return (
    order.status === OrderStatus.PAID ||
    order.status === OrderStatus.PROCESSING ||
    order.status === OrderStatus.SHIPPED ||
    order.status === OrderStatus.COMPLETED
  )
}

// 获取退款状态文本
const getRefundStatusText = (orderId: number): string => {
  const refundStatus = getOrderRefundStatus(orderId)
  if (!refundStatus) return ''

  switch (refundStatus.applicationStatus) {
    case 1:
      return '退款待处理'
    case 2:
      return '退款已同意'
    case 3:
      return '退款已拒绝'
    case 4:
      return '退款已取消'
    case 5:
      return '退款中'
    case 6:
      return '退款成功'
    case 7:
      return '退款失败'
    default:
      return '退款处理中'
  }
}

// 显示优化的退款申请对话框
const showRefundDialog = async (
  order: OrderVO,
  checkResult: RefundCheckResult,
  refundForm: {
    refundReason: string
    refundAmount: number
    refundType: number
    refundMethod: number
  },
): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 使用Element Plus的MessageBox来简化对话框
    ElMessageBox.prompt('请输入退款原因', '申请退款', {
      confirmButtonText: '确认申请',
      cancelButtonText: '取消',
      inputPlaceholder: '请详细说明退款原因...',
      inputValidator: (value) => {
        if (!value || value.trim().length < 5) {
          return '退款原因至少需要5个字符'
        }
        return true
      },
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          // 设置表单数据
          refundForm.refundAmount = Math.min(order.amount, checkResult.maxRefundAmount)
          refundForm.refundType = 1 // 默认仅退款
          refundForm.refundMethod = 1 // 默认原路退回
          refundForm.refundReason = instance.inputValue.trim()
          resolve()
        } else {
          reject('cancel')
        }
        done()
      },
    }).catch(() => {
      reject('cancel')
    })
  })
}

// 申请退款
const applyRefund = async (order: OrderVO): Promise<void> => {
  try {
    // 首先检查订单是否可以退款
    const checkResponse = await refundApi.checkRefundEligibility(order.id)
    if (checkResponse.data.code !== 1) {
      ElMessage.error(checkResponse.data.msg || '该订单不符合退款条件')
      return
    }

    const checkResult = checkResponse.data.data

    // 创建退款申请表单
    const refundForm = {
      refundReason: '',
      refundAmount: order.amount,
      refundType: 1, // 默认仅退款
      refundMethod: 1, // 默认原路退回
    }

    // 显示优化后的退款申请对话框
    await showRefundDialog(order, checkResult, refundForm)

    // 构建退款请求数据
    const refundRequest: RefundApplicationDTO = {
      orderId: order.id,
      refundAmount: refundForm.refundAmount,
      refundReason: refundForm.refundReason,
      refundType: refundForm.refundType,
      refundMethod: refundForm.refundMethod,
      remark: `Order: ${order.number}, Time: ${new Date().toLocaleString()}`,
    }

    console.log('退款请求数据:', refundRequest)

    // 调用退款API
    const response = await refundApi.applyRefund(refundRequest)

    if (response.data.code === 1) {
      ElMessage.success('退款申请已提交，请等待处理')
      // 刷新订单列表
      fetchOrdersByPage()
    } else {
      ElMessage.error(response.data.msg || '退款申请失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('申请退款失败:', error)
      ElMessage.error('申请退款失败，请稍后重试')
    }
  }
}
</script>

<style scoped>
.order-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
  background: transparent;
}

.background-circle {
  display: none;
}

.bg-circle-1,
.bg-circle-2,
.bg-circle-3 {
  display: none;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #8a4af3;
  font-size: 28px;
  margin-bottom: 8px;
}

.header p {
  color: #666;
  font-size: 16px;
}

.card {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.order-tabs {
  display: flex;
  padding: 0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 15px 0;
  cursor: pointer;
  color: #666;
  position: relative;
  transition: all 0.3s;
}

.tab-item:hover {
  color: #8a4af3;
}

.tab-item.active {
  color: #8a4af3;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background-color: #8a4af3;
  border-radius: 3px;
}

.count {
  display: inline-block;
  background-color: #f0eaff;
  color: #8a4af3;
  border-radius: 10px;
  padding: 2px 8px;
  font-size: 12px;
  margin-left: 5px;
}

.search-section {
  padding: 15px;
}

.search-input {
  display: flex;
  margin-bottom: 15px;
}

.search-input input {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 6px 0 0 6px;
  padding: 10px 15px;
  font-size: 14px;
  outline: none;
}

.search-input input:focus {
  border-color: #8a4af3;
}

.search-btn {
  background-color: #8a4af3;
  color: white;
  border: none;
  border-radius: 0 6px 6px 0;
  padding: 0 20px;
  cursor: pointer;
}

.icon-search::before {
  content: '🔍';
}

.filter-options {
  display: flex;
  gap: 20px;
}

.date-filter,
.status-filter {
  display: flex;
  align-items: center;
}

.date-filter span,
.status-filter span {
  color: #666;
  margin-right: 8px;
}

.date-filter select,
.status-filter select {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 8px 10px;
  font-size: 14px;
  outline: none;
  background-color: white;
}

.order-list {
  padding: 0;
}

.empty-state {
  padding: 60px 0;
  text-align: center;
  color: #999;
}

.empty-icon {
  font-size: 40px;
  margin-bottom: 10px;
}

.order-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px;
}

.order-item:last-child {
  border-bottom: none;
}

.order-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 14px;
}

.order-id {
  font-weight: 600;
}

.order-date {
  color: #999;
}

.order-status {
  font-weight: 600;
}

.status-pending {
  color: #ff9800;
}

.status-processing {
  color: #2196f3;
}

.status-shipped {
  color: #8a4af3;
}

.status-completed {
  color: #4caf50;
}

.status-cancelled {
  color: #f44336;
}

.status-refunded {
  color: #9c27b0;
}

.order-products {
  margin-bottom: 15px;
}

.product-item {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px dashed #f0f0f0;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 80px;
  height: 80px;
  margin-right: 15px;
  border-radius: 6px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  flex: 1;
}

.product-name {
  font-weight: 600;
  margin-bottom: 5px;
}

.product-specs {
  color: #999;
  font-size: 13px;
  margin-bottom: 5px;
}

.product-price {
  color: #ff6b6b;
}

.product-quantity {
  color: #999;
  margin-left: 15px;
  align-self: center;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.order-total {
  color: #666;
}

.total-price {
  color: #ff6b6b;
  font-weight: 600;
  font-size: 18px;
}

.order-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 8px 15px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  border: none;
  transition: all 0.3s;
}

.pay-btn {
  background-color: #8a4af3;
  color: white;
}

.pay-btn:hover {
  background-color: #7a3ae3;
}

.track-btn {
  background-color: #f0eaff;
  color: #8a4af3;
}

.track-btn:hover {
  background-color: #e5daff;
}

.review-btn {
  background-color: #fff8e6;
  color: #ff9800;
}

.review-btn:hover {
  background-color: #fff0d1;
}

.refund-btn {
  background-color: #fff3e0;
  color: #f57c00;
}

.refund-btn:hover {
  background-color: #ffe0b2;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.cancel-btn:hover {
  background-color: #e8e8e8;
}

.refund-status-text {
  padding: 8px 15px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  background-color: #fff3e0;
  color: #f57c00;
  border: 1px solid #ffcc80;
}

.detail-btn {
  background-color: white;
  color: #666;
  border: 1px solid #e0e0e0;
}

.detail-btn:hover {
  background-color: #f9f9f9;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
}

.page-btn {
  padding: 8px 15px;
  border: 1px solid #e0e0e0;
  background-color: white;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.prev {
  border-radius: 6px 0 0 6px;
}

.next {
  border-radius: 0 6px 6px 0;
}

.page-numbers {
  display: flex;
}

.page-number {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e0e0;
  border-left: none;
  cursor: pointer;
  transition: all 0.3s;
}

.page-number.active {
  background-color: #8a4af3;
  color: white;
  border-color: #8a4af3;
}

.pagination-info {
  margin-left: 15px;
  color: #666;
  font-size: 14px;
}

/* 订单详情样式 */
.back-btn {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f0eaff;
  color: #8a4af3;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.back-btn:hover {
  background-color: #e5daff;
}

.icon-back::before {
  content: '←';
  margin-right: 6px;
}

.order-detail {
  padding: 20px;
}

.detail-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.info-item {
  display: flex;
}

.info-label {
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.info-value {
  color: #333;
  font-weight: 500;
}

.address-info {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
}

.recipient {
  font-weight: 600;
  margin-right: 15px;
}

.phone {
  color: #666;
}

.address {
  margin-top: 8px;
  color: #333;
}

.product-detail-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.product-detail-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  margin-right: 15px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  flex: 1;
}

.product-name {
  font-weight: 600;
  margin-bottom: 5px;
}

.product-spec {
  color: #999;
  font-size: 13px;
}

.product-price,
.product-quantity,
.product-subtotal {
  margin-left: 15px;
  text-align: right;
  min-width: 80px;
}

.product-subtotal {
  color: #ff6b6b;
  font-weight: 600;
}

.order-amount {
  margin-top: 20px;
  text-align: right;
}

.amount-item {
  margin-bottom: 8px;
  color: #666;
}

.amount-item.total {
  font-size: 18px;
  font-weight: 600;
  color: #ff6b6b;
}

.logistics-info {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
}

.detail-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.confirm-btn {
  background-color: #4caf50;
  color: white;
}

.confirm-btn:hover {
  background-color: #43a047;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(138, 74, 243, 0.2);
  border-top-color: #8a4af3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 高亮订单样式 */
.highlight-order {
  animation: highlight-pulse 2s ease-in-out;
  box-shadow:
    0 0 0 2px #8a4af3,
    0 4px 20px rgba(138, 74, 243, 0.15);
  position: relative;
  z-index: 1;
}

@keyframes highlight-pulse {
  0% {
    box-shadow:
      0 0 0 2px #8a4af3,
      0 4px 20px rgba(138, 74, 243, 0.15);
  }
  50% {
    box-shadow:
      0 0 0 4px #8a4af3,
      0 4px 25px rgba(138, 74, 243, 0.3);
  }
  100% {
    box-shadow:
      0 0 0 2px #8a4af3,
      0 4px 20px rgba(138, 74, 243, 0.15);
  }
}

@media (max-width: 768px) {
  .filter-options {
    flex-direction: column;
    gap: 10px;
  }

  .order-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .order-actions {
    flex-wrap: wrap;
  }

  .order-header {
    flex-direction: column;
    gap: 5px;
  }
}

/* 加载效果样式 */
.loading-container {
  padding: 40px 20px;
  text-align: center;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  border-radius: 12px;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.loading-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(138, 74, 243, 0.1), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
}

.spinner-ring {
  display: inline-block;
  position: relative;
  width: 60px;
  height: 60px;
}

.spinner-ring div {
  box-sizing: border-box;
  display: block;
  position: absolute;
  width: 48px;
  height: 48px;
  margin: 6px;
  border: 4px solid #8a4af3;
  border-radius: 50%;
  animation: spinner-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  border-color: #8a4af3 transparent transparent transparent;
}

.spinner-ring div:nth-child(1) {
  animation-delay: -0.45s;
}

.spinner-ring div:nth-child(2) {
  animation-delay: -0.3s;
}

.spinner-ring div:nth-child(3) {
  animation-delay: -0.15s;
}

@keyframes spinner-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #8a4af3;
}

.loading-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
  animation: pulse 2s ease-in-out infinite;
}

.loading-subtitle {
  font-size: 14px;
  color: #666;
  animation: pulse 2s ease-in-out infinite 0.5s;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* 骨架屏样式 */
.skeleton-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.skeleton-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.skeleton-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.skeleton-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-order-id {
  width: 120px;
  height: 16px;
}

.skeleton-date {
  width: 100px;
  height: 16px;
}

.skeleton-status {
  width: 60px;
  height: 16px;
}

.skeleton-content {
  margin-bottom: 15px;
}

.skeleton-product {
  display: flex;
  gap: 15px;
}

.skeleton-image {
  width: 80px;
  height: 80px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6px;
}

.skeleton-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-name {
  width: 200px;
  height: 16px;
}

.skeleton-spec {
  width: 120px;
  height: 14px;
}

.skeleton-price {
  width: 80px;
  height: 16px;
}

.skeleton-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.skeleton-total {
  width: 150px;
  height: 18px;
}

.skeleton-actions {
  display: flex;
  gap: 10px;
}

.skeleton-btn {
  width: 80px;
  height: 32px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .loading-container {
    padding: 30px 15px;
  }

  .skeleton-header {
    flex-direction: column;
    gap: 8px;
  }

  .skeleton-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .skeleton-product {
    flex-direction: column;
  }

  .skeleton-image {
    width: 100%;
    height: 120px;
  }
}
</style>
