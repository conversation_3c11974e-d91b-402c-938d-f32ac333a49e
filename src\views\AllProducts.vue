<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-8">
    <!-- 装饰性背景元素 -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none z-0">
      <div
        class="absolute top-20 right-10 w-64 h-64 bg-blue-200 rounded-full opacity-20 blur-3xl"
      ></div>
      <div
        class="absolute bottom-20 left-10 w-80 h-80 bg-purple-200 rounded-full opacity-20 blur-3xl"
      ></div>
    </div>

    <!-- 头部导航 -->
    <header class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8 relative z-10">
      <div class="flex items-center justify-between">
        <button
          class="flex items-center gap-2 px-4 py-2 text-purple-600 hover:text-purple-700 bg-white/80 backdrop-blur-sm rounded-lg shadow-sm transition-all duration-200 hover:shadow-md group"
          @click="goToHome"
        >
          <span class="text-lg transition-transform group-hover:-translate-x-1">←</span>
          <span class="font-medium">返回首页</span>
        </button>
        <div class="text-center">
          <h1
            class="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2"
          >
            全部商品
          </h1>
          <div class="text-sm text-gray-500 tracking-wider uppercase">浏览我们的全部商品系列</div>
        </div>
        <div class="w-[100px]"></div>
        <!-- 用于平衡布局的占位div -->
      </div>
    </header>

    <!-- 主内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <!-- 搜索和排序 -->
      <div
        class="bg-white/80 backdrop-blur-sm rounded-xl shadow-md p-4 mb-8 flex flex-wrap gap-4 items-center justify-between"
      >
        <div class="relative flex-grow max-w-md">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="搜索商品..."
            @focus="isSearchFocused = true"
            @blur="
              setTimeout(() => {
                isSearchFocused = false
              }, 200)
            "
            class="w-full pl-10 pr-12 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
            :class="{ 'ring-2 ring-purple-500 border-transparent': isSearchFocused }"
          />
          <div class="absolute left-3 top-2.5 text-gray-400">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>

          <!-- 清除按钮 -->
          <button
            v-if="searchQuery"
            @click="clearSearch"
            class="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clip-rule="evenodd"
              />
            </svg>
          </button>

          <!-- 搜索结果数量提示 -->
          <div
            v-if="searchQuery && filteredProducts.length > 0"
            class="absolute -bottom-8 left-0 text-sm text-gray-500"
          >
            找到 {{ filteredProducts.length }} 个相关商品
          </div>

          <!-- 搜索建议 -->
          <div
            v-if="isSearchFocused && searchSuggestions.length > 0"
            class="absolute top-full left-0 right-0 mt-1 bg-white rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto"
          >
            <div
              v-for="suggestion in searchSuggestions"
              :key="suggestion"
              @click="selectSuggestion(suggestion)"
              class="px-4 py-2 hover:bg-purple-50 cursor-pointer text-gray-700 flex items-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-2 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
              {{ suggestion }}
            </div>
          </div>
        </div>

        <div class="flex items-center gap-3">
          <span class="text-sm text-gray-500">排序方式:</span>
          <select
            v-model="sortOption"
            class="bg-white border border-gray-200 text-gray-700 py-2 px-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
          >
            <option value="default">默认排序</option>
            <option value="priceAsc">价格从低到高</option>
            <option value="priceDesc">价格从高到低</option>
            <option value="newest">最新上架</option>
          </select>
        </div>
      </div>

      <!-- 商品展示区域 -->
      <div
        class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-6 mb-8 relative overflow-hidden"
      >
        <!-- 背景装饰 -->
        <div class="absolute -top-24 -right-24 w-48 h-48 bg-blue-100 rounded-full opacity-50"></div>
        <div
          class="absolute -bottom-24 -left-24 w-48 h-48 bg-purple-100 rounded-full opacity-50"
        ></div>

        <!-- 商品网格 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          <div
            v-for="product in filteredAndSortedProducts"
            :key="product.id"
            class="group bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden flex flex-col transform hover:-translate-y-2"
          >
            <div class="relative aspect-[4/3] overflow-hidden">
              <img
                :src="product.pic"
                :alt="product.name"
                class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
              />
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              >
                <div class="absolute bottom-3 left-3 right-3 flex justify-between items-center">
                  <span class="text-white font-medium text-sm">查看详情</span>
                  <button
                    @click.stop="toggleFavorite(product.id)"
                    class="p-2 bg-white/90 backdrop-blur-sm rounded-full hover:bg-purple-600 hover:text-white transition-colors duration-200"
                  >
                    <i class="icon-heart" :class="{ 'text-red-500': product.isFavorite }">❤</i>
                  </button>
                </div>
              </div>
            </div>
            <div class="p-4 flex-grow flex flex-col">
              <h3 class="text-lg font-medium text-gray-800 mb-2 line-clamp-1">
                {{ product.name }}
              </h3>
              <p class="text-sm text-gray-500 mb-3 line-clamp-2 flex-grow">
                {{ stripHtml(product.detailHtml || '') }}
              </p>
              <div class="flex items-center justify-between">
                <span
                  class="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
                  >¥{{ product.price }}</span
                >
                <div class="flex gap-2">
                  <button
                    @click.stop="handleViewDetails(product)"
                    class="p-2 text-sm bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors"
                  >
                    详情
                  </button>
                  <button
                    @click.stop="handleAddToCart(product)"
                    class="p-2 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    购买
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div
          v-if="filteredAndSortedProducts.length === 0"
          class="flex flex-col items-center justify-center py-16 px-4"
        >
          <div class="bg-white p-8 rounded-2xl shadow-lg text-center max-w-md">
            <h3 class="text-xl font-medium text-gray-900 mb-2">暂无商品</h3>
            <p class="text-gray-500">暂时没有找到符合条件的商品，请尝试其他搜索条件</p>
          </div>
        </div>
      </div>

      <!-- 分页控制 -->
      <div class="flex justify-center mb-8">
        <div class="inline-flex bg-white rounded-lg shadow-sm p-1">
          <button
            v-for="page in totalPages"
            :key="page"
            :class="[
              'w-10 h-10 flex items-center justify-center rounded-md text-sm font-medium transition-all duration-200',
              currentPage === page
                ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md'
                : 'text-gray-700 hover:bg-purple-50',
            ]"
            @click="currentPage = page"
          >
            {{ page }}
          </button>
        </div>
      </div>
    </div>

    <!-- 底部 -->
    <footer
      class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 text-center text-gray-500 text-sm relative z-10"
    >
      <div class="mb-2">全部商品 · 品质保障 · 正品直供</div>
      <div>© 2025 商城. 保留所有权利</div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'
import type { ProductVO } from '@/types/product'
import { cartApi } from '@/api/cart'

const { t } = useI18n()
const router = useRouter()
const products = ref<ProductVO[]>([])
const searchQuery = ref('')
const sortOption = ref('default')
const currentPage = ref(1)
const pageSize = 12
const totalPages = computed(() => Math.ceil(filteredProducts.value.length / pageSize))
const isSearchFocused = ref(false)
const searchHistory = ref<string[]>([])
const searchSuggestions = computed(() => {
  if (!searchQuery.value) {
    // 当搜索框为空时，显示搜索历史
    return searchHistory.value.slice(0, 5)
  }

  // 当有搜索内容时，显示匹配的商品名称作为建议
  const query = searchQuery.value.toLowerCase()
  const suggestions = products.value
    .filter((product) => product.name.toLowerCase().includes(query))
    .map((product) => product.name)
    .slice(0, 5)

  return [...new Set(suggestions)] // 去重
})

// 获取所有商品
const getAllProducts = async () => {
  try {
    // 参考 SelectedProducts.vue 的接口实现
    const res = await request.get('/products/category/0')
    if (res.data.code === 1) {
      const allProducts = Array.isArray(res.data.data) ? res.data.data : [res.data.data]
      products.value = allProducts.map((item) => ({
        ...item,
        isFavorite: false,
      }))
    }
  } catch (error) {
    console.error('获取所有商品失败:', error)
    ElMessage.error('获取所有商品失败')
  }
}

// 根据搜索条件筛选商品
const filteredProducts = computed(() => {
  if (!searchQuery.value) return products.value

  const query = searchQuery.value.toLowerCase()
  return products.value.filter(
    (product) =>
      product.name.toLowerCase().includes(query) ||
      (product.detailHtml && stripHtml(product.detailHtml).toLowerCase().includes(query)),
  )
})

// 排序并分页
const filteredAndSortedProducts = computed(() => {
  const result = [...filteredProducts.value]

  // 排序
  switch (sortOption.value) {
    case 'priceAsc':
      result.sort((a, b) => a.price - b.price)
      break
    case 'priceDesc':
      result.sort((a, b) => b.price - a.price)
      break
    case 'newest':
      result.sort((a, b) => b.id - a.id)
      break
    default:
      // 默认排序，保持原顺序
      break
  }

  // 分页
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return result.slice(start, end)
})

// 解析HTML文本
const stripHtml = (html: string): string => {
  const tmp = document.createElement('div')
  tmp.innerHTML = html
  return tmp.textContent || tmp.innerText || ''
}

// 查看商品详情
const handleViewDetails = (product: ProductVO) => {
  router.push({
    path: `/product/${product.id}`,
    query: {
      name: product.name,
      image: product.pic,
      price: product.price.toString(),
      introduction: product.detailHtml,
    },
  })
}

// 添加到购物车
const handleAddToCart = async (product: ProductVO) => {
  const token = localStorage.getItem('token')
  if (!token) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }

  try {
    const userStr = localStorage.getItem('user')
    const user = userStr ? JSON.parse(userStr) : null
    const userId = user?.id
    if (!userId) {
      ElMessage.error('用户信息异常')
      return
    }

    const cartData = {
      buyerId: userId,
      productId: product.id.toString(),
      productName: product.name,
      number: 1,
      price: product.price,
      image: product.pic,
    }

    const res = await cartApi.addToCart(cartData)
    if (res.data.code === 1) {
      ElMessage.success('添加成功')
    } else {
      ElMessage.error(res.data.msg || '添加失败')
    }
  } catch (error) {
    console.error('添加购物车失败:', error)
    ElMessage.error('添加失败')
  }
}

// 返回首页方法
const goToHome = () => {
  router.push('/')
}

// 收藏商品
const toggleFavorite = async (productId) => {
  const token = localStorage.getItem('token')
  if (!token) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  const product = products.value.find((p) => p.id === productId)
  if (product) {
    try {
      const res = await request.post('/favorites/toggle', { productId })
      if (res.data.code === 1) {
        product.isFavorite = !product.isFavorite
        ElMessage.success(product.isFavorite ? '收藏成功' : '取消收藏成功')
      } else {
        ElMessage.error('操作失败')
      }
    } catch (error) {
      console.error('收藏操作失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  // 重置到第一页
  currentPage.value = 1
}

// 选择搜索建议
const selectSuggestion = (suggestion: string) => {
  searchQuery.value = suggestion

  // 添加到搜索历史
  if (!searchHistory.value.includes(suggestion)) {
    searchHistory.value.unshift(suggestion)
    // 限制历史记录数量
    if (searchHistory.value.length > 10) {
      searchHistory.value.pop()
    }
    // 保存到本地存储
    localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value))
  }

  // 重置到第一页
  currentPage.value = 1
}

// 监听搜索查询变化，重置页码
watch(searchQuery, () => {
  currentPage.value = 1
})

// 初始化时加载搜索历史
onMounted(() => {
  getAllProducts()

  // 从本地存储加载搜索历史
  const savedHistory = localStorage.getItem('searchHistory')
  if (savedHistory) {
    try {
      searchHistory.value = JSON.parse(savedHistory)
    } catch (e) {
      console.error('Failed to parse search history:', e)
    }
  }
})
</script>

<style scoped>
.icon-heart {
  color: #999;
  font-size: 16px;
}

.icon-heart.text-red-500 {
  color: #ef4444;
}
</style>
