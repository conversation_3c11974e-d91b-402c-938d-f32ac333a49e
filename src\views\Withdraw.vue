<template>
  <div class="coming-soon-container">
    <div class="content">
      <!-- 顶部装饰元素 -->
      <div class="decorative-elements">
        <div class="circle"></div>
        <div class="square"></div>
        <div class="triangle"></div>
      </div>

      <div class="icon-wrapper">
        <SparklesIcon class="sparkles-icon" />
      </div>

      <h1 class="title">提款记录功能正在开发中</h1>
      <p class="subtitle">更好的体验即将到来</p>

      <!-- 功能预告 -->
      <div class="features">
        <div class="feature-item">
          <ZapIcon class="feature-icon" />
          <div class="feature-text">
            <h3>全新体验</h3>
            <p>界面焕然一新</p>
          </div>
        </div>
        <div class="feature-item">
          <RocketIcon class="feature-icon" />
          <div class="feature-text">
            <h3>性能升级</h3>
            <p>快速响应</p>
          </div>
        </div>
        <div class="feature-item">
          <StarIcon class="feature-icon" />
          <div class="feature-text">
            <h3>更多惊喜</h3>
            <p>持续更新中</p>
          </div>
        </div>
      </div>

      <!-- 上线预告 -->
      <div class="launch-info">
        <div class="launch-card">
          <ClockIcon class="launch-icon" />
          <div class="launch-text">
            <h3>预计上线时间</h3>
            <p>{{ launchDays }}天内</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import {
  Sparkles as SparklesIcon,
  Zap as ZapIcon,
  Rocket as RocketIcon,
  Star as StarIcon,
  Clock as ClockIcon,
} from 'lucide-vue-next'

const launchDays = ref('14')
</script>

<style scoped>
.coming-soon-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
  padding: 2rem;
}

.content {
  position: relative;
  text-align: center;
  padding: 2.5rem;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(12px);
  border-radius: 2rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  max-width: 700px;
  width: 90%;
  overflow: hidden;
}

.decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.circle {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(249, 168, 212, 0.1));
  animation: float 6s ease-in-out infinite;
}

.square {
  position: absolute;
  bottom: -30px;
  left: -30px;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));
  transform: rotate(45deg);
  animation: float 8s ease-in-out infinite;
}

.triangle {
  position: absolute;
  top: 50%;
  right: -20px;
  width: 0;
  height: 0;
  border-left: 40px solid transparent;
  border-right: 40px solid transparent;
  border-bottom: 60px solid rgba(168, 85, 247, 0.1);
  animation: float 7s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

.icon-wrapper {
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.2), rgba(249, 168, 212, 0.2));
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  animation: pulse 2s infinite;
}

.sparkles-icon {
  color: rgb(168, 85, 247);
  width: 50px;
  height: 50px;
}

.title {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  color: #6b7280;
  font-size: 1.25rem;
  margin-bottom: 2.5rem;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(168, 85, 247, 0.1);
}

.feature-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.05);
  border-color: rgba(168, 85, 247, 0.3);
}

.feature-icon {
  color: rgb(168, 85, 247);
  width: 24px;
  height: 24px;
}

.feature-text h3 {
  color: #374151;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.feature-text p {
  color: #6b7280;
  font-size: 0.813rem;
}

.launch-info {
  margin: 3rem 0;
}

.launch-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(249, 168, 212, 0.1));
  border-radius: 1rem;
  max-width: 400px;
  margin: 0 auto;
}

.launch-icon {
  color: rgb(168, 85, 247);
  width: 32px;
  height: 32px;
}

.launch-text h3 {
  color: #374151;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.launch-text p {
  color: rgb(168, 85, 247);
  font-size: 1.5rem;
  font-weight: 700;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(168, 85, 247, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(168, 85, 247, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(168, 85, 247, 0);
  }
}

@media (max-width: 640px) {
  .title {
    font-size: 1.75rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .feature-text h3 {
    font-size: 1rem;
  }

  .feature-text p {
    font-size: 0.75rem;
  }

  .launch-card {
    flex-direction: column;
    text-align: center;
  }

  .launch-text {
    flex-direction: column;
  }
}
</style>
