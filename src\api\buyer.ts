import request from '@/utils/request'
import type { PasswordChangeDTO, PasswordChangeResponse } from './types'
import type { AxiosResponse } from 'axios'

// 类型定义
interface BuyerDTO {
  id?: number
  accountName: string
  gender?: string
  phone?: string
  email?: string
}
interface SendCodeDTO {
  phone: string // 用户的手机号
}
interface BuyerRegisterDTO {
  accountName: string
  password: string
  gender: string
  phone: string
  email: string
  verificationCode: string // 验证码
}

interface BuyerLoginDTO {
  accountName: string
  password: string
  loginType?: 'email' | 'accountName' // 登录类型：邮箱或用户名
}

interface BuyerLoginVO {
  id: number
  accountName: string
  token: string
}

interface BuyerInfoVO {
  id: number
  accountName: string
  gender: string
  photoUrl: string
  phone: string
  email: string
  accountStatus: AccountStatus // 0为停用，1为启用
  createTime: string
  lastLoginTime: string
  address?: {
    province: string
    city: string
    detail: string
  }
}

interface SendCodeResponse {
  code: number
  msg: string
  data: null
}

interface RegisterResponse {
  code: number
  msg: string
  data: null
}

export enum BuyerErrorCode {
  NOT_LOGIN = 1001,
  INVALID_PRODUCT = 1002,
  EMPTY_COMMENT = 1003,
}

export enum AccountStatus {
  DISABLED = 0,
  ENABLED = 1,
}

// 添加 Result 类型定义
interface Result {
  msg: string
  code: number
  token: string
}

// 定义API通用响应结构
interface ApiResponse<T> {
  code: number
  msg: string | null
  data: T
}

// 买家相关接口
export const buyerApi = {
  // 注册
  register(data: BuyerRegisterDTO): Promise<RegisterResponse> {
    return request.post('/buyer/register', {
      accountName: data.accountName.trim(),
      password: data.password.trim(),
      gender: data.gender,
      phone: data.phone.trim(),
      email: data.email.trim(),
      verificationCode: data.verificationCode.trim(),
    })
  },
  // 登录
  login(data: BuyerLoginDTO): Promise<AxiosResponse<ApiResponse<BuyerLoginVO>>> {
    console.log('Making login request with data:', {
      accountName: data.accountName,
      password: data.password,
      loginType: data.loginType,
    })
    return request.post('/buyer/login', {
      accountName: data.accountName.trim(),
      password: data.password.trim(),
      loginType: data.loginType, // 添加登录类型参数
    })
  },

  // 获取买家信息
  getInfo(id: number) {
    return request.get<ApiResponse<BuyerInfoVO>>(`/buyer/${id}`)
  },

  // 修改买家信息
  updateInfo(data: BuyerDTO) {
    return request.put<ApiResponse<BuyerInfoVO>>('/api/buyers/buyers', data)
  },

  // 删除买家账户
  deleteAccount(id: number) {
    return request.delete<ApiResponse<null>>(`/api/buyers/buyers/${id}`)
  },

  // 处理错误
  handleError(code: number): string {
    switch (code) {
      case BuyerErrorCode.NOT_LOGIN:
        return '用户未登录'
      case BuyerErrorCode.INVALID_PRODUCT:
        return '无效的产品ID'
      case BuyerErrorCode.EMPTY_COMMENT:
        return '评论内容为空'
      default:
        return '未知错误'
    }
  },

  // 发送验证码
  sendCode(data: SendCodeDTO): Promise<SendCodeResponse> {
    return request.post('/buyer/register/sendCode', data)
  },

  // 修改密码
  changePassword(data: PasswordChangeDTO): Promise<PasswordChangeResponse> {
    console.log('Sending data to backend:', data)
    return request.post<PasswordChangeResponse>(
      '/Change/PassWord',
      JSON.stringify({
        phone: data.phone,
        oldPassword: data.oldPassword,
        newPassword: data.newPassword,
      }),
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    )
  },
}

// 导出获取买家信息的函数
export const getBuyerInfo = async () => {
  // 从localStorage中获取user对象并解析
  const userStr = localStorage.getItem('user')
  if (!userStr) {
    throw new Error('用户未登录')
  }

  try {
    const user = JSON.parse(userStr)
    if (!user.id) {
      throw new Error('用户信息不完整')
    }
    const response = await buyerApi.getInfo(parseInt(user.id.toString()))
    return response
  } catch (error) {
    console.error('解析用户信息失败:', error)
    throw new Error('获取用户信息失败')
  }
}

export type { BuyerDTO, BuyerRegisterDTO, BuyerLoginDTO, BuyerLoginVO, BuyerInfoVO }
