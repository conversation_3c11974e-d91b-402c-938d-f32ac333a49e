export default {
  header: {
    language: 'Language',
    userGuide: 'Update Notification',
    // ... 其他翻译
  },
  auth: {
    login: 'Login',
    welcomeBack: 'Welcome back! Please enter your details',
    accountOrEmail: 'Account or Email',
    accountOrEmailPlaceholder: 'Enter your account or email',
    password: 'Password',
    loggingIn: 'Logging in...',
    noAccount: 'Don\'t have an account?',
    userRegister: 'User Register',
    sellerRegister: 'Seller Register',
    forgotUsernameTip: 'Forgot username or email?',
    forgotUsername: 'Recover Username',
    forgotPasswordTip: 'Forgot password?',
    forgotPassword: 'Reset Password',
    adminLoginTip: 'Are you an administrator?',
    adminLogin: 'Admin Login',
    terms: {
      agreement: 'By continuing, you agree to our ',
      userAgreement: 'User Agreement',
      and: ' and ',
      privacyPolicy: 'Privacy Policy'
    }
  },
  // ... 其他翻译

  // 隐私政策页面
  privacyPolicy: {
    title: 'Privacy Policy',
    content: 'This Privacy Policy describes how we collect, use, and share your personal information...',
    // ... 其他内容
  },

  // 用户协议页面
  userAgreement: {
    title: 'User Agreement',
    content: 'By using our services, you agree to the following terms...',
    // ... 其他内容
  },

  // 找回用户名页面
  forgotUsername: {
    title: 'Recover Username',
    subtitle: 'Please fill in the information to verify your identity',
    emailLabel: 'Email',
    emailPlaceholder: 'Enter your registered email',
    verificationCodeLabel: 'Verification Code',
    verificationCodePlaceholder: 'Enter verification code',
    sendCode: 'Send Code',
    resendCode: 'Retry in {seconds}s',
    codeTip: 'Verification code will be sent to your email',
    submitButton: 'Verify Identity',
    verifying: 'Verifying...',
    rememberUsername: 'Remember your username?',
    backToLogin: 'Back to Login',
    success: 'Verification Successful',
    usernameInfo: 'Your username is:',
    emailInfo: 'Your email is:',
    updateSuccess: 'Username updated successfully!',
    setSuccess: 'Username set successfully!'
  },

  // 找回密码页面
  forgotPassword: {
    title: 'Reset Password',
    subtitle: 'Please fill in the information to verify your identity',
    usernameLabel: 'Username',
    usernamePlaceholder: 'Enter your username',
    emailLabel: 'Email',
    emailPlaceholder: 'Enter your email address',
    verificationCodeLabel: 'Verification Code',
    verificationCodePlaceholder: 'Enter verification code',
    sendCode: 'Send Code',
    resendCode: 'Retry in {seconds}s',
    codeTip: 'Verification code will be sent to your email',
    submitButton: 'Verify Identity',
    verifying: 'Verifying...',
    rememberAccount: 'Remember your account?',
    backToLogin: 'Back to Login'
  },

  // 管理员登录页面
  adminLogin: {
    title: 'Administrator Login',
    // ... 其他内容
  },

  // 更新公告页面
  updateNotification: {
    title: 'System Update Notification',
    importantUpdate: 'Important Update',
    version: 'Version 1.0.1',
    releaseTime: 'Release Time',
    systemUpgrade: 'System Feature Upgrade Announcement',
    description: 'We are pleased to announce that the system has been upgraded with new features and optimizations.',

    // 标签页
    tabs: {
      features: 'Updates',
      guide: 'Guide',
      feedback: 'Feedback'
    },

    // 功能特性
    features: {
      performance: {
        title: 'Performance Optimization',
        description: 'Significantly improved system response speed and faster page loading'
      },
      interface: {
        title: 'Interface Upgrade',
        description: 'New user interface design for better visual experience'
      },
      security: {
        title: 'Security Enhancement',
        description: 'Added multiple security protection mechanisms to protect user data'
      },
      ai: {
        title: 'Smart Recommendations',
        description: 'AI-based intelligent recommendation system for personalized service'
      }
    },

    // 使用指南
    guide: {
      title: 'User Guide',
      steps: [
        'Update to the latest version to experience new features',
        'Check the new feature tutorial for detailed usage',
        'Provide timely feedback on issues for continuous improvement',
        'Follow system announcements for latest updates'
      ]
    },

    // 问题反馈
    feedback: {
      title: 'Feedback',
      placeholder: 'Please enter your feedback...',
      submit: 'Submit Feedback',
      success: 'Thank you for your feedback!',
      empty: 'Please enter feedback content'
    },

    // 底部操作
    actions: {
      like: 'Like',
      bookmark: 'Bookmark',
      share: 'Share Update',
      shareMessage: 'Share feature in development...'
    },

    // 相关更新
    relatedUpdates: {
      mobileOptimization: {
        title: 'Mobile Adaptation Optimization',
        description: 'Optimize mobile display effect to improve user experience',
        tag: 'Performance Optimization'
      },
      dataAnalysis: {
        title: 'New Data Analysis Feature',
        description: 'Support for more data dimension analysis and visualization',
        tag: 'Feature Update'
      }
    }
  },

  // 用户注册页面
  register: {
    title: 'User Registration',
    subtitle: 'Create your account to start a new experience',
    accountNamePlaceholder: 'Enter account name',
    passwordPlaceholder: 'Enter password',
    confirmPasswordPlaceholder: 'Confirm password',
    passwordStrength: 'Password strength',
    strength: {
      weak: 'Weak',
      medium: 'Medium',
      strong: 'Strong'
    },
    passwordRules: {
      minLength: 'At least 8 characters',
      upperCase: 'At least one uppercase letter',
      lowerCase: 'At least one lowercase letter',
      number: 'At least one number'
    },
    gender: {
      male: 'Male',
      female: 'Female'
    },
    phonePlaceholder: 'Enter phone number',
    emailPlaceholder: 'Enter email for verification code',
    emailTip: 'Please enter a valid email to receive verification code',
    verificationCodePlaceholder: 'Enter verification code',
    sendCode: 'Send Code',
    resendCode: 'Retry in {seconds}s',
    sendingCode: 'Sending code...',
    codeTip: 'Verification code will be sent to your email',
    inviteCodePlaceholder: 'Enter invite code (optional)',
    inviteCodeTip: 'If you have an invite code, you can enter it here',
    registerButton: 'Register',
    registering: 'Registering...',
    haveAccount: 'Already have an account?',
    loginNow: 'Login now'
  },

  // 商家注册页面
  sellerRegister: {
    title: 'Seller Registration',
    subtitle: 'Create your seller account to start your business journey',
    shopNamePlaceholder: 'Enter shop name',
    mobilePlaceholder: 'Enter contact phone',
    contactNamePlaceholder: 'Enter contact name',
    provincePlaceholder: 'Enter province',
    cityPlaceholder: 'Enter city',
    detailAddressPlaceholder: 'Enter detailed address',
    submitButton: 'Submit Application',
    submitting: 'Submitting...',
    haveAccount: 'Already have a seller account?',
    loginNow: 'Login now'
  }
}
