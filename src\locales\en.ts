export default {
  header: {
    language: 'English',
    userGuide: 'User Guide',
    search: 'Search products...',
    cart: 'Cart',
    userInfo: 'User Info',
    login: 'Login To See Price',
  },
  nav: {
    nbs: 'NBS Selected',
    selected: 'Featured',
    newArrivals: 'New Arrivals',
    bestSellers: 'Best Sellers',
    allProducts: 'All Products',
    categories: {
      all: 'All',
      electronics: 'Electronics',
      mobile: 'Mobile Phones',
      laptop: 'Laptops',
      appliances: 'Home Appliances',
      tv: 'TV',
      refrigerator: 'Refrigerator',
      kitchenAppliances: 'Kitchen Appliances',
      clothing: 'Clothing',
      menswear: "Men's Wear",
      womenswear: "Women's Wear",
      shoes: 'Shoes',
      sneakers: 'Sneakers',
      leatherShoes: 'Leather Shoes',
      accessories: 'Accessories',
      jewelry: 'Jewelry',
      watches: 'Watches',
      books: 'Books',
      novels: 'Novels',
    },
  },
  cart: {
    title: 'Shopping Cart',
    empty: 'Cart is empty',
    total: 'Total',
    checkout: 'Checkout',
  },
  product: {
    buyNow: 'Buy Now',
    addToCart: 'Add to Cart',
    featured: 'Featured Products',
    all: 'All Products',
    categories: 'Categories',
  },
  auth: {
    register: 'Register',
    login: 'Login',
    accountName: 'Account Name',
    accountOrEmail: 'Account or Email',
    accountOrEmailPlaceholder: 'Enter account name or email',
    forgotUsername: 'Recover Username',
    forgotUsernameTip: 'Forgot username or email?',
    forgotPassword: 'Forgot Password',
    forgotPasswordTip: 'Forgot your password?',
    password: 'Password',
    gender: '性别',
    male: '男',
    female: '女',
    phone: '联系电话',
    email: '电子邮箱',
    registering: '注册中',
    registerSuccess: '注册成功',
    registerFailed: '注册失败',
    alreadyHaveAccount: '已有账号？',
    goToLogin: '去登录',
    loggingIn: '登录中',
    loginSuccess: '登录成功',
    loginFailed: '登录失败',
    noAccount: "Don't have an account?",
    goToRegister: '去注册',
    pleaseInputAll: '请输入账号和密码',
    logout: 'Logout',
    userRegister: 'User Register',
    sellerRegister: 'Seller Register',
    terms: {
      agreement: 'By continuing, you agree to our',
      userAgreement: 'User Agreement',
      and: 'and',
      privacyPolicy: 'Privacy Policy',
    },
  },
  store: {
    startJourney: 'Start Your E-commerce Journey',
    createStore: 'Create your store and start selling online',
  },
}
