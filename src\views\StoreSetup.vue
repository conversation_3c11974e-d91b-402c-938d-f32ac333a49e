<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-white to-purple-50 py-8 px-4">
    <!-- Header with Progress -->
    <div class="max-w-6xl mx-auto mb-8">
      <div class="text-center space-y-2 mb-6">
        <h1 class="text-3xl font-bold text-gray-900">创建您的在线商店</h1>
        <p class="text-gray-600">填写详细信息，开启您的电商之旅</p>
      </div>

      <!-- Progress Steps -->
      <div class="max-w-3xl mx-auto">
        <div class="relative">
          <div class="absolute left-0 top-1/2 h-0.5 w-full bg-gray-200 -translate-y-1/2"></div>
          <div
            class="absolute left-0 top-1/2 h-0.5 bg-purple-600 -translate-y-1/2 transition-all duration-300"
            :style="{ width: `${(currentStep / 3) * 100}%` }"
          ></div>
          <div class="relative flex justify-between">
            <div v-for="(step, index) in steps" :key="index" class="flex flex-col items-center">
              <button
                @click="goToStep(index + 1)"
                :disabled="!canNavigateToStep(index + 1)"
                :class="[
                  'w-10 h-10 rounded-full flex items-center justify-center relative z-10 transition-all duration-300',
                  currentStep > index + 1
                    ? 'bg-purple-600 text-white'
                    : currentStep === index + 1
                      ? 'bg-purple-600 text-white ring-4 ring-purple-100'
                      : 'bg-white border-2 border-gray-200 text-gray-400',
                ]"
              >
                <component :is="step.icon" class="w-5 h-5" />
              </button>
              <span
                class="mt-2 text-sm font-medium"
                :class="currentStep >= index + 1 ? 'text-purple-600' : 'text-gray-400'"
              >
                {{ step.title }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Form Content -->
    <div class="max-w-6xl mx-auto">
      <div class="bg-white rounded-xl shadow-xl overflow-hidden">
        <form @submit.prevent="handleSubmit" class="p-6">
          <TransitionGroup name="fade" mode="out-in">
            <!-- Step 1: Basic Information -->
            <div v-if="currentStep === 1" key="step1" class="space-y-8">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Store Information -->
                <div class="space-y-6">
                  <h3 class="text-lg font-medium text-gray-900 flex items-center gap-2">
                    <Store class="w-5 h-5 text-purple-600" />
                    店铺信息
                  </h3>

                  <div class="space-y-4">
                    <label class="block">
                      <span class="text-sm font-medium text-gray-700">店铺名称</span>
                      <input
                        v-model="form.storeName"
                        type="text"
                        :class="[inputClass, errors.storeName ? errorInputClass : normalInputClass]"
                        placeholder="输入店铺名称"
                        @blur="handleBlur('storeName')"
                        @input="handleInput('storeName')"
                      />
                      <span
                        v-if="touched.storeName && errors.storeName"
                        class="text-sm text-red-500 mt-1"
                      >
                        {{ errors.storeName }}
                      </span>
                    </label>

                    <label class="block">
                      <span class="text-sm font-medium text-gray-700">经营类目</span>
                      <select
                        v-model="form.category"
                        :class="[inputClass, normalInputClass]"
                        @blur="handleBlur('category')"
                        @change="handleInput('category')"
                      >
                        <option value="">选择经营类目</option>
                        <option
                          v-for="category in categories"
                          :key="category.value"
                          :value="category.value"
                        >
                          {{ category.label }}
                        </option>
                      </select>
                      <span
                        v-if="touched.category && errors.category"
                        class="text-sm text-red-500 mt-1"
                      >
                        {{ errors.category }}
                      </span>
                    </label>

                    <label class="block">
                      <span class="text-sm font-medium text-gray-700">店铺简介</span>
                      <textarea
                        v-model="form.description"
                        rows="4"
                        :class="[inputClass, normalInputClass]"
                        placeholder="描述您的店铺特色..."
                      ></textarea>
                    </label>
                  </div>
                </div>

                <!-- Owner Information -->
                <div class="space-y-6">
                  <h3 class="text-lg font-medium text-gray-900 flex items-center gap-2">
                    <User class="w-5 h-5 text-purple-600" />
                    店主信息
                  </h3>

                  <div class="space-y-4">
                    <label class="block">
                      <span class="text-sm font-medium text-gray-700">店主姓名</span>
                      <input
                        v-model="form.ownerName"
                        type="text"
                        :class="[inputClass, errors.ownerName ? errorInputClass : normalInputClass]"
                        placeholder="输入店主姓名"
                        @blur="handleBlur('ownerName')"
                        @input="handleInput('ownerName')"
                      />
                      <span
                        v-if="touched.ownerName && errors.ownerName"
                        class="text-sm text-red-500 mt-1"
                      >
                        {{ errors.ownerName }}
                      </span>
                    </label>

                    <label class="block">
                      <span class="text-sm font-medium text-gray-700">联系电话</span>
                      <input
                        v-model="form.phone"
                        type="tel"
                        :class="[inputClass, errors.phone ? errorInputClass : normalInputClass]"
                        placeholder="输入联系电话"
                        @blur="handleBlur('phone')"
                        @input="handleInput('phone')"
                      />
                      <span v-if="touched.phone && errors.phone" class="text-sm text-red-500 mt-1">
                        {{ errors.phone }}
                      </span>
                    </label>

                    <label class="block">
                      <span class="text-sm font-medium text-gray-700">电子邮箱</span>
                      <input
                        v-model="form.email"
                        type="email"
                        :class="[inputClass, errors.email ? errorInputClass : normalInputClass]"
                        placeholder="输入电子邮箱"
                        @blur="handleBlur('email')"
                        @input="handleInput('email')"
                      />
                      <span v-if="touched.email && errors.email" class="text-sm text-red-500 mt-1">
                        {{ errors.email }}
                      </span>
                    </label>
                  </div>
                </div>
              </div>

              <!-- Store Type Selection -->
              <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 flex items-center gap-2">
                  <Store class="w-5 h-5 text-purple-600" />
                  店铺类型
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div
                    v-for="type in storeTypes"
                    :key="type.value"
                    @click="form.type = type.value"
                    class="relative rounded-lg border-2 p-4 cursor-pointer transition-all duration-200 hover:shadow-md"
                    :class="
                      form.type === type.value
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200'
                    "
                  >
                    <div class="flex items-center space-x-3">
                      <component
                        :is="type.icon"
                        class="w-6 h-6"
                        :class="form.type === type.value ? 'text-purple-600' : 'text-gray-400'"
                      />
                      <div>
                        <h4
                          class="font-medium"
                          :class="form.type === type.value ? 'text-purple-600' : 'text-gray-900'"
                        >
                          {{ type.label }}
                        </h4>
                        <p class="text-sm text-gray-500">{{ type.description }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Step 2: Store Settings -->
            <div v-if="currentStep === 2" key="step2" class="space-y-8">
              <!-- Store Logo Upload -->
              <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 flex items-center gap-2">
                  <Image class="w-5 h-5 text-purple-600" />
                  店铺形象
                </h3>
                <div class="flex items-start space-x-6">
                  <div class="relative group">
                    <div
                      class="w-40 h-40 rounded-lg border-2 border-dashed flex items-center justify-center overflow-hidden transition-all duration-200"
                      :class="[
                        isDragging ? 'border-purple-500 bg-purple-50' : 'border-gray-300',
                        form.logo ? 'border-solid' : 'border-dashed',
                      ]"
                    >
                      <template v-if="form.logo">
                        <img :src="form.logo" class="w-full h-full object-cover" />
                        <div
                          class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center"
                        >
                          <button
                            type="button"
                            @click="removeLogo"
                            class="text-white hover:text-red-400 transition-colors duration-200"
                          >
                            <Trash2 class="w-6 h-6" />
                          </button>
                        </div>
                      </template>
                      <template v-else>
                        <div class="text-center">
                          <Upload class="mx-auto h-12 w-12 text-gray-400" />
                          <p class="mt-2 text-sm text-gray-500">点击或拖拽上传</p>
                        </div>
                      </template>
                      <input
                        type="file"
                        @change="handleLogoUpload"
                        @dragenter="isDragging = true"
                        @dragleave="isDragging = false"
                        @drop="isDragging = false"
                        accept="image/*"
                        class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      />
                    </div>
                  </div>
                  <div class="space-y-2">
                    <h4 class="font-medium text-gray-900">店铺Logo</h4>
                    <p class="text-sm text-gray-500">建议尺寸：400x400px，支持 jpg、png 格式</p>
                    <div class="flex space-x-2">
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                      >
                        JPG
                      </span>
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                      >
                        PNG
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Business Hours and Location -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="space-y-6">
                  <h3 class="text-lg font-medium text-gray-900 flex items-center gap-2">
                    <Clock class="w-5 h-5 text-purple-600" />
                    营业时间
                  </h3>
                  <div class="grid grid-cols-2 gap-4">
                    <label class="block">
                      <span class="text-sm font-medium text-gray-700">开始时间</span>
                      <input
                        v-model="form.businessHoursStart"
                        type="time"
                        class="mt-1 block w-full rounded-lg border-gray-300 focus:border-purple-500 focus:ring-purple-500"
                      />
                    </label>
                    <label class="block">
                      <span class="text-sm font-medium text-gray-700">结束时间</span>
                      <input
                        v-model="form.businessHoursEnd"
                        type="time"
                        class="mt-1 block w-full rounded-lg border-gray-300 focus:border-purple-500 focus:ring-purple-500"
                      />
                    </label>
                  </div>
                  <div class="space-y-2">
                    <span class="text-sm font-medium text-gray-700">营业日</span>
                    <div class="flex flex-wrap gap-2">
                      <button
                        v-for="day in businessDays"
                        :key="day.value"
                        type="button"
                        @click="toggleBusinessDay(day.value)"
                        :class="[
                          'px-3 py-1 rounded-full text-sm font-medium transition-colors duration-200',
                          form.businessDays.includes(day.value)
                            ? 'bg-purple-600 text-white'
                            : 'bg-gray-100 text-gray-600 hover:bg-gray-200',
                        ]"
                      >
                        {{ day.label }}
                      </button>
                    </div>
                  </div>
                </div>

                <div class="space-y-6">
                  <h3 class="text-lg font-medium text-gray-900 flex items-center gap-2">
                    <MapPin class="w-5 h-5 text-purple-600" />
                    店铺地址
                  </h3>
                  <div class="space-y-4">
                    <label class="block">
                      <span class="text-sm font-medium text-gray-700">详细地址</span>
                      <textarea
                        v-model="form.address"
                        rows="3"
                        class="mt-1 block w-full rounded-lg border-gray-300 focus:border-purple-500 focus:ring-purple-500"
                        placeholder="输入店铺详细地址"
                      ></textarea>
                    </label>
                  </div>
                </div>
              </div>

              <!-- Store Features -->
              <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 flex items-center gap-2">
                  <Star class="w-5 h-5 text-purple-600" />
                  店铺特色
                </h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div
                    v-for="feature in storeFeatures"
                    :key="feature.value"
                    @click="toggleFeature(feature.value)"
                    class="relative rounded-lg border p-4 cursor-pointer transition-all duration-200"
                    :class="
                      form.features.includes(feature.value)
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200'
                    "
                  >
                    <div class="flex items-center justify-between">
                      <span
                        class="font-medium"
                        :class="
                          form.features.includes(feature.value)
                            ? 'text-purple-600'
                            : 'text-gray-900'
                        "
                      >
                        {{ feature.label }}
                      </span>
                      <CheckCircle2
                        v-if="form.features.includes(feature.value)"
                        class="w-5 h-5 text-purple-600"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Step 3: Payment Settings -->
            <div v-if="currentStep === 3" key="step3" class="space-y-8">
              <!-- Payment Methods -->
              <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 flex items-center gap-2">
                  <CreditCard class="w-5 h-5 text-purple-600" />
                  支付方式
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div
                    v-for="method in paymentMethods"
                    :key="method.value"
                    @click="togglePaymentMethod(method.value)"
                    class="relative rounded-lg border p-4 cursor-pointer transition-all duration-200"
                    :class="
                      form.paymentMethods.includes(method.value)
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200'
                    "
                  >
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-3">
                        <component
                          :is="method.icon"
                          class="w-6 h-6"
                          :class="
                            form.paymentMethods.includes(method.value)
                              ? 'text-purple-600'
                              : 'text-gray-400'
                          "
                        />
                        <div>
                          <h4
                            class="font-medium"
                            :class="
                              form.paymentMethods.includes(method.value)
                                ? 'text-purple-600'
                                : 'text-gray-900'
                            "
                          >
                            {{ method.label }}
                          </h4>
                          <p class="text-sm text-gray-500">{{ method.description }}</p>
                        </div>
                      </div>
                      <CheckCircle2
                        v-if="form.paymentMethods.includes(method.value)"
                        class="w-5 h-5 text-purple-600"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- Settlement Information -->
              <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 flex items-center gap-2">
                  <Wallet class="w-5 h-5 text-purple-600" />
                  结算信息
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <label class="block">
                    <span class="text-sm font-medium text-gray-700">开户银行</span>
                    <select
                      v-model="form.bankName"
                      class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                    >
                      <option value="">选择开户银行</option>
                      <option v-for="bank in banks" :key="bank" :value="bank">
                        {{ bank }}
                      </option>
                    </select>
                  </label>

                  <label class="block">
                    <span class="text-sm font-medium text-gray-700">银行账号</span>
                    <input
                      v-model="form.bankAccount"
                      type="text"
                      class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
                      placeholder="输入银行账号"
                    />
                  </label>
                </div>
              </div>

              <!-- Additional Settings -->
              <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 flex items-center gap-2">
                  <Settings class="w-5 h-5 text-purple-600" />
                  额外设置
                </h3>
                <div class="space-y-4">
                  <div
                    v-for="setting in additionalSettings"
                    :key="setting.value"
                    class="flex items-center justify-between p-4 rounded-lg border border-gray-200 hover:border-purple-200 transition-colors duration-200"
                  >
                    <div>
                      <h4 class="font-medium text-gray-900">{{ setting.label }}</h4>
                      <p class="text-sm text-gray-500">{{ setting.description }}</p>
                    </div>
                    <button
                      type="button"
                      @click="toggleSetting(setting.value)"
                      :class="[
                        'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2',
                        form.settings[setting.value] ? 'bg-purple-600' : 'bg-gray-200',
                      ]"
                    >
                      <span
                        :class="[
                          'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                          form.settings[setting.value] ? 'translate-x-5' : 'translate-x-0',
                        ]"
                      />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </TransitionGroup>

          <!-- Navigation Buttons -->
          <div class="mt-8 flex justify-between">
            <button
              v-if="currentStep > 1"
              type="button"
              @click="prevStep"
              class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200"
            >
              <ChevronLeft class="w-5 h-5 mr-1" />
              上一步
            </button>
            <button
              v-if="currentStep < 3"
              type="button"
              @click="nextStep"
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200"
            >
              下一步
              <ChevronRight class="w-5 h-5 ml-1" />
            </button>
            <button
              v-if="currentStep === 3"
              type="submit"
              :disabled="isSubmitting"
              class="inline-flex items-center px-6 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              <template v-if="isSubmitting">
                <Loader2 class="w-5 h-5 mr-2 animate-spin" />
                创建中...
              </template>
              <template v-else>
                <CheckCircle2 class="w-5 h-5 mr-2" />
                完成创建
              </template>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { storeApi } from '@/api/store'
import type { StoreSettings } from '@/types'
import {
  Store,
  User,
  Phone,
  Mail,
  MapPin,
  Calendar,
  CreditCard,
  Settings,
  Building2,
  ShoppingBag,
  ChevronLeft,
  ChevronRight,
  CheckCircle2,
  Loader2,
  Wallet,
  Star,
  Clock,
  Image,
  Trash2,
  Truck,
} from 'lucide-vue-next'

const router = useRouter()
const currentStep = ref(1)
const isDragging = ref(false)
const isSubmitting = ref(false)
const errors = reactive<Errors>({})

const steps = [
  { title: '基本信息', icon: Store },
  { title: '店铺设置', icon: Building2 },
  { title: '支付设置', icon: CreditCard },
]

const storeTypes = [
  {
    value: 'retail',
    label: '零售店',
    icon: Store,
    description: '面向终端消费者的零售业务',
  },
  {
    value: 'wholesale',
    label: '批发店',
    icon: Building2,
    description: '面向企业的批发业务',
  },
  {
    value: 'service',
    label: '服务类',
    icon: ShoppingBag,
    description: '提供专业服务',
  },
]

const categories = [
  { value: 'digital', label: '数码电子' },
  { value: 'clothing', label: '服装服饰' },
  { value: 'food', label: '食品生鲜' },
  { value: 'home', label: '家居用品' },
  { value: 'beauty', label: '美妆个护' },
]

const businessDays = [
  { value: 'mon', label: '周一' },
  { value: 'tue', label: '周二' },
  { value: 'wed', label: '周三' },
  { value: 'thu', label: '周四' },
  { value: 'fri', label: '周五' },
  { value: 'sat', label: '周六' },
  { value: 'sun', label: '周日' },
]

const storeFeatures = [
  { value: 'delivery', label: '同城配送' },
  { value: 'pickup', label: '到店自提' },
  { value: 'return', label: '七天退换' },
  { value: 'warranty', label: '质保服务' },
  { value: 'installation', label: '上门安装' },
  { value: 'repair', label: '维修服务' },
  { value: 'vip', label: '会员服务' },
  { value: 'coupon', label: '优惠券' },
]

const paymentMethods = [
  { value: 'alipay', label: '支付宝', icon: Wallet },
  { value: 'wechat', label: '微信支付', icon: Wallet },
  { value: 'unionpay', label: '银联支付', icon: CreditCard },
]

const banks = [
  '工商银行',
  '建设银行',
  '农业银行',
  '中国银行',
  '交通银行',
  '招商银行',
  '浦发银行',
  '中信银行',
]

const additionalSettings = [
  {
    value: 'invoice',
    label: '开启发票功能',
    description: '允许客户索取发票',
  },
  {
    value: 'autoConfirm',
    label: '自动确认订单',
    description: '收到付款自动确认订单',
  },
  {
    value: 'sms',
    label: '短信通知',
    description: '开启短信通知服务',
  },
  {
    value: 'inventory',
    label: '库存预警',
    description: '启用库存不足提醒',
  },
]

const form = reactive<FormState>({
  // 必填字段
  sellerId: 0, // 将从localStorage获取
  storeName: '',
  storeStatus: 1, // 默认开启状态

  // 选填字段
  photo: '',
  storeDescription: '',
  storeAddress: '',
  // 扩展信息（选填）
  category: '',
  ownerName: '',
  phone: '',
  email: '',
  type: 'retail',

  // 店铺设置（选填）
  logo: null,
  businessHoursStart: '',
  businessHoursEnd: '',
  businessDays: ['mon', 'tue', 'wed', 'thu', 'fri'],
  features: [],

  // 支付设置（选填）
  paymentMethods: [],
  bankName: '',
  bankAccount: '',
  settings: {
    invoice: false,
    autoConfirm: true,
    sms: false,
    inventory: false,
  },
})

const inputClass = `
  mt-1 block w-full rounded-lg border-2 shadow-sm
  transition-all duration-200 py-3 px-4
  focus:ring-2 focus:ring-purple-500 focus:border-purple-500
`
const errorInputClass = 'border-red-300 focus:ring-red-500 focus:border-red-500'
const normalInputClass = 'border-gray-300'

const touched = reactive<TouchedFields>({
  storeName: false,
})

const handleBlur = (field: keyof FormState) => {
  touched[field] = true
  validateField(field)
}

const handleInput = (field: keyof FormState) => {
  if (touched[field]) {
    validateField(field)
  }
}

const validateField = (field: keyof FormState) => {
  delete errors[field]

  switch (field) {
    case 'storeName':
      if (!form.storeName?.trim()) {
        errors[field] = '请输入店铺名称'
      } else if (form.storeName.length < 2 || form.storeName.length > 50) {
        errors[field] = '店铺名称长度应在2-50个字符之间'
      }
      break
  }
}

const handleLogoUpload = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    if (file.size > 5 * 1024 * 1024) {
      ElMessage.warning('文件大小不能超过5MB')
      return
    }
    const reader = new FileReader()
    reader.onload = (e) => {
      form.logo = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

const removeLogo = () => {
  form.logo = null
}

const toggleBusinessDay = (day: string) => {
  const index = form.businessDays.indexOf(day)
  if (index === -1) {
    form.businessDays.push(day)
  } else {
    form.businessDays.splice(index, 1)
  }
}

const toggleFeature = (feature: string) => {
  const index = form.features.indexOf(feature)
  if (index === -1) {
    form.features.push(feature)
  } else {
    form.features.splice(index, 1)
  }
}

const togglePaymentMethod = (method: string) => {
  const index = form.paymentMethods.indexOf(method)
  if (index === -1) {
    form.paymentMethods.push(method)
  } else {
    form.paymentMethods.splice(index, 1)
  }
}

const toggleSetting = (setting: keyof typeof form.settings) => {
  form.settings[setting] = !form.settings[setting]
}

const canNavigateToStep = (step) => {
  if (step < currentStep.value) return true
  if (step === currentStep.value + 1) return validateStep(currentStep.value)
  return false
}

const goToStep = (step) => {
  if (canNavigateToStep(step)) {
    currentStep.value = step
  }
}

const nextStep = () => {
  if (validateStep(currentStep.value)) {
    currentStep.value++
  }
}

const prevStep = () => {
  currentStep.value--
}

const handleSubmit = async () => {
  if (!validateStep(currentStep.value)) return
  isSubmitting.value = true
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}')
    if (!user.id) {
      ElMessage.error('请先登录')
      return
    }

    // 只提交 API 需要的字段
    const storeData = {
      sellerId: user.id,
      storeName: form.storeName,
      storeStatus: form.storeStatus,
      photo: form.photo || undefined,
      storeDescription: form.storeDescription || undefined,
      storeAddress: form.storeAddress || undefined,
    }

    const response = await storeApi.createStore(storeData)
    if (response.code === 1) {
      // 保存额外信息到本地存储，供后续使用
      localStorage.setItem(
        'storeExtendInfo',
        JSON.stringify({
          category: form.category,
          ownerName: form.ownerName,
          phone: form.phone,
          email: form.email,
          type: form.type,
          businessHours: {
            start: form.businessHoursStart,
            end: form.businessHoursEnd,
            days: form.businessDays,
          },
          features: form.features,
          paymentMethods: form.paymentMethods,
          settings: form.settings,
        }),
      )

      ElMessage.success('店铺创建成功')
      router.push('/store/manage')
    } else {
      ElMessage.error(response.msg || '创建失败')
    }
  } catch (error) {
    console.error('Submit error:', error)
    ElMessage.error('创建失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

const validateStep = (step) => {
  if (step === 1) {
    // 标记所有字段为已触摸
    Object.keys(touched).forEach((key) => {
      touched[key] = true
      validateField(key)
    })
  }
  return Object.keys(errors).length === 0
}
</script>
<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>
