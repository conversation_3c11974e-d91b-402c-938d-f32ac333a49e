import request from '@/utils/request'
import type { ApiResponse } from './types'

export enum LogisticsStatus {
  SHIPPING = 1,
  ARRIVED = 2,
}

interface LogisticsInfoDTO {
  id?: number
  logisticsNumber: string
  orderId: number
  logisticsCompany: string
  logisticsStatus: LogisticsStatus
  shippingDate: string
}

interface LogisticsInfoVO extends LogisticsInfoDTO {
  createTime: string
  shippingDate: string
}

export const logisticsApi = {
  // 创建物流信息
  createLogistics(data: LogisticsInfoDTO) {
    return request.post<ApiResponse<null>>('/logistics/create', data)
  },

  // 查询物流信息
  getLogisticsInfo(id: number) {
    return request.get<ApiResponse<LogisticsInfoVO>>(`/logistics/${id}`)
  },

  // 更新物流状态
  updateLogisticsStatus(id: number, logisticsStatus: LogisticsStatus) {
    return request.put<ApiResponse<null>>(`/logistics/update/${id}`, { logisticsStatus })
  },

  // 添加获取用户物流列表的接口
  getUserLogisticsList(userId: number) {
    return request.get<ApiResponse<LogisticsInfoVO[]>>(`/logistics/user/${userId}`)
  },
}
