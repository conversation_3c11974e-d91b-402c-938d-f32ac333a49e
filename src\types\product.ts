// 分页查询参数
export interface ProductPageQueryDTO {
  page: number
  pageSize: number
  productName?: string
  categoryId?: number
  productStatus?: number
}

// 分页结果
export interface PageResult<T> {
  total: number
  records: T[]
}

// 产品详情
export interface ProductVO {
  id: number // 商品ID
  productSnapshotId: number // 商品快照ID
  brandId: number // 品牌ID
  categoryId: number // 分类ID
  outProductId: string // 商品编码
  name: string // 商品名称
  pic: string // 主图
  albumPics: string // 画册图片
  publishStatus: number // 上架状态：0->下架；1->上架
  pdfDocument: string // PDF文档
  sort: number // 排序
  inventory:number // 库存
  price: number // 价格
  unit: string // 单位
  weight: number // 商品重量，默认克
  detailHtml: string // 产品详情网页内容
  detailMobileHtml: string // 移动端网页详情
  brandName: string // 品牌名称
  productCategoryName: string // 商品分类名称
  createBy: number // 创建人
  createTime: string // 创建时间
  updateBy: number // 修改人
  updateTime: string // 修改时间
  productAttr: string // 商品销售属性，json格式
}

// API 响应格式
export interface ApiResponse<T> {
  code: number
  msg: string | null
  data: T
}
