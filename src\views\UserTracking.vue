<template>
  <div class="user-tracking-page">
    <div class="background-circle bg-circle-1"></div>
    <div class="background-circle bg-circle-2"></div>
    <div class="background-circle bg-circle-3"></div>

    <div class="container">
      <div class="header">
        <h1>物流查询中心</h1>
        <p>查询您的订单物流信息和跟踪详情</p>
      </div>

      <!-- 查询表单 -->
      <div class="card search-section">
        <h2>物流查询</h2>
        <div class="search-form">
          <div class="form-row">
            <div class="form-group">
              <label>物流单号</label>
              <input
                v-model="trackingNumber"
                type="text"
                placeholder="请输入物流单号"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label>运输商</label>
              <select v-model="selectedCarrier" class="form-select">
                <option value="">请选择运输商</option>
                <option v-for="carrier in carriers" :key="carrier.code" :value="carrier.code">
                  {{ carrier.name }}
                </option>
              </select>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>订单ID</label>
              <input
                v-model="orderId"
                type="number"
                placeholder="请输入订单ID"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <button @click="searchByTracking" class="search-btn" :disabled="loading">
                {{ loading ? '查询中...' : '查询物流' }}
              </button>
              <button @click="searchByOrder" class="search-btn secondary" :disabled="loading">
                {{ loading ? '查询中...' : '查询订单物流' }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 物流详情 -->
      <div v-if="trackingDetail" class="card tracking-detail">
        <h2>物流详情</h2>
        <div class="detail-header">
          <div class="detail-info">
            <div class="info-item">
              <span class="label">物流单号：</span>
              <span class="value">{{ trackingDetail.trackingNumber }}</span>
            </div>
            <div class="info-item">
              <span class="label">运输商：</span>
              <span class="value">{{ trackingDetail.carrierName }}</span>
            </div>
            <div class="info-item">
              <span class="label">订单号：</span>
              <span class="value">{{ trackingDetail.orderNumber }}</span>
            </div>
            <div class="info-item">
              <span class="label">当前状态：</span>
              <span :class="['status', `status-${trackingDetail.status}`]">
                {{ trackingDetail.statusDesc }}
              </span>
            </div>
          </div>
          <div class="detail-times">
            <div class="time-item" v-if="trackingDetail.pickupTime">
              <span class="label">揽收时间：</span>
              <span class="value">{{ formatDate(trackingDetail.pickupTime) }}</span>
            </div>
            <div class="time-item" v-if="trackingDetail.deliveryTime">
              <span class="label">签收时间：</span>
              <span class="value">{{ formatDate(trackingDetail.deliveryTime) }}</span>
            </div>
            <div class="time-item">
              <span class="label">最后更新：</span>
              <span class="value">{{ formatDate(trackingDetail.latestEventTime) }}</span>
            </div>
          </div>
        </div>

        <!-- 物流轨迹 -->
        <div class="tracking-timeline">
          <h3>物流轨迹</h3>
          <div class="timeline">
            <div v-for="(event, index) in trackingDetail.events" :key="index" class="timeline-item">
              <div class="timeline-dot"></div>
              <div class="timeline-content">
                <div class="event-time">{{ formatDate(event.time) }}</div>
                <div class="event-status">{{ event.status }}</div>
                <div class="event-location">{{ event.location }}</div>
                <div class="event-description">{{ event.description }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 订单物流列表 -->
      <div v-if="orderTrackingList.length > 0" class="card order-tracking-list">
        <h2>订单物流信息</h2>
        <div class="tracking-list">
          <div
            v-for="tracking in orderTrackingList"
            :key="tracking.id"
            class="tracking-item"
            @click="selectTracking(tracking)"
          >
            <div class="tracking-header">
              <div class="tracking-number">{{ tracking.trackingNumber }}</div>
              <div :class="['tracking-status', `status-${tracking.status}`]">
                {{ tracking.statusDesc }}
              </div>
            </div>
            <div class="tracking-info">
              <div class="info-row">
                <span class="label">运输商：</span>
                <span class="value">{{ tracking.carrierName }}</span>
              </div>
              <div class="info-row">
                <span class="label">最新事件：</span>
                <span class="value">{{ tracking.latestEventInfo }}</span>
              </div>
              <div class="info-row">
                <span class="label">更新时间：</span>
                <span class="value">{{ formatDate(tracking.latestEventTime) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div
        v-if="!trackingDetail && orderTrackingList.length === 0 && !loading"
        class="card empty-state"
      >
        <div class="empty-icon">📦</div>
        <p>请输入物流单号或订单ID查询物流信息</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import { userTrackingApi, type TrackingDetailVO, type CarrierVO } from '@/api/order'

defineOptions({
  name: 'UserTracking',
})

const route = useRoute()

// 状态变量
const loading = ref(false)
const trackingNumber = ref('')
const selectedCarrier = ref<number | ''>('')
const orderId = ref<number | ''>('')
const trackingDetail = ref<TrackingDetailVO | null>(null)
const orderTrackingList = ref<TrackingDetailVO[]>([])
const carriers = ref<CarrierVO[]>([])

// 格式化日期
const formatDate = (dateString: string): string => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 根据物流单号查询
const searchByTracking = async () => {
  if (!trackingNumber.value.trim()) {
    ElMessage.warning('请输入物流单号')
    return
  }

  loading.value = true
  try {
    const response = await userTrackingApi.getTrackingDetail(
      trackingNumber.value.trim(),
      selectedCarrier.value || undefined,
    )

    if (response.data.code === 1) {
      trackingDetail.value = response.data.data
      orderTrackingList.value = []
      ElMessage.success('查询成功')
    } else {
      ElMessage.error(response.data.msg || '查询失败')
      trackingDetail.value = null
    }
  } catch (error) {
    console.error('查询物流详情失败:', error)
    ElMessage.error('查询失败，请稍后重试')
    trackingDetail.value = null
  } finally {
    loading.value = false
  }
}

// 根据订单ID查询
const searchByOrder = async () => {
  if (!orderId.value) {
    ElMessage.warning('请输入订单ID')
    return
  }

  loading.value = true
  try {
    const response = await userTrackingApi.getOrderTracking(Number(orderId.value))

    if (response.data.code === 1) {
      orderTrackingList.value = response.data.data || []
      trackingDetail.value = null
      if (orderTrackingList.value.length === 0) {
        ElMessage.info('该订单暂无物流信息')
      } else {
        ElMessage.success('查询成功')
      }
    } else {
      ElMessage.error(response.data.msg || '查询失败')
      orderTrackingList.value = []
    }
  } catch (error) {
    console.error('查询订单物流失败:', error)
    ElMessage.error('查询失败，请稍后重试')
    orderTrackingList.value = []
  } finally {
    loading.value = false
  }
}

// 选择物流记录
const selectTracking = (tracking: TrackingDetailVO) => {
  trackingDetail.value = tracking
  orderTrackingList.value = []
}

// 获取运输商列表
const fetchCarriers = async () => {
  try {
    const response = await userTrackingApi.getCarriers()
    if (response.data.code === 1) {
      carriers.value = response.data.data || []
    }
  } catch (error) {
    console.error('获取运输商列表失败:', error)
  }
}

// 页面初始化
onMounted(() => {
  fetchCarriers()

  // 如果URL中有参数，自动查询
  const urlOrderId = route.query.orderId
  const urlTrackingNumber = route.query.trackingNumber

  if (urlOrderId) {
    orderId.value = Number(urlOrderId)
    searchByOrder()
  } else if (urlTrackingNumber) {
    trackingNumber.value = String(urlTrackingNumber)
    searchByTracking()
  }
})
</script>

<style scoped>
.user-tracking-page {
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.background-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.bg-circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -150px;
}

.bg-circle-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -100px;
}

.bg-circle-3 {
  width: 150px;
  height: 150px;
  top: 50%;
  left: 10%;
  transform: translateY(-50%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.header h1 {
  font-size: 32px;
  margin-bottom: 10px;
  font-weight: 600;
}

.header p {
  font-size: 18px;
  opacity: 0.9;
}

.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 24px;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
}

.card h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 18px;
}

/* 搜索表单样式 */
.search-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  align-items: end;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-input,
.form-select {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s;
  margin-right: 10px;
}

.search-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.search-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.search-btn.secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* 物流详情样式 */
.detail-header {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.detail-info,
.detail-times {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item,
.time-item {
  display: flex;
  align-items: center;
}

.info-item .label,
.time-item .label {
  min-width: 100px;
  font-weight: 500;
  color: #666;
}

.info-item .value,
.time-item .value {
  color: #333;
  font-weight: 500;
}

.status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-delivered {
  background: #d4edda;
  color: #155724;
}

.status-in_transit {
  background: #d1ecf1;
  color: #0c5460;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-exception {
  background: #f8d7da;
  color: #721c24;
}

/* 物流轨迹样式 */
.tracking-timeline {
  margin-top: 20px;
}

.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #667eea;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-dot {
  position: absolute;
  left: -25px;
  top: 5px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #667eea;
  border: 3px solid white;
  box-shadow: 0 0 0 2px #667eea;
}

.timeline-content {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-left: 3px solid #667eea;
}

.event-time {
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
  margin-bottom: 5px;
}

.event-status {
  font-weight: 500;
  color: #333;
  margin-bottom: 3px;
}

.event-location {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.event-description {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
}

/* 订单物流列表样式 */
.tracking-list {
  display: grid;
  gap: 15px;
}

.tracking-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px solid transparent;
}

.tracking-item:hover {
  background: white;
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.tracking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.tracking-number {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.tracking-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.tracking-info {
  display: grid;
  gap: 8px;
}

.info-row {
  display: flex;
  align-items: center;
}

.info-row .label {
  min-width: 80px;
  font-size: 14px;
  color: #666;
}

.info-row .value {
  font-size: 14px;
  color: #333;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 30px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state p {
  font-size: 16px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 10px;
  }

  .card {
    padding: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .detail-header {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .search-btn {
    margin-bottom: 10px;
    margin-right: 0;
  }
}
</style>
