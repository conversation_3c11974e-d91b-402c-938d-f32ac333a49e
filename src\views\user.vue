<template>
  <div class="min-h-screen bg-gray-100">
    <!-- 顶部信息栏 -->
    <div class="bg-white shadow">
      <div class="container mx-auto px-4 py-6">
        <div class="flex items-center space-x-4">
          <div
            class="w-20 h-20 rounded-full bg-purple-600 flex items-center justify-center text-white text-2xl font-bold"
          >
            {{ userInfo.accountName?.[0]?.toUpperCase() || 'U' }}
          </div>
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ userInfo.accountName || '用户' }}</h1>
            <p class="text-gray-600">上次登录时间：{{ userInfo.lastLoginTime || '未知' }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="container mx-auto px-4 py-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <!-- 左侧导航 -->
        <div class="bg-white rounded-lg shadow p-4">
          <nav class="space-y-2">
            <button
              v-for="(item, index) in navItems"
              :key="index"
              @click="activeNav = item.key"
              :class="[
                'w-full px-4 py-2 text-left rounded-lg transition-colors',
                activeNav === item.key
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-600 hover:bg-purple-50',
              ]"
            >
              <div class="flex items-center space-x-2">
                <component :is="item.icon" class="w-5 h-5" />
                <span>{{ item.label }}</span>
              </div>
            </button>
          </nav>
        </div>

        <!-- 右侧内容 -->
        <div class="md:col-span-3 bg-white rounded-lg shadow p-6">
          <!-- 基本信息 -->
          <div v-if="activeNav === 'profile'" class="space-y-6">
            <h2 class="text-xl font-bold text-gray-900">基本信息</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div v-for="(field, key) in userFields" :key="key" class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">{{ field.label }}</label>
                <input
                  v-if="!field.readonly"
                  v-model="userInfo[key]"
                  :type="field.type"
                  class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
                <p v-else class="px-4 py-2 bg-gray-50 rounded-lg text-gray-700">
                  {{ userInfo[key] || '未设置' }}
                </p>
              </div>
            </div>
            <div class="flex justify-end">
              <el-button type="primary" @click="handleSaveProfile">保存修改</el-button>
            </div>
          </div>

          <!-- 订单列表 -->
          <div v-if="activeNav === 'orders'" class="space-y-6">
            <h2 class="text-xl font-bold text-gray-900">我的订单</h2>
            <div v-if="orders.length > 0" class="space-y-4">
              <div
                v-for="order in orders"
                :key="order.id"
                class="border rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div class="flex justify-between items-center">
                  <span class="text-gray-600">订单号：{{ order.id }}</span>
                  <span class="text-purple-600 font-medium">{{ order.status }}</span>
                </div>
                <div class="mt-2 text-sm text-gray-500">
                  <p>创建时间：{{ order.createTime }}</p>
                  <p>总金额：¥{{ order.totalAmount }}</p>
                </div>
                <div class="mt-4 flex justify-end space-x-2">
                  <el-button size="small" @click="viewOrderDetail(order.id)">查看详情</el-button>
                  <el-button
                    v-if="order.status === '待付款'"
                    type="primary"
                    size="small"
                    @click="handlePayment(order.id)"
                  >
                    立即付款
                  </el-button>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8 text-gray-500">暂无订单</div>
          </div>

          <!-- 收货地址 -->
          <div v-if="activeNav === 'address'" class="space-y-6">
            <div class="flex justify-between items-center">
              <h2 class="text-xl font-bold text-gray-900">收货地址</h2>
              <el-button type="primary" @click="showAddressDialog = true">添加新地址</el-button>
            </div>
            <div v-if="addresses.length > 0" class="space-y-4">
              <div
                v-for="address in addresses"
                :key="address.id"
                class="border rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div class="flex justify-between">
                  <div>
                    <p class="font-medium">{{ address.name }} {{ address.phone }}</p>
                    <p class="text-gray-600 mt-1">
                      {{ address.province }}{{ address.city }}{{ address.detail }}
                    </p>
                  </div>
                  <div class="flex items-start space-x-2">
                    <el-button size="small" @click="editAddress(address)">编辑</el-button>
                    <el-button size="small" type="danger" @click="deleteAddress(address.id)">
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8 text-gray-500">暂无收货地址</div>
          </div>

          <!-- 账户安全 -->
          <div v-if="activeNav === 'security'" class="space-y-6">
            <h2 class="text-xl font-bold text-gray-900">账户安全</h2>
            <div class="space-y-4">
              <div class="flex justify-between items-center p-4 border rounded-lg">
                <div>
                  <h3 class="font-medium">登录密码</h3>
                  <p class="text-sm text-gray-500 mt-1">建议定期更换密码，确保账户安全</p>
                </div>
                <el-button @click="showPasswordDialog = true">修改密码</el-button>
              </div>
              <div class="flex justify-between items-center p-4 border rounded-lg">
                <div>
                  <h3 class="font-medium">手机绑定</h3>
                  <p class="text-sm text-gray-500 mt-1">已绑定：{{ userInfo.phone || '未绑定' }}</p>
                </div>
                <el-button @click="showPhoneDialog = true">
                  {{ userInfo.phone ? '更换手机' : '绑定手机' }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 地址弹窗 -->
    <el-dialog v-model="showAddressDialog" title="添加收货地址" width="500px">
      <el-form ref="addressFormRef" :model="addressForm" label-width="80px">
        <el-form-item label="收货人" prop="name">
          <el-input v-model="addressForm.name" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="addressForm.phone" />
        </el-form-item>
        <el-form-item label="所在地区">
          <div class="flex space-x-2">
            <el-input v-model="addressForm.province" placeholder="省" />
            <el-input v-model="addressForm.city" placeholder="市" />
          </div>
        </el-form-item>
        <el-form-item label="详细地址" prop="detail">
          <el-input v-model="addressForm.detail" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end space-x-2">
          <el-button @click="showAddressDialog = false">取消</el-button>
          <el-button type="primary" @click="saveAddress">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  User,
  Package,
  MapPin,
  Shield,
  Settings,
  Heart,
  MessageSquare,
  HelpCircle,
} from 'lucide-vue-next'

const router = useRouter()
const activeNav = ref('profile')

// 导航项
const navItems = [
  { key: 'profile', label: '基本信息', icon: User },
  { key: 'orders', label: '我的订单', icon: Package },
  { key: 'address', label: '收货地址', icon: MapPin },
  { key: 'security', label: '账户安全', icon: Shield },
]

// 用户信息
const userInfo = ref({
  accountName: '',
  email: '',
  phone: '',
  gender: '',
  lastLoginTime: '',
})

// 用户字段配置
const userFields = {
  accountName: { label: '用户名', type: 'text', readonly: true },
  email: { label: '邮箱', type: 'email' },
  phone: { label: '手机号', type: 'tel' },
  gender: { label: '性别', type: 'text' },
}

// 订单列表
const orders = ref([])

// 地址列表
const addresses = ref([])
const showAddressDialog = ref(false)
const addressForm = ref({
  name: '',
  phone: '',
  province: '',
  city: '',
  detail: '',
})

// 获取用户信息
const getUserInfo = async () => {
  try {
    // 从localStorage获取用户信息
    const userStr = localStorage.getItem('user')
    if (userStr) {
      userInfo.value = JSON.parse(userStr)
    }
    // TODO: 调用后端API获取完整用户信息
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  }
}

// 保存用户信息
const handleSaveProfile = async () => {
  try {
    // TODO: 调用后端API保存用户信息
    ElMessage.success('保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 查看订单详情
const viewOrderDetail = (orderId: string) => {
  router.push(`/order/detail/${orderId}`)
}

// 处理支付
const handlePayment = (orderId: string) => {
  router.push(`/payment/${orderId}`)
}

// 保存地址
const saveAddress = async () => {
  try {
    // TODO: 调用后端API保存地址
    showAddressDialog.value = false
    ElMessage.success('保存成功')
  } catch (error) {
    console.error('保存地址失败:', error)
    ElMessage.error('保存失败')
  }
}

// 编辑地址
const editAddress = (address: any) => {
  addressForm.value = { ...address }
  showAddressDialog.value = true
}

// 删除地址
const deleteAddress = async (id: string) => {
  try {
    // TODO: 调用后端API删除地址
    ElMessage.success('删除成功')
  } catch (error) {
    console.error('删除地址失败:', error)
    ElMessage.error('删除失败')
  }
}

onMounted(() => {
  getUserInfo()
})
</script>

<style scoped>
.el-dialog {
  border-radius: 0.5rem;
}
</style>
