# 依赖目录
node_modules/
bower_components/

# 构建输出
dist/
build/
.output/
.nuxt/
.vite/
.cache/

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 编辑器目录和文件
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 操作系统文件
.DS_Store
Thumbs.db

# 环境变量和敏感信息
.env
.env.*
!.env.example

# 测试文件
coverage/
__tests__/fixtures/
test/fixtures/

# 静态资源文件
*.gif
*.webp
*.mp3
*.mp4
*.webm
*.woff
*.woff2
*.ttf
*.eot

# 缓存文件
.eslintcache
.stylelintcache
.sass-cache/


# 大型数据文件
*.min.js
*.min.css
**/*.map

# 特定框架文件
.firebase/
.firebaserc
firebase.json 