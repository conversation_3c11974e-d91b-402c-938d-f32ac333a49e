import request from '@/utils/request'
import type { ApiResponse, PageResult } from './types'

export enum PaymentMethod {
  WECHAT = 1,
  ALIPAY = 2
}

export enum TransactionStatus {
  PENDING = 0,
  SUCCESS = 1,
  FAILED = 2
}

interface PaymentRecordVO {
  id: number
  orderId: number
  paymentMethod: PaymentMethod
  amount: number
  transactionStatus: TransactionStatus
  transactionTime: string
}

export const paymentApi = {
  // 获取支付记录
  getPaymentRecord(id: number) {
    return request.get<ApiResponse<PaymentRecordVO>>(`/payment/record/${id}`)
  },

  // 查询所有支付记录
  getPaymentRecords(params?: { page?: number; pageSize?: number }) {
    return request.get<ApiResponse<PageResult<PaymentRecordVO>>>('/payment/record/page', {
      params
    })
  }
}
