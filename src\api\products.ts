import request from '@/utils/request'
import type { ApiResponse, PageResult } from './types'

// 类型定义
interface ProductPageQueryDTO {
  page: number
  pageSize: number
  productName?: string
  categoryId?: number
  productStatus: 0 | 1 // 0表示禁用 1表示启用
}

// 补充产品状态枚举
export enum ProductStatus {
  DISABLED = 0,
  ENABLED = 1
}

interface ProductVO {
  id: number
  productNumber: string
  storeId: number
  productName: string
  price: number
  productIntroduction: string
  productImage: string
  productVideo: string
  productStatus: ProductStatus
  categoryId: number
  type?: string
  createTime?: string
  updateTime?: string
}

interface CategoryQueryParams {
  categoryId: number
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 产品相关接口
export const productApi = {
  /**
   * 获取所有产品（分页）
   */
  getProducts(params: ProductPageQueryDTO) {
    return request.get<ApiResponse<PageResult<ProductVO>>>('/products/page', {
      params
    })
  },

  /**
   * 根据类别查询产品
   */
  getProductsByCategory(params: CategoryQueryParams) {
    return request.get<ApiResponse<PageResult<ProductVO>>>(`/products/category/${params.categoryId}`, {
      params: {
        page: params.page,
        pageSize: params.pageSize,
        sortBy: params.sortBy,
        sortOrder: params.sortOrder
      }
    })
  },

  /**
   * 根据关键词搜索产品
   */
  searchProducts(params: { keyword: string; pageNum?: number; pageSize?: number }) {
    return request.get<ApiResponse<PageResult<ProductVO>>>('/products/search', {
      params: {
        keyword: params.keyword,
        page: params.pageNum || 1,
        pageSize: params.pageSize || 20
      }
    })
  },

  /**
   * 根据ID获取产品详情
   */
  getProductDetail(id: number) {
    return request.get<ApiResponse<ProductVO>>(`/products/${id}`)
  }
}

export type {
  ProductPageQueryDTO,
  ProductVO,
  CategoryQueryParams
}
